"""
DD-C数据更新API路由

基于DD-B更新API的完全复刻版本，适配SDR字段处理。
"""

import logging
import time
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

# 数据库依赖
from service import get_client

# 请求和响应模型
from ..models.request_models import DDCUpdateRequest
from ..models.response_models import DDCUpdateResponse, DDCUpdateDataItem

# DD-C更新处理器
from modules.dd_submission.dd_c_update import DDCUpdateProcessor, DDCUpdateRequest

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/dd-c",
    tags=["DD-C数据更新"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


@router.post("/backfill", response_model=List[Dict])
async def update_dd_c_data(
    request: DDCUpdateRequest,
    background_tasks: BackgroundTasks
)-> List[Dict]:
    """
    DD-C数据更新接口
    
    基于DD-B更新接口的完全复刻版本，处理SDR字段更新。
    
    Args:
        request: DD-C更新请求
        background_tasks: 后台任务
        rdb_client: 关系数据库客户端
        vdb_client: 向量数据库客户端  
        embedding_client: 嵌入模型客户端
        
    Returns:
        更新后的数据项列表（直接数组格式）
    """
    start_time = time.time()
    
    try:
        logger.info(f"🚀 收到DD-C更新请求: report_code={request.report_code}, dept_id={request.dept_id}, 数据量={len(request.data)}")
        
        # 1. 参数验证
        if not request.data:
            raise HTTPException(status_code=400, detail="data不能为空")
        
        # 验证每个数据项都包含entry_id
        for i, item in enumerate(request.data):
            if not item.get('entry_id'):
                raise HTTPException(status_code=400, detail=f"data[{i}]缺少entry_id字段")
        
        # 2. 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql") 
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")


        # 3. 创建DD-C更新处理器
        processor = DDCUpdateProcessor(
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            max_workers=5
        )
        
        # 3. 构建更新请求
        update_request = DDCUpdateRequest(
            report_code=request.report_code,
            dept_id=request.dept_id,
            data=request.data
        )
        
        # 4. 执行更新处理
        batch_result = await processor.process_update_request(update_request)
        
        # 5. 构建响应数据（直接数组格式）
        response_data = []
        
        for result in batch_result.results:
            # 构建数据项字典
            data_dict = {
                "entry_id": result.entry_id,
                "entry_type": result.entry_type
            }

             # 只添加有实际值的BDR字段到响应中
            # 只添加有实际值的SDR字段到响应中
            if result.updated_fields:
                import json
                for field_name, field_value in result.updated_fields.items():
                    # 只处理SDR字段
                    if field_name.upper().startswith('SDR'):
                        # 过滤掉null
                        if field_value is not None:

                            # 将字段名转换为大写格式
                            field_name_upper = field_name.upper()

                            # JSON序列化复杂数据类型
                            if isinstance(field_value, (dict, list)):
                                data_dict[field_name_upper] = json.dumps(field_value, ensure_ascii=False)
                            else:
                                data_dict[field_name_upper] = str(field_value)


            response_data.append(data_dict)
        
        execution_time = time.time() - start_time
        
        logger.info(f"✅ DD-C更新请求处理完成: 总数={batch_result.total_count}, 成功={batch_result.success_count}, 失败={batch_result.failed_count}, 耗时={execution_time:.2f}s")
        
        # 记录失败的项目
        if batch_result.failed_count > 0:
            failed_entries = [r.entry_id for r in batch_result.results if not r.success]
            logger.warning(f"⚠️ DD-C更新失败的项目: {failed_entries}")
        
        # 返回直接数组格式（符合前端要求）
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"DD-C更新请求处理失败: {str(e)}"
        logger.error(f"❌ {error_msg}, 耗时={execution_time:.2f}s", exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)


@router.post("/backfill/preview")
async def preview_dd_c_update(
    request: DDCUpdateRequest
):
    """
    DD-C数据更新预览接口
    
    预览更新操作，不实际执行数据库更新。
    
    Args:
        request: DD-C更新请求
        rdb_client: 关系数据库客户端
        
    Returns:
        预览结果
    """
    try:
        logger.info(f"🔍 DD-C更新预览: report_code={request.report_code}, dept_id={request.dept_id}, 数据量={len(request.data)}")
        
        # 简单的预览逻辑
        preview_data = []
        for item in request.data:
            entry_id = item.get('entry_id')
            update_fields = {k: v for k, v in item.items() if k != 'entry_id'}
            
            preview_item = {
                "entry_id": entry_id,
                "entry_type": item.get('entry_type', 'ITEM'),
                "update_fields": update_fields,
                "estimated_strategy": "SIMPLE_UPDATE"  # 简化预览
            }
            
            # 检测可能的策略
            if any(field.upper().startswith('SDR') for field in update_fields.keys()):
                if 'SDR08' in [f.upper() for f in update_fields.keys()]:
                    preview_item["estimated_strategy"] = "COLUMN_SELECTION"
                elif 'SDR10' in [f.upper() for f in update_fields.keys()]:
                    preview_item["estimated_strategy"] = "SQL_GENERATION"
            
            preview_data.append(preview_item)
        
        return {
            "success": True,
            "message": "DD-C更新预览完成",
            "data": {
                "total_items": len(preview_data),
                "preview_items": preview_data
            }
        }
        
    except Exception as e:
        logger.error(f"❌ DD-C更新预览失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")


@router.get("/backfill/stats")
async def get_dd_c_update_stats(
    report_code: str,
    dept_id: str
):
    """
    获取DD-C更新统计信息
    
    Args:
        report_code: 报表代码
        dept_id: 部门ID
        rdb_client: 关系数据库客户端
        
    Returns:
        统计信息
    """
    try:
        logger.info(f"📊 获取DD-C更新统计: report_code={report_code}, dept_id={dept_id}")
        
        # 简化的统计逻辑
        stats = {
            "report_code": report_code,
            "dept_id": dept_id,
            "total_records": 0,
            "sdr_fields_count": {
                "SDR05": 0, "SDR06": 0, "SDR08": 0, "SDR09": 0, "SDR10": 0
            },
            "last_update_time": None
        }
        
        return {
            "success": True,
            "message": "DD-C统计信息获取成功",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"❌ 获取DD-C统计信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/backfill/health")
async def dd_c_update_health_check():
    """
    DD-C更新服务健康检查
    
    Returns:
        健康状态
    """
    return {
        "status": "healthy",
        "service": "dd-c-update",
        "timestamp": time.time(),
        "message": "DD-C数据更新服务运行正常"
    }
