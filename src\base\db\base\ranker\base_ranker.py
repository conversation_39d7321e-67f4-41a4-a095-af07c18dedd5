"""
基础排序器抽象类

定义了所有排序器必须实现的接口，确保不同排序算法的一致性。

作者: HSBC Knowledge Team
日期: 2025-01-14
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseRanker(ABC):
    """
    基础排序器抽象类
    
    所有排序器都必须继承此类并实现rank方法
    """
    
    def __init__(self, name: str = "BaseRanker"):
        """
        初始化排序器
        
        Args:
            name: 排序器名称，用于日志和调试
        """
        self.name = name
        
    @abstractmethod
    def rank(self, 
             results_list: List[List[Dict[str, Any]]], 
             top_k: Optional[int] = None,
             **kwargs) -> List[Dict[str, Any]]:
        """
        对多个搜索结果进行重排序
        
        Args:
            results_list: 多个搜索结果列表，每个元素是一个搜索结果列表
                         格式: [
                             [{"id": 1, "distance": 0.1, "entity": {...}}, ...],  # 第一个搜索的结果
                             [{"id": 2, "distance": 0.2, "entity": {...}}, ...],  # 第二个搜索的结果
                             ...
                         ]
            top_k: 最终返回的结果数量，如果为None则返回所有结果
            **kwargs: 额外参数，由具体实现定义
            
        Returns:
            重排序后的结果列表，格式与输入结果保持一致
            [{"id": 1, "distance": 0.1, "entity": {...}, "rank_score": 0.95}, ...]
        """
        pass
    
    def validate_results(self, results_list: List[List[Dict[str, Any]]]) -> None:
        """
        验证输入结果的格式
        
        Args:
            results_list: 待验证的结果列表
            
        Raises:
            ValueError: 如果结果格式不正确
        """
        if not isinstance(results_list, list):
            raise ValueError("results_list必须是列表类型")
            
        if len(results_list) == 0:
            raise ValueError("results_list不能为空")
            
        for i, results in enumerate(results_list):
            if not isinstance(results, list):
                raise ValueError(f"results_list[{i}]必须是列表类型")
                
            for j, result in enumerate(results):
                if not isinstance(result, dict):
                    raise ValueError(f"results_list[{i}][{j}]必须是字典类型")
                    
                # 检查必需字段
                if 'id' not in result:
                    raise ValueError(f"results_list[{i}][{j}]缺少'id'字段")
                    
                if 'distance' not in result and 'score' not in result:
                    raise ValueError(f"results_list[{i}][{j}]必须包含'distance'或'score'字段")
    
    def normalize_scores(self, results: List[Dict[str, Any]], 
                        score_field: str = 'distance',
                        reverse: bool = False) -> List[Dict[str, Any]]:
        """
        归一化分数到[0,1]范围
        
        Args:
            results: 结果列表
            score_field: 分数字段名
            reverse: 是否反转分数（distance需要反转，score不需要）
            
        Returns:
            归一化后的结果列表
        """
        if not results:
            return results
            
        scores = [result.get(score_field, 0.0) for result in results]
        
        if not scores:
            return results
            
        min_score = min(scores)
        max_score = max(scores)
        
        # 避免除零
        if max_score == min_score:
            normalized_scores = [0.5] * len(scores)
        else:
            if reverse:
                # 对于distance，越小越好，需要反转
                normalized_scores = [(max_score - score) / (max_score - min_score) for score in scores]
            else:
                # 对于score，越大越好
                normalized_scores = [(score - min_score) / (max_score - min_score) for score in scores]
        
        # 更新结果
        for result, norm_score in zip(results, normalized_scores):
            result['normalized_score'] = norm_score
            
        return results
    
    def get_score(self, result: Dict[str, Any]) -> float:
        """
        从结果中提取分数（用于排序）

        Args:
            result: 单个搜索结果

        Returns:
            相似度分数（越大越好），用于降序排序
        """
        # 🔧 修复：优先使用相似度分数（越大越好），而不是距离（越小越好）
        if 'score' in result and result['score'] is not None:
            return float(result['score'])
        elif 'distance' in result and result['distance'] is not None:
            # 将距离转换为相似度分数：score = 1 - distance
            distance = float(result['distance'])
            return max(0.0, min(1.0, 1.0 - distance))
        else:
            return 0.0
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name={self.name})"
    
    def __repr__(self) -> str:
        return self.__str__()
