#!/usr/bin/env python3
"""
DD-B核心处理器测试

专注于测试核心逻辑，输出最终的list[dict]格式数据用于调试：
1. 简化的输入参数（只有report_code和dept_id）
2. 完整的数据流可视化
3. 详细的调试信息输出
4. 批量处理性能测试
"""

import asyncio
import os
import sys
import time
import json
from typing import Dict, Any

# 设置项目根目录
project_root = os.getcwd()
sys.path.insert(0, project_root)

# 设置日志
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="dd_b_core_test",
    level="INFO"
)


async def get_database_clients():
    """获取数据库客户端"""
    try:
        from service import get_client

        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        logger.info("✅ RDB客户端获取成功")

        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None

        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            logger.info("✅ VDB客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ VDB客户端获取失败: {e}")

        try:
            embedding_client = await get_client('model.embeddings.moka-m3e-base')
            logger.info("✅ Embedding客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ Embedding客户端获取失败: {e}")

        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }

    except Exception as e:
        logger.error(f"❌ 数据库客户端获取失败: {e}")
        raise


async def test_dd_b_core_processor():
    """测试DD-B核心处理器"""
    print("\n🚀 开始DD-B核心处理器测试")
    print("=" * 60)
    
    try:
        # 1. 获取数据库客户端
        logger.info("1️⃣ 获取数据库客户端...")
        clients = await get_database_clients()
        
        # 2. 导入核心处理器
        logger.info("2️⃣ 导入核心处理器...")
        from modules.dd_submission.dd_b.dd_b_core_processor import (
            process_dd_b_core,
            DDBCoreProcessor,
            CoreProcessResult
        )
        
        # 3. 设置测试参数
        logger.info("3️⃣ 设置测试参数...")
        test_cases = [
            {
                "name": "标准测试用例",
                "report_code": "G53_3_ADS_RELEASE_V0",
                "dept_id": "30239"
            }
        ]
        
        # 4. 执行测试
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print(f"  report_code: {test_case['report_code']}")
            print(f"  dept_id: {test_case['dept_id']}")
            print("-" * 50)
            
            # 执行核心处理
            start_time = time.time()
            
            result = await process_dd_b_core(
                rdb_client=clients['rdb_client'],
                report_code=test_case['report_code'],
                dept_id=test_case['dept_id'],
                vdb_client=clients['vdb_client'],
                embedding_client=clients['embedding_client'],
                batch_size=300
            )
            
            processing_time = time.time() - start_time
            
            # 输出测试结果
            await display_test_results(result, processing_time)
            
            # 保存调试数据
            await save_debug_data(result, test_case)
        
        return True
        
    except Exception as e:
        logger.error(f"核心处理器测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")
        return False


async def display_test_results(result: Any, processing_time: float):
    """显示测试结果"""
    print(f"\n📊 处理结果:")
    print(f"  状态: {'✅ 成功' if result.success else '❌ 失败'}")
    print(f"  总记录数: {result.total_records}")
    print(f"  处理记录数: {result.processed_records}")
    print(f"  处理耗时: {result.processing_time:.2f}s")
    print(f"  实际耗时: {processing_time:.2f}s")
    
    if result.total_records > 0:
        success_rate = result.processed_records / result.total_records * 100
        avg_time_ms = result.processing_time * 1000 / result.total_records
        print(f"  成功率: {success_rate:.1f}%")
        print(f"  平均每条: {avg_time_ms:.1f}ms")
    
    # 显示调试信息
    if result.debug_info:
        print(f"\n🔍 调试信息:")
        for key, value in result.debug_info.items():
            print(f"  {key}: {value}")
    
    # 显示错误信息
    if result.errors:
        print(f"\n⚠️ 错误信息:")
        for i, error in enumerate(result.errors[:3], 1):
            print(f"  {i}. {error}")
        if len(result.errors) > 3:
            print(f"  ... 还有{len(result.errors) - 3}个错误")
    
    # 显示最终数据样例
    if result.final_data:
        print(f"\n📋 最终数据样例 (前3条):")
        for i, record in enumerate(result.final_data[:3], 1):
            print(f"  记录 {i}:")
            print(f"    record_id: {record.get('record_id')}")
            print(f"    dept_id: {record.get('dept_id')}")
            print(f"    version: {record.get('version')}")
            print(f"    submission_type: {record.get('submission_type')}")
            print(f"    processing_status: {record.get('processing_status')}")
            
            # 显示关键字段
            key_fields = ['bdr09', 'bdr10', 'bdr11', 'sdr09', 'sdr10']
            for field in key_fields:
                value = record.get(field, '')
                display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {field}: {display_value}")
            print()
        
        if len(result.final_data) > 3:
            print(f"  ... 还有{len(result.final_data) - 3}条记录")
    else:
        print(f"\n❌ 未获取到最终数据")


async def save_debug_data(result: Any, test_case: Dict[str, str]):
    """保存调试数据到文件"""
    try:
        # 创建调试数据目录
        debug_dir = "debug_output"
        os.makedirs(debug_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = int(time.time())
        filename = f"dd_b_core_debug_{test_case['report_code']}_{test_case['dept_id']}_{timestamp}.json"
        filepath = os.path.join(debug_dir, filename)
        
        # 准备调试数据
        debug_data = {
            "test_case": test_case,
            "result_summary": {
                "success": result.success,
                "total_records": result.total_records,
                "processed_records": result.processed_records,
                "processing_time": result.processing_time,
                "debug_info": result.debug_info,
                "errors": result.errors
            },
            "final_data": result.final_data
        }
        
        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 调试数据已保存: {filepath}")
        print(f"  文件大小: {os.path.getsize(filepath)} bytes")
        print(f"  记录数: {len(result.final_data)}")
        
    except Exception as e:
        logger.error(f"保存调试数据失败: {e}")


async def test_batch_performance():
    """测试批量处理性能"""
    print("\n⚡ 批量处理性能测试")
    print("=" * 40)
    
    try:
        clients = await get_database_clients()
        
        # 测试不同批量大小
        batch_sizes = [100]
        test_case = {
            "report_code": "S71_ADS_RELEASE_V0",
            "dept_id": "30239"
        }
        
        print(f"📋 性能测试参数:")
        print(f"  report_code: {test_case['report_code']}")
        print(f"  dept_id: {test_case['dept_id']}")
        print(f"  测试批量大小: {batch_sizes}")
        
        performance_results = []
        
        for batch_size in batch_sizes:
            print(f"\n🔄 测试批量大小: {batch_size}")
            
            start_time = time.time()
            
            from modules.dd_submission.dd_b.dd_b_core_processor import process_dd_b_core
            
            result = await process_dd_b_core(
                rdb_client=clients['rdb_client'],
                report_code=test_case['report_code'],
                dept_id=test_case['dept_id'],
                vdb_client=clients['vdb_client'],
                embedding_client=clients['embedding_client'],
                batch_size=batch_size
            )
            
            total_time = time.time() - start_time
            
            performance_results.append({
                'batch_size': batch_size,
                'total_time': total_time,
                'processing_time': result.processing_time,
                'total_records': result.total_records,
                'processed_records': result.processed_records,
                'success_rate': result.processed_records / result.total_records * 100 if result.total_records > 0 else 0
            })
            
            print(f"  耗时: {total_time:.2f}s")
            print(f"  成功率: {performance_results[-1]['success_rate']:.1f}%")
        
        # 显示性能对比
        print(f"\n📊 性能对比:")
        print(f"{'批量大小':<10} {'总耗时(s)':<12} {'处理耗时(s)':<14} {'记录数':<8} {'成功率(%)':<10}")
        print("-" * 60)
        
        for perf in performance_results:
            print(f"{perf['batch_size']:<10} {perf['total_time']:<12.2f} {perf['processing_time']:<14.2f} "
                  f"{perf['processed_records']:<8} {perf['success_rate']:<10.1f}")
        
        # 推荐最佳批量大小
        best_perf = min(performance_results, key=lambda x: x['total_time'])
        print(f"\n🏆 推荐批量大小: {best_perf['batch_size']} (耗时: {best_perf['total_time']:.2f}s)")
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")


async def test_data_flow_visualization():
    """测试数据流可视化"""
    print("\n🔍 数据流可视化测试")
    print("=" * 40)
    
    try:
        clients = await get_database_clients()
        
        from modules.dd_submission.dd_b.dd_b_core_processor import DDBCoreProcessor
        
        processor = DDBCoreProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client'],
            batch_size=300
        )
        
        # 执行处理并观察每个步骤
        print(f"📋 数据流步骤:")
        print(f"  1. 查询数据 (query_data)")
        print(f"  2. 批量向量搜索 (batch_vector_search)")
        print(f"  3. 批量处理记录 (batch_process_records)")
        print(f"  4. 字段聚合 (aggregate_fields)")
        print(f"  5. 生成最终输出 (generate_final_output)")
        
        result = await processor.process_core_logic("S71_ADS_RELEASE_V0", "30239")
        
        print(f"\n📊 数据流结果:")
        print(f"  最终成功: {'✅' if result.success else '❌'}")
        print(f"  数据流完整性: {'✅' if result.final_data else '❌'}")
        
    except Exception as e:
        logger.error(f"数据流可视化测试失败: {e}")


async def main():
    """主函数"""
    print("DD-B核心处理器完整测试")
    print("=" * 80)
    
    try:
        # 核心功能测试
        success = await test_dd_b_core_processor()
        
        if success:
            # 性能测试
            await test_batch_performance()
            
            # 数据流可视化测试
            await test_data_flow_visualization()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成!")
        
        if success:
            print("✅ DD-B核心处理器工作正常")
            print("💡 请查看debug_output目录中的调试数据文件")
        else:
            print("❌ DD-B核心处理器存在问题")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
