"""
DD-C更新数据主处理器 - 完全独立实现

整合所有核心组件，实现完整的DD-C更新流程
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field

from .ddc_field_analyzer import DDCUpdateStrategy, DDCFieldAnalysisResult
from .ddc_strategy_processor import DDCStrategyProcessor, DDCStrategyExecutionResult
from .ddc_update_crud import DDCUpdateCrud

logger = logging.getLogger(__name__)


@dataclass
class DDCUpdateRequest:
    """DD-C更新请求数据模型"""
    report_code: str  # 对应version
    dept_id: str
    data: List[Dict[str, Any]]  # 包含entry_id和修改字段的列表
    
    def __post_init__(self):
        """验证数据格式"""
        if not self.report_code or not self.dept_id:
            raise ValueError("report_code和dept_id不能为空")
        
        if not self.data or not isinstance(self.data, list):
            raise ValueError("data必须是非空列表")
        
        for item in self.data:
            if not isinstance(item, dict) or 'entry_id' not in item:
                raise ValueError("data中每个项目必须包含entry_id")


@dataclass
class DDCUpdateItem:
    """DD-C单个更新项目"""
    entry_id: str
    update_fields: Dict[str, Any] = field(default_factory=dict)
    original_data: Optional[Dict[str, Any]] = None
    analysis_result: Optional[DDCFieldAnalysisResult] = None


@dataclass
class DDCUpdateResult:
    """DD-C更新结果"""
    entry_id: str
    success: bool
    strategy: DDCUpdateStrategy
    entry_type: str = "ITEM"  # 条目类型：ITEM/TABLE
    updated_fields: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    execution_time: float = 0.0

    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        result = {
            "entry_id": self.entry_id,
            "entry_type": self.entry_type
        }

        # 添加所有更新字段
        if self.updated_fields:
            result.update(self.updated_fields)

        # 如果失败，添加错误信息
        if not self.success and self.error_message:
            result["error_message"] = self.error_message

        return result


@dataclass
class DDCBatchUpdateResult:
    """DD-C批量更新结果"""
    total_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    results: List[DDCUpdateResult] = field(default_factory=list)
    execution_time: float = 0.0
    range_aggregation_performed: bool = False

    def add_result(self, result: DDCUpdateResult):
        """添加单个结果"""
        self.results.append(result)
        self.total_count += 1
        if result.success:
            self.success_count += 1
        else:
            self.failed_count += 1

    def get_summary(self) -> str:
        """获取结果摘要"""
        return f"总数={self.total_count}, 成功={self.success_count}, 失败={self.failed_count}, 耗时={self.execution_time:.2f}s"

    def to_frontend_format(self) -> List[Dict[str, Any]]:
        """
        转换为前端格式

        Returns:
            前端需要的格式：List[Dict] 包含entry_id, entry_type和所有更新字段
        """
        return [result.to_frontend_format() for result in self.results]


class DDCUpdateProcessor:
    """DD-C更新数据主处理器 - 完全独立实现"""
    
    def __init__(
        self, 
        rdb_client: Any, 
        vdb_client: Any = None, 
        embedding_client: Any = None,
        max_workers: int = 5
    ):
        """
        初始化DD-C更新处理器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
            max_workers: 最大并发工作数
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.max_workers = max_workers
        
        # 初始化核心组件
        self.update_crud = DDCUpdateCrud(rdb_client, vdb_client, embedding_client)
        self.strategy_processor = DDCStrategyProcessor(self.update_crud)
        
        logger.debug(f"DD-C更新处理器初始化完成: max_workers={max_workers}")
    
    async def process_update_request(self, request: DDCUpdateRequest) -> DDCBatchUpdateResult:
        """
        处理更新请求
        
        Args:
            request: 更新请求
            
        Returns:
            批量更新结果
        """
        start_time = time.time()
        result = DDCBatchUpdateResult()
        
        try:
            logger.info(f"🚀 开始处理DD-C更新请求: report_code={request.report_code}, dept_id={request.dept_id}, 项目数={len(request.data)}")
            
            # 1. 解析更新项目
            update_items = self._parse_update_items(request.data)
            logger.debug(f"解析更新项目完成: {len(update_items)}个项目")
            
            # 2. 并发处理更新项目
            if update_items:
                logger.info(f"🔄 开始并发处理{len(update_items)}个更新项目...")
                
                # 使用信号量控制并发数
                semaphore = asyncio.Semaphore(self.max_workers)
                
                async def process_single_item(item: DDCUpdateItem) -> DDCUpdateResult:
                    async with semaphore:
                        return await self._process_single_update(item, request.report_code, request.dept_id)
                
                # 并发执行所有更新任务
                tasks = [process_single_item(item) for item in update_items]
                individual_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for i, individual_result in enumerate(individual_results):
                    if isinstance(individual_result, Exception):
                        logger.error(f"❌ 更新项目 {update_items[i].entry_id} 处理异常: {individual_result}")
                        result.add_result(DDCUpdateResult(
                            entry_id=update_items[i].entry_id,
                            success=False,
                            strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                            entry_type="ITEM",  # 默认ITEM类型
                            error_message=str(individual_result)
                        ))
                    else:
                        result.add_result(individual_result)
            
            # 3. 检查是否需要Range聚合
            # 检查是否有ITEM类型记录修改了轴心字段
            has_item_core_modification = False
            for item in update_items:
                if (item.original_data and
                    item.original_data.get('submission_type', 'SUBMISSION') == 'SUBMISSION'):

                    # 检查是否修改了轴心字段
                    core_fields_modified = any(
                        field.upper() in ['SDR05', 'SDR08', 'SDR10']
                        for field in item.update_fields.keys()
                    )

                    if core_fields_modified:
                        has_item_core_modification = True
                        logger.debug(f"检测到ITEM类型条目修改轴心字段: entry_id={item.entry_id}")
                        break

            if has_item_core_modification:
                logger.info("🎯 检测到ITEM类型条目修改了轴心字段，执行Range聚合...")
                range_result = await self._perform_range_aggregation(request.report_code, request.dept_id)
                result.range_aggregation_performed = True

                # 将Range聚合结果加入到batch_result中
                if range_result:
                    result.add_result(range_result)
                    logger.debug(f"Range聚合结果已加入batch_result: entry_id={range_result.entry_id}")

                logger.info("✅ Range聚合完成")
            else:
                logger.debug("无需Range聚合: 没有ITEM类型条目修改轴心字段")
            
            result.execution_time = time.time() - start_time
            logger.info(f"✅ DD-C更新请求处理完成: {result.get_summary()}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ DD-C更新请求处理失败: {e}", exc_info=True)
            result.execution_time = time.time() - start_time
            
            # 如果没有任何结果，添加一个失败结果
            if not result.results:
                result.add_result(DDCUpdateResult(
                    entry_id="unknown",
                    success=False,
                    strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                    entry_type="ITEM",  # 默认ITEM类型
                    error_message=str(e)
                ))
            
            return result
    
    def _parse_update_items(self, data: List[Dict[str, Any]]) -> List[DDCUpdateItem]:
        """
        解析更新项目
        
        Args:
            data: 原始数据列表
            
        Returns:
            更新项目列表
        """
        update_items = []
        
        for item_data in data:
            try:
                entry_id = item_data.get('entry_id', '')
                if not entry_id:
                    logger.warning(f"跳过无效项目: 缺少entry_id")
                    continue
                
                # 提取更新字段（排除entry_id）
                update_fields = {k: v for k, v in item_data.items() if k != 'entry_id'}
                
                if not update_fields:
                    logger.warning(f"跳过无效项目: entry_id={entry_id}, 无更新字段")
                    continue
                
                update_item = DDCUpdateItem(
                    entry_id=entry_id,
                    update_fields=update_fields
                )
                
                update_items.append(update_item)
                
            except Exception as e:
                logger.warning(f"解析更新项目失败: {item_data}, error={e}")
                continue
        
        return update_items
    
    async def _process_single_update(
        self,
        item: DDCUpdateItem,
        report_code: str,
        dept_id: str
    ) -> DDCUpdateResult:
        """
        处理单个更新项目

        Args:
            item: 更新项目
            report_code: 报表代码
            dept_id: 部门ID

        Returns:
            更新结果
        """
        start_time = time.time()

        try:
            logger.debug(f"🔄 处理更新项目: entry_id={item.entry_id}, fields={list(item.update_fields.keys())}")

            # 1. 获取原始数据
            original_data = await self.update_crud.get_record_by_identifiers(report_code, dept_id, item.entry_id)
            if not original_data:
                return DDCUpdateResult(
                    entry_id=item.entry_id,
                    success=False,
                    strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                    entry_type="ITEM",  # 默认ITEM类型
                    error_message=f"未找到entry_id={item.entry_id}的原始数据",
                    execution_time=time.time() - start_time
                )

            item.original_data = original_data

            # 2. 检查entry_type，只有ITEM类型才触发Pipeline
            submission_type = original_data.get('submission_type', 'SUBMISSION')
            if submission_type == 'RANGE':
                # TABLE类型记录直接更新，不走Pipeline和聚合逻辑
                logger.debug(f"检测到TABLE类型记录(submission_type=RANGE): entry_id={item.entry_id}, 直接更新")
                db_success = await self.update_crud.update_record_fields(
                    original_data['id'], item.update_fields
                )

                return DDCUpdateResult(
                    entry_id=item.entry_id,
                    success=db_success,
                    strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                    entry_type="TABLE",  # TABLE类型记录
                    updated_fields=item.update_fields if db_success else {},
                    error_message=None if db_success else "数据库更新失败",
                    execution_time=time.time() - start_time
                )

            logger.debug(f"检测到ITEM类型记录(submission_type={submission_type}): entry_id={item.entry_id}, 进入策略处理")
            
            # 2. 使用策略处理器处理更新
            strategy_result = await self.strategy_processor.process_strategy(item.update_fields, original_data)
            item.analysis_result = strategy_result.analysis_result

            logger.debug(f"策略处理结果: entry_id={item.entry_id}, strategy={strategy_result.strategy.value}, success={strategy_result.success}")

            # 3. 如果策略执行成功，更新数据库
            if strategy_result.success and strategy_result.updated_fields:
                db_success = await self.update_crud.update_record_fields(
                    original_data['id'], strategy_result.updated_fields
                )

                return DDCUpdateResult(
                    entry_id=item.entry_id,
                    success=db_success,
                    strategy=strategy_result.strategy,
                    entry_type="ITEM",  # ITEM类型记录
                    updated_fields=strategy_result.updated_fields if db_success else {},
                    error_message=None if db_success else "数据库更新失败",
                    execution_time=time.time() - start_time
                )
            else:
                # 策略执行失败
                return DDCUpdateResult(
                    entry_id=item.entry_id,
                    success=False,
                    strategy=strategy_result.strategy,
                    entry_type="ITEM",  # ITEM类型记录
                    error_message=strategy_result.error_message or "策略执行失败",
                    execution_time=time.time() - start_time
                )
            
        except Exception as e:
            logger.error(f"❌ 处理更新项目失败: entry_id={item.entry_id}, error={e}", exc_info=True)
            return DDCUpdateResult(
                entry_id=item.entry_id,
                success=False,
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                entry_type="ITEM",  # 默认ITEM类型
                error_message=str(e),
                execution_time=time.time() - start_time
            )
    
    async def _perform_range_aggregation(
        self,
        report_code: str,
        dept_id: str
    ) -> Optional['DDCUpdateResult']:
        """
        执行Range聚合（完全对齐DD-B逻辑）

        在所有ITEM类型记录处理完毕后，查询数据库中所有相关记录，
        聚合submission_type=SUBMISSION的记录，更新submission_type=RANGE的记录

        Args:
            report_code: 报表代码（对应数据库中的version字段）
            dept_id: 部门ID

        Returns:
            Range记录的更新结果，如果没有更新则返回None
        """
        try:
            logger.debug(f"开始Range聚合: report_code={report_code}, dept_id={dept_id}")

            # 1. 查询数据库中所有相关记录
            all_records = await self.update_crud.get_all_records_for_aggregation(report_code, dept_id)

            if not all_records:
                logger.debug("没有找到需要聚合的记录")
                return None

            # 2. 分离SUBMISSION和RANGE记录
            submission_records = []
            range_record = None

            for record in all_records:
                if record.get('submission_type') == 'SUBMISSION':
                    submission_records.append(record)
                elif record.get('submission_type') == 'RANGE':
                    range_record = record

            logger.debug(f"找到 {len(submission_records)} 个SUBMISSION记录, {'1' if range_record else '0'} 个RANGE记录")

            if not submission_records or not range_record:
                logger.debug("没有SUBMISSION记录或RANGE记录需要聚合")
                return None

            # 3. 使用DD-B的RangeAggregator进行聚合（完全对齐DD-B逻辑）
            from modules.dd_submission.dd_b.utils.range_aggregator import RangeAggregator
            range_aggregator = RangeAggregator()

            # 执行聚合（与DD-B完全相同的逻辑）
            aggregated_record = await range_aggregator.aggregate_range_fields(
                range_record=range_record,
                submission_list=submission_records
            )

            # 4. 更新数据库中的Range记录（只更新BDR和SDR字段）
            update_fields = {k.lower(): v for k, v in aggregated_record.items()
                           if k.lower().startswith(('bdr', 'sdr'))}

            await self.update_crud.update_range_record(range_record['id'], update_fields)
            logger.debug(f"Range记录更新完成: range_record_id={range_record['id']}")

            # 5. 创建Range记录的更新结果（与DD-B对齐）
            range_result = DDCUpdateResult(
                entry_id=range_record.get('submission_id', 'UNKNOWN'),  # 使用submission_id，不加前缀
                success=True,
                strategy="range_aggregation",
                updated_fields={
                    # 返回所有BDR和SDR字段（与DD-B完全对齐）
                    k.lower(): v for k, v in aggregated_record.items()
                    if k.lower().startswith(('bdr', 'sdr')) and v is not None
                },
                entry_type="RANGE",
                execution_time=0.0  # Range聚合时间单独计算
            )

            logger.debug(f"Range聚合结果创建: entry_id={range_result.entry_id}, fields={list(range_result.updated_fields.keys())}")
            return range_result

        except Exception as e:
            logger.error(f"Range聚合失败: {e}")
            # Range聚合失败不应该影响主流程
            return None
