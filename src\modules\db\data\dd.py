"""
G14.xlsx数据入库脚本
读取Excel文件并使用DD CRUD将数据插入到dd_submission_data表
"""

import asyncio
import pandas as pd
import sys
import os
from pathlib import Path
from datetime import datetime


# 添加项目根目录到Python路径
project_root = os.getcwd()
sys.path.insert(0, str(project_root))
from service import get_client
from modules.knowledge.dd.crud import DDCrud

from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    level="DEBUG",
)


async def load_g14_data_to_db():
    """读取G14.xlsx文件并将数据入库"""
    
    # Excel文件路径
    excel_path = r"D:\Code\shtlx\sync\hsbc-knowledge\src\modules\db\data\source\G53.xlsx"
    
    if not os.path.exists(excel_path):
        logger.error(f"Excel文件不存在: {excel_path}")
        return
    
    # 手动输入version
    version = 'G53_3_ADS_RELEASE_V0'
    # version = 'G14_1_ADS_RELEASE_V0'
    
    logger.info(f"使用version: {version}")
    
    # 初始化客户端
    rdb_client=await get_client("database.rdbs.mysql")
    vdb_client=await get_client("database.vdbs.pgvector")
    embedding_client=await get_client("model.embeddings.moka-m3e-base")
    
    
    # 创建CRUD实例
    dd_crud = DDCrud(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client
    )
    
    try:
        # 读取Excel文件
        logger.info(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        logger.info(f"Excel文件读取成功，共 {len(df)} 行数据，{len(df.columns)} 列")
        
        # 显示列名
        logger.info(f"列名: {list(df.columns)}")
        
        # 固定的knowledge_id
        knowledge_id = "80788d5f-3979-41f4-a570-7eebfb5870bb"
        
        # 在入库前先删除重复数据
        if version:
            logger.info("开始检查并删除重复数据...")
            await delete_duplicate_data(dd_crud, knowledge_id, version, df)
        
        # 准备批量数据
        batch_data = []
        
        # 遍历每一行数据，准备批量插入的数据
        for index, row in df.iterrows():
            # 将pandas Series转换为字典，并处理NaN值
            submission_data = {}
            for col in df.columns:
                value = row[col]
                # 处理NaN值，转换为None或者空字符串
                if pd.isna(value):
                    submission_data[col.lower()] = None  # 字段名转换为小写
                else:
                    submission_data[col.lower()] = str(value)  # 转换为字符串
            
            # 添加version字段
            submission_data['version'] = version
            
            batch_data.append(submission_data)
        
        logger.info(f"准备批量入库 {len(batch_data)} 条数据...")
        
        # 批量创建填报数据
        submission_pks, vector_ids = await dd_crud.create_submission_data(batch_data, knowledge_id=knowledge_id)
        
        logger.info(f"✅ 批量入库成功! 插入了 {len(submission_pks)} 条记录，向量数={len(vector_ids)}")
        logger.info(f"插入的主键ID: {submission_pks}")
        
    except Exception as e:
        logger.error(f"数据入库过程中发生错误: {e}")
        
    finally:
        # 关闭数据库连接
        pass


async def delete_duplicate_data(dd_crud: DDCrud, knowledge_id: str, version: str, df: pd.DataFrame):
    """
    删除重复数据
    
    Args:
        dd_crud: DDCrud实例
        knowledge_id: 知识库ID
        version: 版本号
        df: Excel数据DataFrame
    """
    try:
        # 查询现有的数据（按version，因为dd_submission_data表中没有knowledge_id字段）
        existing_data = await dd_crud.list_submission_data(version=version)
        logger.info(f"查询到现有数据 {len(existing_data)} 条 (version={version})")
        
        if not existing_data:
            logger.info("没有现有数据，无需删除重复数据")
            return
        
        # 获取Excel数据中的关键字段用于去重判断
        excel_key_fields = set()
        for _, row in df.iterrows():
            # 根据你的数据结构，选择用于去重的字段
            # 这里使用submission_id作为唯一标识，你可以根据实际情况修改
            if 'submission_id' in df.columns:
                key_value = str(row['submission_id']) if pd.notna(row['submission_id']) else None
                if key_value:
                    excel_key_fields.add(key_value)
            # 或者使用其他字段组合作为唯一标识
            # 例如：使用多个字段组合
            # key_parts = []
            # for field in ['field1', 'field2', 'field3']:
            #     if field in df.columns and pd.notna(row[field]):
            #         key_parts.append(str(row[field]))
            # if key_parts:
            #     excel_key_fields.add('_'.join(key_parts))
        
        logger.info(f"Excel数据中的唯一键数量: {len(excel_key_fields)}")
        
        # 找出需要删除的重复数据
        records_to_delete = []
        for existing_record in existing_data:
            # 根据相同的逻辑构建现有记录的唯一键
            if 'submission_id' in existing_record:
                existing_key = str(existing_record['submission_id']) if existing_record['submission_id'] else None
                if existing_key and existing_key in excel_key_fields:
                    records_to_delete.append(existing_record)
            # 或者使用其他字段组合判断
            # existing_key_parts = []
            # for field in ['field1', 'field2', 'field3']:
            #     if field in existing_record and existing_record[field]:
            #         existing_key_parts.append(str(existing_record[field]))
            # if existing_key_parts:
            #     existing_key = '_'.join(existing_key_parts)
            #     if existing_key in excel_key_fields:
            #         records_to_delete.append(existing_record)
        
        if records_to_delete:
            logger.info(f"发现 {len(records_to_delete)} 条重复数据需要删除")
            
            # 构建删除条件 - 使用主键ID进行删除
            delete_conditions = []
            for record in records_to_delete:
                if 'id' in record:
                    delete_conditions.append({'id': record['id']})
            
            # 批量删除重复数据（包含向量删除）
            if delete_conditions:
                logger.info(f"开始删除重复数据（包含向量）: {len(delete_conditions)} 条")

                # 由于批量删除不支持向量删除，需要逐条删除以确保向量也被删除
                success_count = 0
                for condition in delete_conditions:
                    try:
                        # 单条删除，支持向量删除
                        delete_success = await dd_crud.delete_submission_data(condition)
                        if delete_success:
                            success_count += 1
                        else:
                            logger.warning(f"删除记录失败: {condition}")
                    except Exception as e:
                        logger.error(f"删除记录时发生错误: {condition}, 错误: {e}")

                if success_count > 0:
                    logger.info(f"✅ 成功删除 {success_count}/{len(delete_conditions)} 条重复数据（包含向量）")
                else:
                    logger.warning("所有重复数据删除都失败了")
            else:
                logger.warning("没有找到有效的删除条件")
        else:
            logger.info("没有发现重复数据")
            
    except Exception as e:
        logger.error(f"删除重复数据时发生错误: {e}")


async def main():
    """主函数"""
    logger.info("开始G14数据入库任务...")
    await load_g14_data_to_db()
    logger.info("G14数据入库任务完成!")


if __name__ == "__main__":
    asyncio.run(main())
