# BaseImage
FROM python:3.12-slim

# Environment variables
ENV SERVER_PORT=30351 \
    PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy only requirements first (for better caching)
COPY pyproject.toml ./

# Upgrade pip and install basic tools
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Install dependencies step by step
RUN pip install --no-cache-dir fastapi uvicorn

# Copy the rest of the project
COPY . .

# Try to install the project
RUN pip install --no-cache-dir -e . || \
    (echo "Failed to install project, trying individual packages..." && \
     pip install --no-cache-dir $(grep -E "^\s*\"[^\"]+\"" pyproject.toml | sed 's/.*"\([^"]*\)".*/\1/' | head -20))

# Expose port
EXPOSE ${SERVER_PORT}

# Use shell form for better environment variable handling
SHELL ["/bin/bash", "-c"]

# Entry point with environment variable support
ENTRYPOINT python -u src/api/main.py
