# @package database.rdbs.mysql
# MySQL默认配置

defaults:
  - _self_
  - connection
  # Load priority configurations
  - priority/standard@priority  # 默认使用标准优先级


# 客户端目标类
_target_: base.db.implementations.rdb.sqlalchemy.universal.factory.create_mysql_client

# 数据库类型标识
db_type: "mysql"

# 基础连接池配置 - 优化为总最大连接数100
pool_size: 30           # 基础连接数：30
max_overflow: 70        # 额外连接数：70 (总计100个连接)
pool_timeout: 60.0      # 连接获取超时：60秒 (增加缓冲时间)
pool_recycle: 3600      # 连接回收时间：1小时
pool_pre_ping: true     # 连接健康检查

# 基础缓存配置
enable_cache: true
cache_size: 1000
cache_ttl: 3600

# SQLAlchemy引擎配置
echo: false
future: true

# MySQL特定配置
mysql_specific:
  charset: "utf8mb4"
  isolation_level: "READ_COMMITTED"
