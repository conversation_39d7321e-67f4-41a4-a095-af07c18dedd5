"""
DD-C Pipeline执行器 - 完全独立实现

整合字段分析器和字段映射器，实现完整的Pipeline执行流程
"""

import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .ddc_field_analyzer import DDCUpdateStrategy, DDCPipelineParams
from .ddc_field_mapper import DDCFieldMapper, DDCFieldMappingResult
from .ddc_update_crud import DDCUpdateCrud

logger = logging.getLogger(__name__)


@dataclass
class DDCPipelineExecutionResult:
    """DD-C Pipeline执行结果"""
    success: bool = False
    execution_time: float = 0.0
    strategy: Optional[DDCUpdateStrategy] = None
    pipeline_result: Optional[Dict[str, Any]] = None
    field_mapping_result: Optional[DDCFieldMappingResult] = None
    updated_fields: Dict[str, Any] = None
    error_message: str = ""


class DDCPipelineExecutor:
    """DD-C Pipeline执行器 - 完全独立实现"""
    
    def __init__(self, update_crud: DDCUpdateCrud):
        """
        初始化DD-C Pipeline执行器
        
        Args:
            update_crud: DD-C数据库操作组件
        """
        self.update_crud = update_crud
        self.field_mapper = DDCFieldMapper(update_crud)
        
        # 初始化Pipeline客户端（延迟加载）
        self._pipeline_client = None
        
        logger.debug("DD-C Pipeline执行器初始化完成")
    
    async def execute_pipeline_strategy(
        self,
        original_data: Dict[str, Any],
        strategy: DDCUpdateStrategy,
        pipeline_params: DDCPipelineParams,
        override_fields: Optional[Dict[str, Any]] = None
    ) -> DDCPipelineExecutionResult:
        """
        执行Pipeline策略
        
        Args:
            original_data: 原始记录数据
            strategy: 更新策略
            pipeline_params: Pipeline参数
            override_fields: 覆盖字段
            
        Returns:
            Pipeline执行结果
        """
        start_time = time.time()
        result = DDCPipelineExecutionResult(strategy=strategy)
        
        try:
            record_id = original_data.get('id', 0)
            logger.debug(f"开始执行DD-C Pipeline策略: record_id={record_id}, strategy={strategy.value}")
            
            # 根据策略类型执行不同的Pipeline流程
            if strategy in [DDCUpdateStrategy.SDR05_PIPELINE, DDCUpdateStrategy.SDR08_PIPELINE, DDCUpdateStrategy.SDR10_PIPELINE]:
                # 基础策略：执行完整Pipeline
                pipeline_result = await self._execute_full_pipeline(pipeline_params, original_data)
            elif strategy in [DDCUpdateStrategy.SDR05_SDR08_COMPOSITE, DDCUpdateStrategy.SDR08_SDR10_COMPOSITE, DDCUpdateStrategy.SDR05_SDR10_COMPOSITE]:
                # 组合策略：执行部分Pipeline
                pipeline_result = await self._execute_partial_pipeline(pipeline_params, original_data, strategy)
            else:
                raise ValueError(f"不支持的Pipeline策略: {strategy.value}")
            
            result.pipeline_result = pipeline_result
            
            # 执行字段映射
            field_mapping_result = await self.field_mapper.map_pipeline_result(
                pipeline_result, original_data, strategy, override_fields
            )
            result.field_mapping_result = field_mapping_result
            
            # 转换为数据库更新格式，应用保留字段覆盖
            if field_mapping_result.mapping_success:
                result.updated_fields = field_mapping_result.to_string_format(override_fields)
                result.success = True
                logger.debug(f"DD-C Pipeline策略执行成功: record_id={record_id}")
            else:
                result.error_message = "字段映射失败"
                logger.warning(f"DD-C Pipeline策略字段映射失败: record_id={record_id}")
            
        except Exception as e:
            result.error_message = f"Pipeline执行异常: {e}"
            logger.error(f"DD-C Pipeline策略执行失败: record_id={original_data.get('id', 0)}, error={e}")
        
        result.execution_time = time.time() - start_time
        return result
    
    async def _execute_full_pipeline(
        self, 
        pipeline_params: DDCPipelineParams, 
        original_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行完整Pipeline流程
        
        Args:
            pipeline_params: Pipeline参数
            original_data: 原始记录数据
            
        Returns:
            Pipeline执行结果
        """
        logger.debug("执行完整Pipeline流程")
        
        # 获取Pipeline客户端
        pipeline_client = await self._get_pipeline_client()
        
        # 构建Pipeline请求参数
        schema_params = pipeline_params.schema_generation_params.copy()

        # 根据不同策略构建不同的参数
        if pipeline_params.candidate_columns:
            # SDR08策略：有candidate_columns，确保schema_params包含它
            schema_params["candidate_columns"] = pipeline_params.candidate_columns
            request_params = {
                "candidate_columns": pipeline_params.candidate_columns,
                "schema_generation_params": schema_params,
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }
        elif "table_names" in schema_params:
            # SDR05策略：有table_names，使用字段选择Pipeline（9个步骤）
            # 需要candidate_tables参数而不是candidate_columns
            table_names = schema_params["table_names"]
            candidate_tables = {"default_db": table_names}  # 构建candidate_tables格式
            request_params = {
                "candidate_tables": candidate_tables,
                "schema_generation_params": schema_params,
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }
        elif "sql_candidates" in schema_params:
            # SDR10策略：有sql_candidates，使用SQL解析Pipeline
            request_params = {
                "sql_candidates": schema_params["sql_candidates"],
                "candidate_tables": {},  # SQL解析Pipeline的可选参数
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
                # 不传递schema_generation_params，因为execute_sql_parsing不需要
            }
        else:
            # 默认参数
            request_params = {
                "schema_generation_params": schema_params,
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }

        logger.debug(f"Pipeline请求参数: {request_params}")
        
        # 根据pipeline_type选择正确的Pipeline方法
        if pipeline_params.pipeline_type == "sql_parser":
            # SQL解析Pipeline（SDR10策略）
            logger.debug("执行SQL解析Pipeline")
            pipeline_result = await pipeline_client.execute_sql_parsing(**request_params)
        elif pipeline_params.pipeline_type == "column_selection":
            # SDR05策略：使用字段选择Pipeline（9个步骤）
            logger.debug("执行字段选择Pipeline（9个步骤）")
            pipeline_result = await pipeline_client.execute_column_selection(**request_params)
        elif pipeline_params.pipeline_type == "sql_generation":
            # SDR08策略：使用SQL生成Pipeline（5个步骤）
            logger.debug("执行SQL生成Pipeline（5个步骤）")
            pipeline_result = await pipeline_client.execute_sql_generation(**request_params)
        else:
            # 默认使用完整Pipeline
            logger.debug("执行完整Pipeline")
            pipeline_result = await pipeline_client.execute(**request_params)
        
        return self._normalize_pipeline_result(pipeline_result)
    
    async def _execute_partial_pipeline(
        self, 
        pipeline_params: DDCPipelineParams, 
        original_data: Dict[str, Any],
        strategy: DDCUpdateStrategy
    ) -> Dict[str, Any]:
        """
        执行部分Pipeline流程（用于组合策略）
        
        Args:
            pipeline_params: Pipeline参数
            original_data: 原始记录数据
            strategy: 更新策略
            
        Returns:
            Pipeline执行结果
        """
        logger.debug(f"执行部分Pipeline流程: strategy={strategy.value}")
        
        # 获取Pipeline客户端
        pipeline_client = await self._get_pipeline_client()
        
        # 根据策略执行不同的Pipeline部分 - 使用正确的方法名和参数格式
        if strategy == DDCUpdateStrategy.SDR05_SDR08_COMPOSITE:
            # SDR05+SDR08组合：执行SQL生成
            # 确保schema_generation_params包含candidate_columns
            schema_params = pipeline_params.schema_generation_params.copy()
            schema_params["candidate_columns"] = pipeline_params.candidate_columns

            request_params = {
                "candidate_columns": pipeline_params.candidate_columns,
                "schema_generation_params": schema_params,
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }
            logger.debug(f"SQL生成Pipeline请求参数: {request_params}")
            pipeline_result = await pipeline_client.execute_sql_generation(**request_params)

        elif strategy == DDCUpdateStrategy.SDR08_SDR10_COMPOSITE:
            # SDR08+SDR10组合：执行SQL解析
            # SQL解析Pipeline需要candidate_tables和sql_candidates
            schema_params = pipeline_params.schema_generation_params
            request_params = {
                "sql_candidates": schema_params.get("sql_candidates", [pipeline_params.user_question]),
                "candidate_tables": pipeline_params.candidate_columns,  # 使用candidate_columns作为candidate_tables
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }
            logger.debug(f"SQL解析Pipeline请求参数: {request_params}")
            pipeline_result = await pipeline_client.execute_sql_parsing(**request_params)

        elif strategy == DDCUpdateStrategy.SDR05_SDR10_COMPOSITE:
            # SDR05+SDR10组合：执行SQL解析
            # SQL解析Pipeline需要candidate_tables和sql_candidates
            schema_params = pipeline_params.schema_generation_params
            request_params = {
                "sql_candidates": schema_params.get("sql_candidates", [pipeline_params.user_question]),
                "candidate_tables": pipeline_params.candidate_columns,  # 使用candidate_columns作为candidate_tables
                "user_question": pipeline_params.user_question,
                "hint": pipeline_params.hint,
                "db_type": pipeline_params.db_type
            }
            logger.debug(f"SQL解析Pipeline请求参数: {request_params}")
            pipeline_result = await pipeline_client.execute_sql_parsing(**request_params)
        else:
            # 默认执行完整Pipeline
            pipeline_result = await pipeline_client.execute(**request_params)
        
        return self._normalize_pipeline_result(pipeline_result)
    
    async def _get_pipeline_client(self):
        """获取Pipeline客户端（延迟加载）"""
        if self._pipeline_client is None:
            try:
                # 导入Pipeline客户端
                from pipeline.nl2sql_pipeline import CompletePipelineExecutor
                self._pipeline_client = CompletePipelineExecutor(default_db_type="mysql")
                logger.debug("Pipeline客户端初始化成功")
            except ImportError as e:
                logger.error(f"Pipeline客户端导入失败: {e}")
                raise RuntimeError(f"Pipeline客户端不可用: {e}")
        
        return self._pipeline_client
    
    def _normalize_pipeline_result(self, pipeline_result: Any) -> Dict[str, Any]:
        """
        标准化Pipeline结果格式
        
        Args:
            pipeline_result: 原始Pipeline结果
            
        Returns:
            标准化后的结果字典
        """
        try:
            # 如果是对象，转换为字典
            if hasattr(pipeline_result, '__dict__'):
                result_dict = {}
                for key, value in pipeline_result.__dict__.items():
                    if not key.startswith('_'):  # 忽略私有属性
                        result_dict[key] = value
                return result_dict
            
            # 如果已经是字典，直接返回
            elif isinstance(pipeline_result, dict):
                return pipeline_result
            
            # 其他情况，尝试转换为字典
            else:
                return {"raw_result": pipeline_result}
                
        except Exception as e:
            logger.warning(f"Pipeline结果标准化失败: {e}")
            return {"raw_result": pipeline_result, "normalization_error": str(e)}
    
    async def execute_simple_update(
        self,
        original_data: Dict[str, Any],
        update_fields: Dict[str, Any]
    ) -> DDCPipelineExecutionResult:
        """
        执行简单更新（不需要Pipeline）
        
        Args:
            original_data: 原始记录数据
            update_fields: 更新字段
            
        Returns:
            执行结果
        """
        start_time = time.time()
        result = DDCPipelineExecutionResult(strategy=DDCUpdateStrategy.SIMPLE_UPDATE)
        
        try:
            record_id = original_data.get('id', 0)
            logger.debug(f"执行简单更新: record_id={record_id}, fields={list(update_fields.keys())}")
            
            # 简单更新不需要Pipeline，直接使用更新字段
            result.updated_fields = update_fields
            result.success = True
            
            logger.debug(f"简单更新执行成功: record_id={record_id}")
            
        except Exception as e:
            result.error_message = f"简单更新异常: {e}"
            logger.error(f"简单更新执行失败: record_id={original_data.get('id', 0)}, error={e}")
        
        result.execution_time = time.time() - start_time
        return result
