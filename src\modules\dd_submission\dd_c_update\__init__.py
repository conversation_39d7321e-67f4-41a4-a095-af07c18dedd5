"""
DD-C更新模块 - 完全独立实现

提供DD-C数据更新的完整功能，包括：
- 字段分析和策略判断
- Pipeline执行和字段映射
- 数据库操作和Range聚合
- 完整的更新流程处理

字段映射关系：
- SDR05 ↔ BDR09 (表英文名列表)
- SDR06 ↔ BDR10 (表中文名列表)
- SDR08 ↔ BDR11 (字段映射)
- SDR09: 字段中文名映射
- SDR10: SQL语句
- SDR12: JOIN条件

Author: AI Assistant
Created: 2025-07-29
Version: 2.0.0 (完全独立重构版本)
"""

__version__ = "2.0.0"
__author__ = "AI Assistant"

# 导出主要组件
from .core.ddc_update_processor import (
    DDCUpdateProcessor,
    DDCUpdateRequest,
    DDCUpdateItem,
    DDCUpdateResult,
    DDCBatchUpdateResult
)

from .core.ddc_field_analyzer import (
    DDCFieldAnalyzer,
    DDCUpdateStrategy,
    DDCFieldAnalysisResult,
    DDCPipelineParams
)

from .core.ddc_strategy_processor import (
    DDCStrategyProcessor,
    DDCStrategyExecutionResult
)

from .core.ddc_pipeline_executor import (
    DDCPipelineExecutor,
    DDCPipelineExecutionResult
)

from .core.ddc_field_mapper import (
    DDCFieldMapper,
    DDCFieldMappingResult
)

from .core.ddc_update_crud import (
    DDCUpdateCrud
)

__all__ = [
    # 主处理器
    'DDCUpdateProcessor',
    'DDCUpdateRequest',
    'DDCUpdateItem',
    'DDCUpdateResult',
    'DDCBatchUpdateResult',

    # 字段分析器
    'DDCFieldAnalyzer',
    'DDCUpdateStrategy',
    'DDCFieldAnalysisResult',
    'DDCPipelineParams',

    # 策略处理器
    'DDCStrategyProcessor',
    'DDCStrategyExecutionResult',

    # Pipeline执行器
    'DDCPipelineExecutor',
    'DDCPipelineExecutionResult',

    # 字段映射器
    'DDCFieldMapper',
    'DDCFieldMappingResult',

    # 数据库操作
    'DDCUpdateCrud'
]
