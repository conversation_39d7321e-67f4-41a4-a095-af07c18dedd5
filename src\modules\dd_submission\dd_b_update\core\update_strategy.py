"""
更新策略处理器 - 根据不同策略执行相应的更新逻辑
"""

import logging
import time
from typing import Dict, List, Any, Optional
from ..models.update_models import (
    UpdateStrategy, UpdateItem, UpdateResult, FieldAnalysisResult
)
from ..utils.field_analyzer import FieldAnalyzer
from ..utils.pipeline_executor import PipelineExecutor
from ..crud.update_crud import UpdateCrud

logger = logging.getLogger(__name__)


class UpdateStrategyProcessor:
    """更新策略处理器"""
    
    def __init__(
        self, 
        rdb_client: Any, 
        vdb_client: Any = None, 
        embedding_client: Any = None
    ):
        """
        初始化更新策略处理器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 嵌入模型客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        
        # 初始化组件
        self.field_analyzer = FieldAnalyzer()
        self.pipeline_executor = PipelineExecutor(rdb_client, vdb_client, embedding_client)
        self.update_crud = UpdateCrud(rdb_client)
        
        logger.debug("更新策略处理器初始化完成")
    
    async def process_update_item(self, update_item: UpdateItem) -> UpdateResult:
        """
        处理单个更新项目
        
        Args:
            update_item: 更新项目
            
        Returns:
            更新结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"开始处理更新项目: entry_id={update_item.entry_id}")
            
            # 1. 分析更新策略
            analysis_result = self.field_analyzer.analyze_update_strategy(update_item)
            update_item.strategy = analysis_result.strategy
            
            logger.debug(f"策略分析完成: entry_id={update_item.entry_id}, strategy={analysis_result.strategy.value}")
            
            # 2. 根据策略执行相应的处理
            if analysis_result.pipeline_required:
                # 需要执行Pipeline的策略
                result = await self._process_pipeline_strategy(update_item, analysis_result)
            else:
                # 简单更新策略
                result = await self._process_simple_strategy(update_item, analysis_result)
            
            result.execution_time = time.time() - start_time
            logger.debug(f"更新项目处理完成: entry_id={update_item.entry_id}, success={result.success}, 耗时={result.execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            error_msg = f"处理更新项目失败: entry_id={update_item.entry_id}, error={e}"
            logger.error(error_msg)
            
            return UpdateResult(
                entry_id=update_item.entry_id,
                success=False,
                strategy=update_item.strategy or UpdateStrategy.SIMPLE_UPDATE,
                entry_type=update_item.entry_type,
                error_message=error_msg,
                execution_time=time.time() - start_time
            )
    
    async def _process_pipeline_strategy(
        self, 
        update_item: UpdateItem, 
        analysis_result: FieldAnalysisResult
    ) -> UpdateResult:
        """
        处理需要Pipeline的策略
        
        Args:
            update_item: 更新项目
            analysis_result: 分析结果
            
        Returns:
            更新结果
        """
        try:
            logger.debug(f"执行Pipeline策略: entry_id={update_item.entry_id}, strategy={analysis_result.strategy.value}")
            
            # 执行Pipeline
            pipeline_result = await self.pipeline_executor.execute_pipeline_strategy(
                strategy=analysis_result.strategy,
                params=analysis_result.pipeline_params,
                entry_id=update_item.entry_id,
                original_data=update_item.original_data
            )
            
            if not pipeline_result.success:
                return pipeline_result
            
            # 合并用户指定的更新字段和Pipeline生成的字段
            final_update_fields = {}

            # 1. 添加Pipeline生成的字段（包含SDR字段用于数据库更新）
            final_update_fields.update(pipeline_result.updated_fields)

            # 2. 添加用户指定的简单字段（覆盖Pipeline结果）
            simple_fields = update_item.get_simple_fields()
            final_update_fields.update(simple_fields)

            # 3. Pipeline执行前：预先查询表中文名映射（避免Pipeline过滤影响）
            pipeline_fields = update_item.get_pipeline_fields()
            pre_table_cn_mapping = {}
            if 'BDR09' in pipeline_fields:
                pre_table_cn_mapping = await self._get_table_cn_mapping_from_bdr09(pipeline_fields['BDR09'])
                logger.debug(f"Pipeline执行前查询表中文名映射: {pre_table_cn_mapping}")

            # 4. 如果用户修改了Pipeline相关字段，需要特殊处理
            for field_name, field_value in pipeline_fields.items():
                field_name_lower = field_name.lower()

                # 根据策略决定是否保持用户输入的值
                if analysis_result.strategy == UpdateStrategy.COLUMN_SELECTION:
                    # Column Selection策略：保持BDR09和BDR16不变（用户输入优先）
                    if field_name_lower in ['bdr09', 'bdr16']:
                        final_update_fields[field_name_lower] = field_value
                        logger.debug(f"保持用户输入的{field_name_lower}: {field_value}")

                elif analysis_result.strategy == UpdateStrategy.SQL_GENERATION:
                    # SQL Generation策略：保持BDR09, BDR11不变
                    if field_name_lower in ['bdr09', 'bdr11']:
                        final_update_fields[field_name_lower] = field_value
                        logger.debug(f"保持用户输入的{field_name_lower}: {field_value}")

            # 4. COLUMN_SELECTION策略特殊处理：如果修改了BDR16表范围，从BDR16内容中提取表信息更新BDR09和BDR10
            if (analysis_result.strategy == UpdateStrategy.COLUMN_SELECTION and
                'BDR16' in pipeline_fields):

                logger.debug("检测到BDR16表范围变化，开始从BDR16内容提取表信息")
                await self._update_table_info_from_bdr16(pipeline_fields['BDR16'], final_update_fields)

            # 5. SQL Generation策略特殊处理：如果只修改了BDR11，从Pipeline结果中提取表信息更新BDR09和BDR10
            elif (analysis_result.strategy == UpdateStrategy.SQL_GENERATION and
                  'BDR11' in pipeline_fields and 'BDR09' not in pipeline_fields and
                  pipeline_result.pipeline_result):

                logger.debug("检测到只修改BDR11，开始从Pipeline结果提取表信息")
                await self._update_table_info_from_pipeline(pipeline_result.pipeline_result, final_update_fields)

            # 6. Pipeline执行后：用预先查询的表中文名覆盖bdr10（避免Pipeline过滤影响）
            if pre_table_cn_mapping and 'BDR09' in pipeline_fields:
                try:
                    import json
                    table_list = json.loads(pipeline_fields['BDR09'])
                    if isinstance(table_list, list):
                        # 根据用户输入的完整表列表，生成完整的中文名列表
                        complete_cn_names = []
                        for table_name in table_list:
                            cn_name = pre_table_cn_mapping.get(table_name.strip(), table_name.strip())
                            complete_cn_names.append(cn_name)

                        # 覆盖bdr10和sdr06（统一使用小写）
                        complete_cn_json = json.dumps(complete_cn_names, ensure_ascii=False)
                        final_update_fields['bdr10'] = complete_cn_json  # 统一小写
                        final_update_fields['sdr06'] = complete_cn_json  # 同时更新sdr06
                        logger.debug(f"Pipeline后覆盖bdr10/sdr06（小写）: {complete_cn_json}")

                except Exception as e:
                    logger.warning(f"覆盖bdr10失败: {e}")

            # 7. 构建返回给API的受影响字段列表（只包含BDR字段及其值）
            affected_bdr_fields = {}

            # 添加用户主动修改的BDR字段
            for field_name, field_value in update_item.update_fields.items():
                if field_name.upper().startswith('BDR'):
                    affected_bdr_fields[field_name.lower()] = field_value

            # 添加Pipeline自动更新的BDR字段（使用最终更新到数据库的值）
            for field_name, field_value in final_update_fields.items():
                if field_name.startswith('bdr'):
                    affected_bdr_fields[field_name] = field_value
            
            # 执行数据库更新前，将复杂数据类型转换为JSON字符串
            import json
            db_update_fields = {}
            for field_name, field_value in final_update_fields.items():
                if isinstance(field_value, (dict, list)):
                    # 复杂数据类型转换为JSON字符串
                    db_update_fields[field_name] = json.dumps(field_value, ensure_ascii=False)
                    logger.debug(f"字段{field_name}转换为JSON: {db_update_fields[field_name]}")
                elif field_value is None:
                    db_update_fields[field_name] = ""
                else:
                    db_update_fields[field_name] = str(field_value)

            # 执行数据库更新
            record_id = int(update_item.original_data.get('id', 0))
            update_success = await self.update_crud.update_record_fields(record_id, db_update_fields)

            return UpdateResult(
                entry_id=update_item.entry_id,
                success=update_success,
                strategy=analysis_result.strategy,
                entry_type=update_item.entry_type,
                updated_fields=final_update_fields,  # 返回实际数据库更新的所有字段（包含SDR）
                pipeline_result=pipeline_result.pipeline_result,
                error_message=None if update_success else "数据库更新失败"
            )
            
        except Exception as e:
            error_msg = f"Pipeline策略处理失败: entry_id={update_item.entry_id}, error={e}"
            logger.error(error_msg)
            
            return UpdateResult(
                entry_id=update_item.entry_id,
                success=False,
                strategy=analysis_result.strategy,
                entry_type=update_item.entry_type,
                error_message=error_msg
            )
    
    async def _process_simple_strategy(
        self, 
        update_item: UpdateItem, 
        analysis_result: FieldAnalysisResult
    ) -> UpdateResult:
        """
        处理简单更新策略
        
        Args:
            update_item: 更新项目
            analysis_result: 分析结果
            
        Returns:
            更新结果
        """
        try:
            logger.debug(f"执行简单更新策略: entry_id={update_item.entry_id}, strategy={analysis_result.strategy.value}")
            
            # 获取所有需要更新的字段
            update_fields = {}

            # 如果分析器生成了override_fields，优先使用override_fields
            if analysis_result.override_fields:
                logger.debug(f"使用分析器生成的override_fields: entry_id={update_item.entry_id}, fields={list(analysis_result.override_fields.keys())}")
                # 将字段名转换为小写（数据库字段名）
                for field_name, field_value in analysis_result.override_fields.items():
                    update_fields[field_name.lower()] = field_value
            else:
                # 否则使用原始字段处理逻辑
                logger.debug(f"使用原始字段处理逻辑: entry_id={update_item.entry_id}")

                # 添加简单字段
                simple_fields = update_item.get_simple_fields()
                update_fields.update(simple_fields)

                # 添加Pipeline字段（直接更新，不经过Pipeline处理）
                pipeline_fields = update_item.get_pipeline_fields()
                for field_name, field_value in pipeline_fields.items():
                    update_fields[field_name.lower()] = field_value

                    # 只有特定的BDR字段才有对应的SDR字段映射
                    if field_name.upper().startswith('BDR'):
                        # 定义BDR到SDR的映射关系（只包含有映射关系的字段）
                        bdr_to_sdr_mapping = {
                            'BDR09': 'SDR05',  # BDR09 → SDR05
                            'BDR10': 'SDR06',  # BDR10 → SDR06
                            'BDR11': 'SDR08',  # BDR11 → SDR08
                            # 注意：BDR11不映射到SDR11，SDR11保持默认值"不适用"
                        }

                        bdr_field_upper = field_name.upper()
                        if bdr_field_upper in bdr_to_sdr_mapping:
                            sdr_field_name = bdr_to_sdr_mapping[bdr_field_upper]
                            update_fields[sdr_field_name.lower()] = field_value
                            logger.debug(f"映射BDR到SDR字段: {field_name} → {sdr_field_name} = {field_value}")
                        else:
                            logger.debug(f"BDR字段无SDR映射: {field_name}")

            if not update_fields:
                logger.debug(f"没有字段需要更新: entry_id={update_item.entry_id}")
                return UpdateResult(
                    entry_id=update_item.entry_id,
                    success=True,
                    strategy=analysis_result.strategy,
                    updated_fields={},
                    error_message="没有字段需要更新"
                )

            # 构建返回给API的受影响字段列表（包含BDR和SDR字段及其值）
            affected_fields = {}
            for field_name, field_value in update_fields.items():
                if field_name.lower().startswith(('bdr', 'sdr')):
                    affected_fields[field_name.lower()] = field_value

            # 执行数据库更新
            record_id = int(update_item.original_data.get('id', 0))
            update_success = await self.update_crud.update_record_fields(record_id, update_fields)

            return UpdateResult(
                entry_id=update_item.entry_id,
                success=update_success,
                strategy=analysis_result.strategy,
                entry_type=update_item.entry_type,
                updated_fields=affected_fields,  # 返回受影响的BDR和SDR字段
                error_message=None if update_success else "数据库更新失败"
            )
            
        except Exception as e:
            error_msg = f"简单策略处理失败: entry_id={update_item.entry_id}, error={e}"
            logger.error(error_msg)
            
            return UpdateResult(
                entry_id=update_item.entry_id,
                success=False,
                strategy=analysis_result.strategy,
                entry_type=update_item.entry_type,
                error_message=error_msg
            )
    
    async def validate_update_items(
        self, 
        report_code: str, 
        dept_id: str, 
        update_items: List[UpdateItem]
    ) -> List[UpdateItem]:
        """
        验证更新项目并加载原始数据
        
        Args:
            report_code: 报告代码
            dept_id: 部门ID
            update_items: 更新项目列表
            
        Returns:
            验证后的更新项目列表（包含原始数据）
        """
        validated_items = []
        
        try:
            logger.debug(f"验证更新项目: report_code={report_code}, dept_id={dept_id}, count={len(update_items)}")
            
            for update_item in update_items:
                # 获取原始记录数据
                original_data = await self.update_crud.get_record_by_identifiers(
                    report_code, dept_id, update_item.entry_id
                )
                
                if original_data:
                    update_item.original_data = original_data

                    # 只有在前端没有提供entry_type时才从数据库转换
                    if not getattr(update_item, 'frontend_entry_type', False):
                        submission_type = original_data.get('submission_type', 'SUBMISSION')
                        if submission_type == 'RANGE':
                            update_item.entry_type = 'TABLE'
                        elif submission_type == 'SUBMISSION':
                            update_item.entry_type = 'ITEM'
                        else:
                            # 默认值
                            update_item.entry_type = 'ITEM'
                            logger.warning(f"未知的submission_type: {submission_type}, 使用默认值ITEM")

                        logger.debug(f"从数据库转换: entry_id={update_item.entry_id}, submission_type={submission_type} → entry_type={update_item.entry_type}")
                    else:
                        logger.debug(f"使用前端entry_type: entry_id={update_item.entry_id}, entry_type={update_item.entry_type}")

                    validated_items.append(update_item)
                else:
                    logger.warning(f"记录不存在: entry_id={update_item.entry_id}")
            
            logger.debug(f"验证完成: 有效项目={len(validated_items)}/{len(update_items)}")
            
        except Exception as e:
            logger.error(f"验证更新项目失败: error={e}")
        
        return validated_items

    async def _update_table_info_from_pipeline(self, pipeline_result: Dict[str, Any], final_update_fields: Dict[str, Any]):
        """
        从Pipeline结果中提取表信息，更新BDR09和BDR10

        Args:
            pipeline_result: Pipeline执行结果
            final_update_fields: 最终更新字段字典（会被修改）
        """
        try:
            logger.debug(f"开始从Pipeline结果提取表信息: {pipeline_result.keys()}")

            # 1. 从candidate_tables中提取表列表
            candidate_tables = pipeline_result.get('candidate_tables', {})
            logger.debug(f"candidate_tables: {candidate_tables}")

            # 提取所有数据库的表列表并合并
            all_tables = []
            for db_name, table_list in candidate_tables.items():
                if isinstance(table_list, list):
                    all_tables.extend(table_list)
                    logger.debug(f"从{db_name}提取表: {table_list}")

            if not all_tables:
                logger.warning("Pipeline结果中未找到表列表")
                return

            logger.debug(f"提取的完整表列表: {all_tables}")

            # 2. 更新BDR09（英文表名列表）
            import json
            final_update_fields['bdr09'] = json.dumps(all_tables, ensure_ascii=False)
            final_update_fields['sdr05'] = final_update_fields['bdr09']  # SDR05 = BDR09
            logger.debug(f"更新BDR09: {final_update_fields['bdr09']}")

            # 3. 查询表中文名，更新BDR10
            from modules.knowledge.metadata.crud import MetadataCrud
            metadata_crud = MetadataCrud(self.rdb_client)

            table_cn_names = []
            for table_name in all_tables:
                try:
                    # 使用source_table方法查询表中文名
                    table_info = await metadata_crud.get_source_table(table_name=table_name.strip())
                    if table_info and table_info.get("table_name_cn"):
                        table_cn_names.append(table_info["table_name_cn"])
                        logger.debug(f"查询到表中文名: {table_name} -> {table_info['table_name_cn']}")
                    else:
                        table_cn_names.append(table_name.strip())  # 使用原名作为备选
                        logger.debug(f"未找到表中文名，使用原名: {table_name}")
                except Exception as e:
                    logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                    table_cn_names.append(table_name.strip())

            # 4. 更新BDR10（中文表名列表）
            final_update_fields['bdr10'] = json.dumps(table_cn_names, ensure_ascii=False)
            final_update_fields['sdr06'] = final_update_fields['bdr10']  # SDR06 = BDR10
            logger.debug(f"更新BDR10: {final_update_fields['bdr10']}")

            logger.info(f"✅ 从Pipeline结果更新表信息完成: BDR09={len(all_tables)}个表, BDR10={len(table_cn_names)}个中文名")

        except Exception as e:
            logger.error(f"从Pipeline结果提取表信息失败: {e}")
            # 不抛出异常，让主流程继续

    async def _update_table_info_from_bdr16(self, bdr16_content: str, final_update_fields: Dict[str, Any]):
        """
        从BDR16内容中提取表信息，更新BDR09和BDR10

        Args:
            bdr16_content: BDR16字段内容
            final_update_fields: 最终更新字段字典（会被修改）
        """
        try:
            logger.debug(f"开始从BDR16内容提取表信息: {repr(bdr16_content)}")

            # 1. 从BDR16内容中提取表列表
            from ..models.update_models import BDR16Parser
            table_list = BDR16Parser.extract_table_range(bdr16_content)

            if not table_list:
                logger.warning("BDR16内容中未找到表列表")
                return

            logger.debug(f"从BDR16提取的表列表: {table_list}")

            # 2. 更新BDR09（英文表名列表）
            import json
            final_update_fields['bdr09'] = json.dumps(table_list, ensure_ascii=False)
            final_update_fields['sdr05'] = final_update_fields['bdr09']  # SDR05 = BDR09
            logger.debug(f"更新BDR09: {final_update_fields['bdr09']}")

            # 3. 查询表中文名，更新BDR10
            from modules.knowledge.metadata.crud import MetadataCrud
            metadata_crud = MetadataCrud(self.rdb_client)

            table_cn_names = []
            for table_name in table_list:
                try:
                    # 使用source_table方法查询表中文名
                    table_info = await metadata_crud.get_source_table(table_name=table_name.strip())
                    if table_info and table_info.get("table_name_cn"):
                        table_cn_names.append(table_info["table_name_cn"])
                        logger.debug(f"查询到表中文名: {table_name} -> {table_info['table_name_cn']}")
                    else:
                        table_cn_names.append(table_name.strip())  # 使用原名作为备选
                        logger.debug(f"未找到表中文名，使用原名: {table_name}")
                except Exception as e:
                    logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                    table_cn_names.append(table_name.strip())

            # 4. 更新BDR10（中文表名列表）
            final_update_fields['bdr10'] = json.dumps(table_cn_names, ensure_ascii=False)
            final_update_fields['sdr06'] = final_update_fields['bdr10']  # SDR06 = BDR10
            logger.debug(f"更新BDR10: {final_update_fields['bdr10']}")

            logger.info(f"✅ 从BDR16内容更新表信息完成: BDR09={len(table_list)}个表, BDR10={len(table_cn_names)}个中文名")

        except Exception as e:
            logger.error(f"从BDR16内容提取表信息失败: {e}")
            # 不抛出异常，让主流程继续

    async def _get_table_cn_mapping_from_bdr09(self, bdr09_value: str) -> Dict[str, str]:
        """
        Pipeline执行前：根据BDR09查询完整的表中文名映射

        Args:
            bdr09_value: BDR09字段值（表英文名列表JSON）

        Returns:
            表英文名到中文名的映射字典
        """
        try:
            import json
            table_list = json.loads(bdr09_value)
            if not isinstance(table_list, list):
                table_list = [table_list]

            logger.debug(f"开始查询表中文名映射: {table_list}")

            # 查询表中文名
            from modules.knowledge.metadata.crud import MetadataCrud
            metadata_crud = MetadataCrud(self.rdb_client)

            table_cn_mapping = {}
            for table_name in table_list:
                try:
                    # 使用source_table方法查询表中文名
                    table_info = await metadata_crud.get_source_table(table_name=table_name.strip())
                    if table_info and table_info.get("table_name_cn"):
                        table_cn_mapping[table_name.strip()] = table_info["table_name_cn"]
                        logger.debug(f"查询到表中文名: {table_name} -> {table_info['table_name_cn']}")
                    else:
                        table_cn_mapping[table_name.strip()] = table_name.strip()  # 使用原名作为备选
                        logger.debug(f"未找到表中文名，使用原名: {table_name}")
                except Exception as e:
                    logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                    table_cn_mapping[table_name.strip()] = table_name.strip()

            logger.info(f"✅ 表中文名映射查询完成: {len(table_cn_mapping)}个表")
            return table_cn_mapping

        except Exception as e:
            logger.error(f"查询表中文名映射失败: {e}")
            return {}
