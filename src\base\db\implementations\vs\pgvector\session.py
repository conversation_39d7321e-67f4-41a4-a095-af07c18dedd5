import asyncio
import time
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any, Union, AsyncGenerator, Generator
import psycopg2
from psycopg2.pool import SimpleConnectionPool, ThreadedConnectionPool
from psycopg2.extras import RealDictCursor
import asyncpg
import logging

logger = logging.getLogger(__name__)

from .config import PGVectorConnectionConfig
from .exceptions import ConnectionError, PoolError, TimeoutError, handle_pgvector_error


class SessionManager:
    """会话管理器 - 统一同步和异步连接管理"""
    
    def __init__(self, config: PGVectorConnectionConfig):
        self.config = config
        self.sync_pool: Optional[Union[SimpleConnectionPool, ThreadedConnectionPool]] = None
        self.async_pool: Optional[asyncpg.Pool] = None

        # 独立的连接状态管理
        self._is_sync_connected = False
        self._is_async_connected = False
        self._connection_lock = asyncio.Lock()

        logger.info(f"Initialized PGVector Session Manager for {config.host}:{config.port}")

    @property
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self._is_sync_connected and self.sync_pool is not None

    @property
    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self._is_async_connected and self.async_pool is not None

    @property
    def is_any_connected(self) -> bool:
        """检查是否有任何连接已建立（同步或异步）"""
        return self.is_connected or self.is_aconnected
    
    @handle_pgvector_error
    def connect(self) -> None:
        """建立同步连接池"""
        if self.sync_pool is not None:
            logger.warning("Sync connection pool already exists")
            return
        
        try:
            # 保守的连接池配置 - 避免PostgreSQL资源耗尽
            min_conn = getattr(self.config, 'min_connections', 1)   # 降低最小连接数
            max_conn = getattr(self.config, 'max_connections', 10)  # 降低最大连接数

            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                'password': self.config.password,
                'database': self.config.database
            }

            if max_conn > 1:
                self.sync_pool = ThreadedConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            else:
                self.sync_pool = SimpleConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            
            # 测试连接
            test_conn = None
            try:
                test_conn = self.sync_pool.getconn()
                if test_conn is None:
                    raise ConnectionError("Failed to get test connection from pool")

                with test_conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    result = cur.fetchone()
                    if result[0] != 1:
                        raise ConnectionError("Connection test failed")
            finally:
                if test_conn:
                    self.sync_pool.putconn(test_conn)
            
            self._is_sync_connected = True
            min_conn = getattr(self.config, 'min_connections', 5)   # 与上面保持一致
            max_conn = getattr(self.config, 'max_connections', 25)  # 与上面保持一致
            logger.info(f"Successfully connected to PGVector database (sync) with {min_conn}-{max_conn} connections")
            
        except Exception as e:
            logger.error(f"Failed to connect to PGVector database: {e}")
            if self.sync_pool:
                self.sync_pool.closeall()
                self.sync_pool = None
            raise ConnectionError(f"Failed to connect to PGVector database: {e}", e)
    
    @handle_pgvector_error
    async def async_connect(self) -> None:
        """建立异步连接池"""
        async with self._connection_lock:
            if self.async_pool is not None:
                logger.warning("Async connection pool already exists")
                return
            
            try:
                # 保守的异步连接池配置 - 避免PostgreSQL资源耗尽
                self.async_pool = await asyncpg.create_pool(
                    host=self.config.host,
                    port=self.config.port,
                    user=getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                    password=self.config.password,
                    database=self.config.database,
                    min_size=getattr(self.config, 'min_connections', 5),   # 降低最小连接数
                    max_size=getattr(self.config, 'max_connections', 75),  # 降低最大连接数
                    command_timeout=60,                                    # 命令超时
                    max_inactive_connection_lifetime=300                   # 非活跃连接生命周期
                )

                # 测试连接
                async with self.async_pool.acquire() as conn:
                    result = await conn.fetchval("SELECT 1")
                    if result != 1:
                        raise ConnectionError("Async connection test failed")
                
                self._is_async_connected = True
                min_conn = getattr(self.config, 'min_connections', 5)   # 与上面保持一致
                max_conn = getattr(self.config, 'max_connections', 25)  # 与上面保持一致
                logger.info(f"Successfully connected to PGVector database (async) with {min_conn}-{max_conn} connections")
                
            except Exception as e:
                logger.error(f"Failed to connect to PGVector database (async): {e}")
                if self.async_pool:
                    await self.async_pool.close()
                    self.async_pool = None
                raise ConnectionError(f"Failed to connect to PGVector database (async): {e}", e)
    
    @handle_pgvector_error
    def disconnect(self) -> None:
        """断开同步连接池"""
        if self.sync_pool:
            try:
                self.sync_pool.closeall()
                logger.info("Sync connection pool closed")
            except Exception as e:
                logger.warning(f"Error closing sync connection pool: {e}")
            finally:
                self.sync_pool = None
                self._is_sync_connected = False

    @handle_pgvector_error
    async def async_disconnect(self) -> None:
        """断开异步连接池"""
        async with self._connection_lock:
            if self.async_pool:
                try:
                    await self.async_pool.close()
                    logger.info("Async connection pool closed")
                except Exception as e:
                    logger.warning(f"Error closing async connection pool: {e}")
                finally:
                    self.async_pool = None
                    self._is_async_connected = False
    
    @contextmanager
    def get_connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        """获取同步连接上下文管理器"""
        if not self.sync_pool:
            raise ConnectionError("Sync connection pool not initialized. Call connect() first.")
        
        conn = None
        try:
            conn = self.sync_pool.getconn()
            if conn is None:
                raise PoolError("Failed to get connection from pool")
            
            # 设置自动提交
            conn.autocommit = True
            yield conn
            
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            if conn:
                try:
                    self.sync_pool.putconn(conn)
                except Exception as e:
                    logger.warning(f"Error returning connection to pool: {e}")
    
    @asynccontextmanager
    async def get_async_connection(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """获取异步连接上下文管理器"""
        if not self.async_pool:
            raise ConnectionError("Async connection pool not initialized. Call async_connect() first.")

        async with self.async_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                # asyncpg连接池会自动处理回滚
                raise

    @contextmanager
    def get_cursor(self, cursor_factory=RealDictCursor) -> Generator[psycopg2.extensions.cursor, None, None]:
        """获取同步游标上下文管理器"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=cursor_factory) as cur:
                yield cur

    @asynccontextmanager
    async def get_async_cursor(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """获取异步连接上下文管理器 - asyncpg不使用游标模式"""
        async with self.get_async_connection() as conn:
            yield conn
    
    def health_check(self) -> bool:
        """同步健康检查"""
        try:
            with self.get_cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                return result and result[0] == 1
        except Exception as e:
            logger.warning(f"Sync health check failed: {e}")
            return False
    
    async def async_health_check(self) -> bool:
        """异步健康检查"""
        try:
            async with self.get_async_cursor() as cur:
                await cur.execute("SELECT 1")
                result = await cur.fetchone()
                return result and result[0] == 1
        except Exception as e:
            logger.warning(f"Async health check failed: {e}")
            return False
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        status = {
            'is_connected': self._is_connected,
            'sync_pool_exists': self.sync_pool is not None,
            'async_pool_exists': self.async_pool is not None,
        }
        
        if self.sync_pool:
            # 注意：不同的连接池类型可能有不同的属性
            try:
                if hasattr(self.sync_pool, '_pool'):
                    status['sync_pool_size'] = len(self.sync_pool._pool)
                if hasattr(self.sync_pool, 'minconn'):
                    status['sync_min_connections'] = self.sync_pool.minconn
                if hasattr(self.sync_pool, 'maxconn'):
                    status['sync_max_connections'] = self.sync_pool.maxconn
            except Exception as e:
                logger.debug(f"Error getting sync pool status: {e}")
        
        if self.async_pool:
            try:
                status['async_pool_size'] = self.async_pool.size
                status['async_pool_free'] = self.async_pool.freesize
                status['async_min_connections'] = self.async_pool.minsize
                status['async_max_connections'] = self.async_pool.maxsize
            except Exception as e:
                logger.debug(f"Error getting async pool status: {e}")
        
        return status

    def get_enhanced_pool_metrics(self) -> Dict[str, Any]:
        """
        企业级监控：获取增强的连接池指标（非侵入式）

        Returns:
            Dict包含详细的连接池指标和健康状态
        """
        base_status = self.get_pool_status()

        # 计算连接池利用率
        sync_utilization = 0.0
        async_utilization = 0.0

        if self.sync_pool and hasattr(self.sync_pool, 'maxconn'):
            try:
                if hasattr(self.sync_pool, '_pool'):
                    used_connections = self.sync_pool.maxconn - len(self.sync_pool._pool)
                    sync_utilization = used_connections / self.sync_pool.maxconn
            except Exception:
                pass  # 保持非侵入性

        if self.async_pool:
            try:
                async_utilization = (self.async_pool.size - self.async_pool.freesize) / self.async_pool.maxsize
            except Exception:
                pass  # 保持非侵入性

        # 健康状态评估
        health_status = "healthy"
        recommendations = []

        if sync_utilization > 0.9 or async_utilization > 0.9:
            health_status = "high_load"
            recommendations.append("Consider increasing max_connections")
        elif sync_utilization < 0.1 and async_utilization < 0.1:
            health_status = "underutilized"
            recommendations.append("Consider decreasing min_connections")

        enhanced_metrics = {
            **base_status,
            'sync_utilization': sync_utilization,
            'async_utilization': async_utilization,
            'overall_utilization': (sync_utilization + async_utilization) / 2,
            'health_status': health_status,
            'recommendations': recommendations,
            'connection_efficiency': self._calculate_connection_efficiency()
        }

        return enhanced_metrics

    def _calculate_connection_efficiency(self) -> float:
        """计算连接效率分数（非侵入式）"""
        try:
            # 简单的效率计算：基于连接池配置的合理性
            sync_efficiency = 1.0
            async_efficiency = 1.0

            if self.sync_pool and hasattr(self.sync_pool, 'maxconn'):
                # 检查同步连接池配置是否合理
                max_conn = self.sync_pool.maxconn
                if max_conn < 5:
                    sync_efficiency = 0.6  # 连接数过少
                elif max_conn > 50:
                    sync_efficiency = 0.8  # 连接数可能过多

            if self.async_pool:
                # 检查异步连接池配置是否合理
                max_size = self.async_pool.maxsize
                min_size = self.async_pool.minsize
                if max_size < 5:
                    async_efficiency = 0.6
                elif max_size > 50:
                    async_efficiency = 0.8
                elif min_size > max_size * 0.5:
                    async_efficiency = 0.9  # min太高，可能浪费资源

            return (sync_efficiency + async_efficiency) / 2

        except Exception:
            return 0.8  # 默认效率分数
