"""
DD提交相关的请求模型定义

基于新业务逻辑设计的请求数据结构
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime


class BusinessSubmissionRequest(BaseModel):
    """业务报送请求模型"""
    report_code: str = Field(..., description="报表代码，直接作为version使用")

    @validator('report_code')
    def validate_report_code(cls, v):
        if not v or not v.strip():
            raise ValueError('report_code不能为空')
        return v.strip()


class DataBackfillRequest(BaseModel):
    """数据回填请求模型"""
    report_code: str = Field(..., description="报表代码，直接作为version使用")
    dept_id: Optional[str] = Field(None, description="部门ID")
    step: str = Field(..., description="处理步骤：报表解读（EXTRACTION）/义务解读（DUTY）/业务解读（BIZ）/IT解读（TECH）")
    data: List[Dict[str, Any]] = Field(..., description="用户修改的数据")


class DDBEnhancedProcessRequest(BaseModel):
    """DD-B增强处理请求模型（异步处理模式）"""
    report_code: str = Field(..., description="报表代码，如 'S71_ADS_RELEASE_V0'")
    dept_id: str = Field(..., description="部门ID")

    @validator('report_code')
    def validate_report_code(cls, v):
        if not v or not v.strip():
            raise ValueError('report_code不能为空')
        return v.strip()

    @validator('dept_id')
    def validate_dept_id(cls, v):
        if not v or not v.strip():
            raise ValueError('dept_id不能为空')
        return v.strip()


class DDCUpdateRequest(BaseModel):
    """DD-C数据更新请求模型"""
    report_code: str = Field(..., description="报表代码，如 'S71_ADS_RELEASE_V0'")
    dept_id: str = Field(..., description="部门ID")
    data: List[Dict[str, Any]] = Field(..., description="用户修改的数据，每个项目必须包含entry_id")

    @validator('report_code')
    def validate_report_code(cls, v):
        if not v or not v.strip():
            raise ValueError('report_code不能为空')
        return v.strip()

    @validator('dept_id')
    def validate_dept_id(cls, v):
        if not v or not v.strip():
            raise ValueError('dept_id不能为空')
        return v.strip()

    @validator('data')
    def validate_data(cls, v):
        if not v:
            raise ValueError('data不能为空')

        for i, item in enumerate(v):
            if not isinstance(item, dict):
                raise ValueError(f'data[{i}]必须是字典类型')
            if 'entry_id' not in item:
                raise ValueError(f'data[{i}]必须包含entry_id字段')

        return v


class DDBUpdateRequest(BaseModel):
    """DD-B数据更新请求模型"""
    report_code: str = Field(..., description="报表代码，如 'S71_ADS_RELEASE_V0'")
    dept_id: str = Field(..., description="部门ID")
    data: List[Dict[str, Any]] = Field(..., description="要更新的数据列表，每个项目必须包含entry_id和entry_type")

    @validator('report_code')
    def validate_report_code(cls, v):
        if not v or not v.strip():
            raise ValueError('report_code不能为空')
        return v.strip()

    @validator('dept_id')
    def validate_dept_id(cls, v):
        if not v or not v.strip():
            raise ValueError('dept_id不能为空')
        return v.strip()

    @validator('data')
    def validate_data(cls, v):
        if not v:
            raise ValueError('data不能为空')

        for i, item in enumerate(v):
            if not isinstance(item, dict):
                raise ValueError(f'data[{i}]必须是字典类型')
            if 'entry_id' not in item:
                raise ValueError(f'data[{i}]必须包含entry_id字段')
            if 'entry_type' not in item:
                raise ValueError(f'data[{i}]必须包含entry_type字段')

            # 验证entry_type的值（支持英文和中文格式）
            entry_type = item.get('entry_type')
            if entry_type not in ['ITEM', 'TABLE', '范围项', '填报项']:
                raise ValueError(f'data[{i}]的entry_type必须是"ITEM"、"TABLE"、"范围项"或"填报项"')

        return v


class DataBackfillItem(BaseModel):
    """数据回填单项模型"""
    entry_id: str = Field(..., description="条目ID")
    entry_type: Optional[str] = Field(None, description="条目类型")
    DR22: Optional[List[str]] = Field(None, description="责任部门")
    BDR01: Optional[List[str]] = Field(None, description="业务部门")
    BDR02: Optional[str] = Field(None, description="责任人")
    BDR03: Optional[str] = Field(None, description="业务描述")
    BDR04: Optional[str] = Field(None, description="其他字段")


# 向后兼容的别名
DutyDistributionRequest = BusinessSubmissionRequest
