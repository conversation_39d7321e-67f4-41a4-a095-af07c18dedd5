"""
全新PGVector客户端

基于新架构设计的PGVector向量数据库客户端
舍弃VSClient，直接实现VectorDatabaseClient接口

设计原则：
1. 简洁高效 - 去除冗余，专注核心功能
2. 模块化 - 基于核心模块组合
3. Milvus兼容 - 完全兼容Milvus API
4. 类型安全 - 完整的类型注解
5. 异步优先 - 优先支持异步操作

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from contextlib import asynccontextmanager

from base.db.base.vdb.core import (
    # 核心接口
    VectorDatabaseClient,
    
    # 数据模型
    CollectionSchema, Entity, SearchResult,
    SearchRequest, QueryRequest, GetRequest,
    InsertRequest, DeleteRequest, HybridSearchRequest,
    
    # 异常
    VectorDBException, ConnectionError, CollectionNotFoundError,
    
    # 类型
    VectorDBType
)
from base.db.base.schemas import VDBConnectionConfig

# 导入核心模块
from .core.collection_ops import PGVectorCollectionManager
from .pgvector_connection import PGVectorConnectionManager

# 向后兼容的别名
PGVectorSessionManager = PGVectorConnectionManager
UnifiedConnectionManager = PGVectorConnectionManager  # 向后兼容

logger = logging.getLogger(__name__)


class PGVectorClient(VectorDatabaseClient):
    """
    全新PGVector客户端
    
    基于新架构设计，提供简洁高效的向量数据库操作接口
    完全兼容Milvus API，支持同步/异步双重操作模式
    """
    
    def __init__(self, config: VDBConnectionConfig, **kwargs):
        """
        初始化PGVector客户端
        
        Args:
            config: 数据库连接配置
            **kwargs: 额外配置参数
        """
        self.config = config
        self.kwargs = kwargs
        
        # 初始化PGVector连接管理器 - 简化架构
        self.connection_manager = PGVectorConnectionManager(config, **kwargs)
        self.collection_manager = PGVectorCollectionManager(self.connection_manager)

        # 向后兼容的别名
        self.session_manager = self.connection_manager

        # 注意：连接状态完全由PGVector连接管理器管理，客户端不维护连接状态
        
        # 并发安全锁 - 防止多个任务同时初始化连接
        self._connection_lock = asyncio.Lock()

        # 性能统计
        self._stats = {
            'operations_count': 0,
            'last_operation_time': None,
            'total_operation_time': 0.0
        }

        logger.info(f"初始化PGVector客户端: {config.host}:{config.port}")
    
    # ==================== 连接管理 ====================
    
    def connect(self, **kwargs) -> None:
        """建立数据库连接（同步）"""
        try:
            self.connection_manager.connect(**kwargs)
            logger.info("PGVector客户端连接成功")
        except Exception as e:
            logger.error(f"PGVector客户端连接失败: {e}")
            raise

    async def aconnect(self, **kwargs) -> None:
        """建立数据库连接（异步）"""
        try:
            await self.connection_manager.aconnect(**kwargs)
            logger.info("PGVector客户端异步连接成功")
        except Exception as e:
            logger.error(f"PGVector客户端异步连接失败: {e}")
            raise
    
    def disconnect(self) -> None:
        """断开数据库连接（同步）"""
        try:
            self.connection_manager.disconnect()
            logger.info("PGVector客户端连接已断开")
        except Exception as e:
            logger.warning(f"断开PGVector客户端连接时出错: {e}")

    async def adisconnect(self) -> None:
        """断开数据库连接（异步）"""
        try:
            await self.connection_manager.adisconnect()
            logger.info("PGVector客户端异步连接已断开")
        except Exception as e:
            logger.warning(f"异步断开PGVector客户端连接时出错: {e}")
    
    @property
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self.connection_manager.is_connected

    @property
    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self.connection_manager.is_aconnected
    
    def health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """健康检查（同步）"""
        self._ensure_connected()
        return self.connection_manager.health_check()

    async def ahealth_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """健康检查（异步）"""
        await self._aensure_connected()
        return await self.connection_manager.ahealth_check()
    
    # ==================== 集合管理 ====================
    
    def create_collection(self, collection_name: str, schema: CollectionSchema,
                         **kwargs) -> bool:
        """创建集合（同步）"""
        self._ensure_connected()
        return self.collection_manager.create_collection(collection_name, schema, **kwargs)
    
    async def acreate_collection(self, collection_name: str, schema: CollectionSchema,
                                **kwargs) -> bool:
        """创建集合（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.acreate_collection(collection_name, schema, **kwargs)
    
    def drop_collection(self, collection_name: str) -> bool:
        """删除集合（同步）"""
        self._ensure_connected()
        return self.collection_manager.drop_collection(collection_name)
    
    async def adrop_collection(self, collection_name: str) -> bool:
        """删除集合（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.adrop_collection(collection_name)
    
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（同步）"""
        self._ensure_connected()
        return self.collection_manager.has_collection(collection_name)
    
    async def ahas_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.ahas_collection(collection_name)
    
    def list_collections(self) -> List[str]:
        """列出所有集合（同步）"""
        self._ensure_connected()
        return self.collection_manager.list_collections()
    
    async def alist_collections(self) -> List[str]:
        """列出所有集合（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.alist_collections()
    
    def describe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（同步）"""
        self._ensure_connected()
        return self.collection_manager.describe_collection(collection_name)
    
    async def adescribe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.adescribe_collection(collection_name)
    
    def create_index(self, collection_name: str, field_name: str,
                    index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（同步）"""
        self._ensure_connected()
        return self.collection_manager.create_index(collection_name, field_name, index_params, **kwargs)
    
    async def acreate_index(self, collection_name: str, field_name: str,
                           index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（异步）"""
        await self._aensure_connected()
        return await self.collection_manager.acreate_index(collection_name, field_name, index_params, **kwargs)

    def drop_index(self, collection_name: str, field_name: str, **kwargs) -> bool:
        """删除索引（同步）"""
        self._ensure_connected()

        # 构建删除索引SQL
        index_name = f"idx_{collection_name}_{field_name}"
        drop_sql = f'DROP INDEX IF EXISTS "{index_name}"'

        try:
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(drop_sql)
                cursor.connection.commit()

            logger.info(f"删除索引成功: {collection_name}.{field_name}")
            return True

        except Exception as e:
            logger.error(f"删除索引失败: {collection_name}.{field_name}, 错误: {e}")
            return False

    async def adrop_index(self, collection_name: str, field_name: str, **kwargs) -> bool:
        """删除索引（异步）"""
        await self._aensure_connected()

        # 构建删除索引SQL
        index_name = f"idx_{collection_name}_{field_name}"
        drop_sql = f'DROP INDEX IF EXISTS "{index_name}"'

        try:
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute(drop_sql)

            logger.info(f"异步删除索引成功: {collection_name}.{field_name}")
            return True

        except Exception as e:
            logger.error(f"异步删除索引失败: {collection_name}.{field_name}, 错误: {e}")
            return False

    def get_database_type(self) -> VectorDBType:
        """获取数据库类型"""
        return VectorDBType.PGVECTOR

    # ==================== 事务管理 ====================

    def begin_transaction(self):
        """开始同步事务（返回事务上下文管理器）"""
        self._ensure_connected()
        return self.connection_manager.transaction()

    def abegin_transaction(self):
        """开始异步事务（返回事务上下文管理器）"""
        # 返回一个可以直接使用的异步上下文管理器
        class AsyncTransactionManager:
            def __init__(self, client):
                self.client = client
                self.transaction_cm = None

            async def __aenter__(self):
                await self.client._aensure_connected()
                self.transaction_cm = self.client.connection_manager.atransaction()
                return await self.transaction_cm.__aenter__()

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                if self.transaction_cm:
                    return await self.transaction_cm.__aexit__(exc_type, exc_val, exc_tb)

        return AsyncTransactionManager(self)

    # ==================== 数据操作 ====================

    def insert(self, collection_name: str, data: List[Dict[str, Any]],
               partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """插入数据（同步）

        Args:
            collection_name: 集合名称
            data: 要插入的数据列表
            partition_name: 分区名称（可选）
            **kwargs: 额外参数

        Returns:
            插入结果
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建插入SQL
            if not data:
                return {'insert_count': 0, 'ids': []}

            # 验证向量格式
            self._validate_insert_data(data)

            # 获取集合模式
            schema = self.describe_collection(collection_name)

            # 生成插入SQL
            insert_sql, values = self._build_insert_sql(collection_name, data, schema)

            # 添加调试信息
            sql_preview = insert_sql[:200] if len(insert_sql) > 200 else insert_sql
            logger.debug(f"同步插入SQL: {sql_preview}...")
            logger.debug(f"参数数量: {len(values)}, 记录数: {len(data)}")

            # 执行插入
            with self.session_manager.get_cursor() as cursor:
                if 'RETURNING' in insert_sql:
                    # 批量插入并返回ID（优化后的实现）
                    cursor.execute(insert_sql, values)
                    results = cursor.fetchall()

                    # 处理返回的ID列表
                    inserted_ids = []
                    if results:
                        for result in results:
                            try:
                                # 尝试按字典方式访问（如果是Row对象）
                                if hasattr(result, 'get') and callable(getattr(result, 'get')):
                                    # 对于复合主键，返回第一个自增字段的值
                                    try:
                                        primary_fields = schema.get_primary_fields()
                                        auto_id_fields = [field for field in primary_fields if field.auto_id]
                                        if auto_id_fields:
                                            inserted_ids.append(result.get(auto_id_fields[0].name))
                                        else:
                                            inserted_ids.append(result.get(primary_fields[0].name))
                                    except (ValueError, AttributeError):
                                        inserted_ids.append(result[0])
                                else:
                                    # 按索引访问（元组或列表）
                                    inserted_ids.append(result[0])
                            except (IndexError, TypeError, AttributeError):
                                # 如果无法获取ID，使用默认值
                                inserted_ids.append(len(inserted_ids))
                    else:
                        # 如果没有返回结果，使用默认ID列表
                        inserted_ids = list(range(len(data)))
                else:
                    # 批量插入，使用execute（因为我们已经构建了完整的批量SQL）
                    cursor.execute(insert_sql, values)
                    inserted_ids = list(range(len(data)))

                cursor.connection.commit()

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'insert_count': len(data),
                'ids': inserted_ids,
                'operation_time': operation_time
            }

            logger.info(f"插入数据成功: {collection_name}, {len(data)}条记录")
            return result

        except Exception as e:
            logger.error(f"插入数据失败: {collection_name}, 错误: {e}")
            raise

    async def ainsert(self, collection_name: str, data: List[Dict[str, Any]],
                     partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """插入数据（异步）"""
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建插入SQL
            if not data:
                return {'insert_count': 0, 'ids': []}

            # 验证向量格式
            self._validate_insert_data(data)

            # 获取集合模式
            schema = await self.adescribe_collection(collection_name)

            # 生成异步插入SQL
            insert_sql, values = self._build_async_insert_sql(collection_name, data, schema)

            # 添加调试信息
            sql_preview = insert_sql[:200] if len(insert_sql) > 200 else insert_sql
            logger.debug(f"异步插入SQL: {sql_preview}...")
            logger.debug(f"参数数量: {len(values)}, 记录数: {len(data)}")

            # 执行插入
            async with self.session_manager.get_async_cursor() as conn:
                # asyncpg使用不同的API
                if 'RETURNING' in insert_sql:
                    # 批量插入并返回ID（优化后的实现）
                    # 对于批量插入，不要解包values，直接传递
                    results = await conn.fetch(insert_sql, *values)

                    # 处理返回的ID列表
                    inserted_ids = []
                    if results:
                        for result in results:
                            # asyncpg返回的是Record对象，可以通过索引访问
                            inserted_ids.append(result[0])
                    else:
                        # 如果没有返回结果，使用默认ID列表
                        inserted_ids = list(range(len(data)))
                else:
                    # 普通插入，使用execute进行批量操作
                    # 对于批量插入，直接传递所有参数
                    await conn.execute(insert_sql, *values)
                    inserted_ids = list(range(len(data)))

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'insert_count': len(data),
                'ids': inserted_ids,
                'operation_time': operation_time
            }

            logger.info(f"异步插入数据成功: {collection_name}, {len(data)}条记录")
            return result

        except Exception as e:
            logger.error(f"异步插入数据失败: {collection_name}, 错误: {e}")
            raise

    # ==================== 数据更新 ====================

    def update(self, collection_name: str, data: Dict[str, Any], expr: str,
               partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """更新数据（同步）

        Args:
            collection_name: 集合名称
            data: 要更新的数据字典
            expr: 更新条件表达式
            partition_name: 分区名称（暂不支持）
            **kwargs: 额外参数

        Returns:
            更新结果字典，包含update_count和operation_time
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 验证更新数据
            if not data:
                return {'update_count': 0, 'operation_time': 0.0}

            # 获取集合模式
            schema = self.describe_collection(collection_name)

            # 构建更新SQL
            update_sql, values = self._build_update_sql(collection_name, data, expr, schema)

            # 执行更新
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(update_sql, values)
                update_count = cursor.rowcount
                cursor.connection.commit()

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'update_count': update_count,
                'operation_time': operation_time
            }

            logger.info(f"更新数据成功: {collection_name}, {update_count}条记录")
            return result

        except Exception as e:
            logger.error(f"更新数据失败: {collection_name}, 错误: {e}")
            raise

    async def aupdate(self, collection_name: str, data: Dict[str, Any], expr: str,
                      partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """更新数据（异步）

        Args:
            collection_name: 集合名称
            data: 要更新的数据字典
            expr: 更新条件表达式
            partition_name: 分区名称（暂不支持）
            **kwargs: 额外参数

        Returns:
            更新结果字典，包含update_count和operation_time
        """
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 验证更新数据
            if not data:
                return {'update_count': 0, 'operation_time': 0.0}

            # 获取集合模式
            schema = await self.adescribe_collection(collection_name)

            # 构建异步更新SQL
            update_sql, values = self._build_async_update_sql(collection_name, data, expr, schema)

            # 执行更新
            async with self.session_manager.get_async_cursor() as conn:
                result = await conn.execute(update_sql, *values)
                # asyncpg的execute返回状态字符串，如"UPDATE 3"
                update_count = int(result.split()[-1]) if result.split() else 0

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'update_count': update_count,
                'operation_time': operation_time
            }

            logger.info(f"异步更新数据成功: {collection_name}, {update_count}条记录")
            return result

        except Exception as e:
            logger.error(f"异步更新数据失败: {collection_name}, 错误: {e}")
            raise

    def batch_update(self, collection_name: str, updates: List[Dict[str, Any]],
                    partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """批量更新数据（同步）

        Args:
            collection_name: 集合名称
            updates: 更新操作列表，每个元素包含data和expr字段
                    格式：[{"data": {...}, "expr": "..."}, ...]
            partition_name: 分区名称（暂不支持）
            **kwargs: 额外参数

        Returns:
            批量更新结果字典，包含update_count和operation_time
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            if not updates:
                return {'update_count': 0, 'operation_time': 0.0, 'batch_results': []}

            # 获取集合模式
            schema = self.describe_collection(collection_name)

            # 优化的批量更新 - 基于连接池的智能分批处理
            batch_results = []
            total_updated = 0

            # 预处理和验证所有更新项
            valid_updates = []
            for i, update_item in enumerate(updates):
                try:
                    # 验证更新项格式
                    if not isinstance(update_item, dict) or 'data' not in update_item or 'expr' not in update_item:
                        raise ValueError(f"更新项{i+1}格式错误，需要包含'data'和'expr'字段")

                    data = update_item['data']
                    expr = update_item['expr']

                    if not data:
                        batch_results.append({
                            'data': data,
                            'expr': expr,
                            'update_count': 0,
                            'success': True
                        })
                        continue

                    valid_updates.append(update_item)

                except Exception as e:
                    batch_results.append({
                        'data': update_item.get('data', {}),
                        'expr': update_item.get('expr', ''),
                        'update_count': 0,
                        'success': False,
                        'error': str(e)
                    })
                    logger.warning(f"批量更新第{i+1}个操作验证失败: {e}")

            if not valid_updates:
                return {
                    'update_count': total_updated,
                    'operation_time': time.time() - start_time,
                    'batch_results': batch_results
                }

            # 分批处理：每批50个更新，减少事务开销
            chunk_size = 50
            chunks = [valid_updates[i:i+chunk_size] for i in range(0, len(valid_updates), chunk_size)]

            # 执行分批更新
            for chunk in chunks:
                try:
                    with self.session_manager.get_cursor() as cursor:
                        cursor.connection.autocommit = False
                        try:
                            # 执行批次更新
                            chunk_results = self._execute_batch_update_chunk(
                                cursor, collection_name, chunk, schema
                            )

                            # 统计成功的更新数量
                            chunk_updated = sum(1 for result in chunk_results if result['success'])
                            total_updated += chunk_updated

                            # 合并批次结果
                            batch_results.extend(chunk_results)

                            # 提交批次事务
                            cursor.connection.commit()

                        except Exception as e:
                            cursor.connection.rollback()
                            raise
                        finally:
                            cursor.connection.autocommit = True

                except Exception as e:
                    # 整个批次失败，记录所有失败结果
                    for update_item in chunk:
                        batch_results.append({
                            'data': update_item.get('data', {}),
                            'expr': update_item.get('expr', ''),
                            'update_count': 0,
                            'success': False,
                            'error': str(e)
                        })
                    logger.warning(f"批次更新失败: {e}")

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'update_count': total_updated,
                'operation_time': operation_time,
                'batch_results': batch_results
            }

            logger.info(f"批量更新数据成功: {collection_name}, 总计更新{total_updated}条记录")
            return result

        except Exception as e:
            logger.error(f"批量更新数据失败: {collection_name}, 错误: {e}")
            raise

    async def abatch_update(self, collection_name: str, updates: List[Dict[str, Any]],
                           partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """批量更新数据（异步）

        Args:
            collection_name: 集合名称
            updates: 更新操作列表，每个元素包含data和expr字段
                    格式：[{"data": {...}, "expr": "..."}, ...]
            partition_name: 分区名称（暂不支持）
            **kwargs: 额外参数

        Returns:
            批量更新结果字典，包含update_count和operation_time
        """
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            if not updates:
                return {'update_count': 0, 'operation_time': 0.0, 'batch_results': []}

            # 获取集合模式
            schema = await self.adescribe_collection(collection_name)

            # 优化的异步批量更新 - 基于连接池的智能并发处理
            batch_results = []
            total_updated = 0

            # 预处理和验证所有更新项
            valid_updates = []
            for i, update_item in enumerate(updates):
                try:
                    # 验证更新项格式
                    if not isinstance(update_item, dict) or 'data' not in update_item or 'expr' not in update_item:
                        raise ValueError(f"更新项{i+1}格式错误，需要包含'data'和'expr'字段")

                    data = update_item['data']
                    expr = update_item['expr']

                    if not data:
                        batch_results.append({
                            'data': data,
                            'expr': expr,
                            'update_count': 0,
                            'success': True
                        })
                        continue

                    valid_updates.append(update_item)

                except Exception as e:
                    batch_results.append({
                        'data': update_item.get('data', {}),
                        'expr': update_item.get('expr', ''),
                        'update_count': 0,
                        'success': False,
                        'error': str(e)
                    })
                    logger.warning(f"异步批量更新第{i+1}个操作验证失败: {e}")

            if not valid_updates:
                return {
                    'update_count': total_updated,
                    'operation_time': time.time() - start_time,
                    'batch_results': batch_results
                }

            # 分批处理：每批50个更新，减少事务开销
            chunk_size = 50
            chunks = [valid_updates[i:i+chunk_size] for i in range(0, len(valid_updates), chunk_size)]

            # 异步并发执行所有批次，让连接池自动管理并发
            async def execute_chunk(chunk):
                """执行单个批次"""
                try:
                    async with self.session_manager.get_async_cursor() as conn:
                        # 执行批次更新
                        chunk_results = await self._execute_async_batch_update_chunk(
                            conn, collection_name, chunk, schema
                        )
                        return chunk_results

                except Exception as e:
                    # 整个批次失败，记录所有失败结果
                    chunk_results = []
                    for update_item in chunk:
                        chunk_results.append({
                            'data': update_item.get('data', {}),
                            'expr': update_item.get('expr', ''),
                            'update_count': 0,
                            'success': False,
                            'error': str(e)
                        })
                    logger.warning(f"异步批次更新失败: {e}")
                    return chunk_results

            # 并发执行所有批次，连接池自动管理并发数
            import asyncio
            tasks = [execute_chunk(chunk) for chunk in chunks]
            all_chunk_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 合并所有批次结果
            for chunk_results in all_chunk_results:
                if isinstance(chunk_results, Exception):
                    logger.error(f"批次执行异常: {chunk_results}")
                    continue

                batch_results.extend(chunk_results)
                # 统计成功的更新数量
                chunk_updated = sum(1 for result in chunk_results if result['success'])
                total_updated += chunk_updated

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'update_count': total_updated,
                'operation_time': operation_time,
                'batch_results': batch_results
            }

            logger.info(f"异步批量更新数据成功: {collection_name}, 总计更新{total_updated}条记录")
            return result

        except Exception as e:
            logger.error(f"异步批量更新数据失败: {collection_name}, 错误: {e}")
            raise

    # ==================== 数据删除 ====================

    def delete(self, collection_name: str, expr: str,
               partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """删除数据（同步）

        Args:
            collection_name: 集合名称
            expr: 删除条件表达式
            partition_name: 分区名称（可选）
            **kwargs: 额外参数

        Returns:
            删除结果
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建删除SQL
            where_clause = self._expr_to_where(expr)
            delete_sql = f'DELETE FROM "{collection_name}" WHERE {where_clause}'

            # 执行删除
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(delete_sql)
                cursor.connection.commit()
                deleted_count = cursor.rowcount

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'delete_count': deleted_count,
                'operation_time': operation_time
            }

            logger.info(f"删除数据成功: {collection_name}, {deleted_count}条记录")
            return result

        except Exception as e:
            logger.error(f"删除数据失败: {collection_name}, 错误: {e}")
            raise

    async def adelete(self, collection_name: str, expr: str,
                     partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """删除数据（异步）"""
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建删除SQL
            where_clause = self._expr_to_where(expr)
            delete_sql = f'DELETE FROM "{collection_name}" WHERE {where_clause}'

            # 执行删除
            async with self.session_manager.get_async_cursor() as conn:
                result = await conn.execute(delete_sql)
                # asyncpg返回删除的行数作为字符串，如 "DELETE 5"
                deleted_count = int(result.split()[-1]) if result.split() else 0

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'delete_count': deleted_count,
                'operation_time': operation_time
            }

            logger.info(f"异步删除数据成功: {collection_name}, {deleted_count}条记录")
            return result

        except Exception as e:
            logger.error(f"异步删除数据失败: {collection_name}, 错误: {e}")
            raise

    def batch_delete(self, collection_name: str, exprs: List[str],
                    partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """批量删除数据（同步）

        Args:
            collection_name: 集合名称
            exprs: 删除条件表达式列表
            partition_name: 分区名称（可选）
            **kwargs: 额外参数

        Returns:
            批量删除结果
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            if not exprs:
                return {'delete_count': 0, 'operation_time': 0.0, 'batch_results': []}

            # 批量执行删除
            batch_results = []
            total_deleted = 0

            with self.session_manager.get_cursor() as cursor:
                for i, expr in enumerate(exprs):
                    try:
                        # 构建删除SQL
                        where_clause = self._expr_to_where(expr)
                        delete_sql = f'DELETE FROM "{collection_name}" WHERE {where_clause}'

                        # 执行删除
                        cursor.execute(delete_sql)
                        deleted_count = cursor.rowcount
                        total_deleted += deleted_count

                        batch_results.append({
                            'expr': expr,
                            'delete_count': deleted_count,
                            'success': True
                        })

                    except Exception as e:
                        batch_results.append({
                            'expr': expr,
                            'delete_count': 0,
                            'success': False,
                            'error': str(e)
                        })
                        logger.warning(f"批量删除第{i+1}个条件失败: {expr}, 错误: {e}")

                cursor.connection.commit()

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'delete_count': total_deleted,
                'operation_time': operation_time,
                'batch_results': batch_results
            }

            logger.info(f"批量删除数据成功: {collection_name}, 总计删除{total_deleted}条记录")
            return result

        except Exception as e:
            logger.error(f"批量删除数据失败: {collection_name}, 错误: {e}")
            raise

    async def abatch_delete(self, collection_name: str, exprs: List[str],
                           partition_name: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """批量删除数据（异步）

        Args:
            collection_name: 集合名称
            exprs: 删除条件表达式列表
            partition_name: 分区名称（可选）
            **kwargs: 额外参数

        Returns:
            批量删除结果
        """
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            if not exprs:
                return {'delete_count': 0, 'operation_time': 0.0, 'batch_results': []}

            # 批量执行删除
            batch_results = []
            total_deleted = 0

            async with self.session_manager.get_async_cursor() as conn:
                for i, expr in enumerate(exprs):
                    try:
                        # 构建删除SQL
                        where_clause = self._expr_to_where(expr)
                        delete_sql = f'DELETE FROM "{collection_name}" WHERE {where_clause}'

                        # 执行删除
                        result = await conn.execute(delete_sql)
                        deleted_count = int(result.split()[-1]) if result.split() else 0
                        total_deleted += deleted_count

                        batch_results.append({
                            'expr': expr,
                            'delete_count': deleted_count,
                            'success': True
                        })

                    except Exception as e:
                        batch_results.append({
                            'expr': expr,
                            'delete_count': 0,
                            'success': False,
                            'error': str(e)
                        })
                        logger.warning(f"异步批量删除第{i+1}个条件失败: {expr}, 错误: {e}")

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            result = {
                'delete_count': total_deleted,
                'operation_time': operation_time,
                'batch_results': batch_results
            }

            logger.info(f"异步批量删除数据成功: {collection_name}, 总计删除{total_deleted}条记录")
            return result

        except Exception as e:
            logger.error(f"异步批量删除数据失败: {collection_name}, 错误: {e}")
            raise

    # ==================== 搜索操作 ====================

    def search(self, collection_name: str, data: List[List[float]], anns_field: str,
               param: Dict[str, Any], limit: int = 10, expr: Optional[str] = None,
               output_fields: Optional[List[str]] = None, min_score: Optional[float] = None, **kwargs) -> List[List[SearchResult]]:
        """向量搜索（同步）

        Args:
            collection_name: 集合名称
            data: 查询向量列表
            anns_field: 向量字段名
            param: 搜索参数
            limit: 返回结果数量
            expr: 过滤表达式
            output_fields: 输出字段
            min_score: 最小相似度分数阈值
            **kwargs: 额外参数

        Returns:
            搜索结果列表
        """
        import time
        start_time = time.time()

        try:
            # 验证搜索向量格式（在连接之前进行，提供更快的反馈）
            self._validate_search_vectors(data)

            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建搜索SQL
            search_results = []

            for query_vector in data:
                # 构建单个向量的搜索SQL
                search_sql = self._build_search_sql(
                    collection_name, query_vector, anns_field, param, limit, expr, output_fields, min_score
                )

                # 执行搜索
                with self.session_manager.get_cursor() as cursor:
                    cursor.execute(search_sql)
                    rows = cursor.fetchall()

                # 转换结果
                vector_results = []
                for row in rows:
                    result = self._row_to_search_result(row, output_fields)
                    vector_results.append(result)

                search_results.append(vector_results)

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            logger.info(f"向量搜索成功: {collection_name}, {len(data)}个查询向量")
            return search_results

        except Exception as e:
            logger.error(f"向量搜索失败: {collection_name}, 错误: {e}")
            raise

    async def asearch(self, collection_name: str, data: List[List[float]], anns_field: str,
                     param: Dict[str, Any], limit: int = 10, expr: Optional[str] = None,
                     output_fields: Optional[List[str]] = None, min_score: Optional[float] = None, **kwargs) -> List[List[SearchResult]]:
        """向量搜索（异步）- 带PostgreSQL资源耗尽重试机制"""
        import time
        import asyncio
        start_time = time.time()

        # PostgreSQL资源耗尽重试配置
        max_retries = 3
        base_delay = 1.0  # 基础延迟1秒

        for retry_attempt in range(max_retries + 1):
            try:
                # 验证搜索向量格式（在连接之前进行，提供更快的反馈）
                self._validate_search_vectors(data)

                await self._aensure_connected()

                # 验证集合存在
                if not await self.ahas_collection(collection_name):
                    raise CollectionNotFoundError(
                        collection_name,
                        database_type=VectorDBType.PGVECTOR
                    )

                # 构建搜索SQL - 使用单个连接处理所有查询向量
                search_results = []

                # 使用单个连接处理所有查询，避免重复获取连接
                async with self.session_manager.get_async_cursor() as conn:
                    for query_vector in data:
                        # 构建单个向量的搜索SQL
                        search_sql = self._build_search_sql(
                            collection_name, query_vector, anns_field, param, limit, expr, output_fields, min_score
                        )

                        # 执行搜索
                        logger.debug(f"执行向量搜索SQL: {search_sql}")
                        rows = await conn.fetch(search_sql)
                        logger.debug(f"SQL返回{len(rows)}行结果")

                        # 转换结果 - SQL已经处理了min_score过滤
                        vector_results = []
                        for i, row in enumerate(rows):
                            # 🔧 添加详细的行数据调试
                            if hasattr(row, 'get'):
                                distance = row.get('distance')
                                similarity_score = row.get('similarity_score')
                                data_row_id = row.get('data_row_id')
                                logger.debug(f"行{i+1}: data_row_id={data_row_id}, distance={distance}, similarity_score={similarity_score}")

                            result = self._row_to_search_result(row, output_fields)
                            vector_results.append(result)

                        if min_score is not None:
                            logger.debug(f"SQL层面过滤完成，返回{len(vector_results)}个结果 (min_score={min_score})")

                        search_results.append(vector_results)

                operation_time = time.time() - start_time
                self._update_stats(operation_time)

                logger.info(f"异步向量搜索成功: {collection_name}, {len(data)}个查询向量")
                return search_results

            except Exception as e:
                error_str = str(e).lower()

                # 检查是否是PostgreSQL资源耗尽错误
                is_resource_exhaustion = (
                    "out of shared memory" in error_str or
                    "max_locks_per_transaction" in error_str or
                    "could not serialize access" in error_str or
                    "deadlock detected" in error_str
                )

                if is_resource_exhaustion and retry_attempt < max_retries:
                    # 计算指数退避延迟
                    delay = base_delay * (2 ** retry_attempt)
                    logger.warning(
                        f"PostgreSQL资源耗尽，第 {retry_attempt + 1}/{max_retries + 1} 次重试 "
                        f"(延迟 {delay:.1f}s): {e}"
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 非资源耗尽错误或重试次数用尽
                    if retry_attempt > 0:
                        logger.error(f"异步向量搜索重试 {retry_attempt} 次后仍失败: {collection_name}, 错误: {e}")
                    else:
                        logger.error(f"异步向量搜索失败: {collection_name}, 错误: {e}")
                    raise

    def query(self, collection_name: str, expr: str, output_fields: Optional[List[str]] = None,
              limit: Optional[int] = None, offset: Optional[int] = None, **kwargs) -> List[Entity]:
        """标量查询（同步）

        Args:
            collection_name: 集合名称
            expr: 查询表达式
            output_fields: 输出字段
            limit: 结果数量限制
            offset: 偏移量
            **kwargs: 额外参数

        Returns:
            查询结果列表
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 构建查询SQL
            query_sql = self._build_query_sql(collection_name, expr, output_fields, limit, offset)

            # 执行查询
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(query_sql)
                rows = cursor.fetchall()

            # 转换结果
            results = []
            for row in rows:
                entity = self._row_to_entity(row, output_fields)
                results.append(entity)

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            logger.info(f"标量查询成功: {collection_name}, {len(results)}条记录")
            return results

        except Exception as e:
            logger.error(f"标量查询失败: {collection_name}, 错误: {e}")
            raise

    async def aquery(self, collection_name: str, expr: str, output_fields: Optional[List[str]] = None,
                    limit: Optional[int] = None, offset: Optional[int] = None, **kwargs) -> List[Entity]:
        """标量查询（异步）- 带PostgreSQL资源耗尽重试机制"""
        import time
        import asyncio
        start_time = time.time()

        # PostgreSQL资源耗尽重试配置
        max_retries = 3
        base_delay = 1.0

        for retry_attempt in range(max_retries + 1):
            try:
                await self._aensure_connected()

                # 验证集合存在
                if not await self.ahas_collection(collection_name):
                    raise CollectionNotFoundError(
                        collection_name,
                        database_type=VectorDBType.PGVECTOR
                    )

                # 构建查询SQL
                query_sql = self._build_query_sql(collection_name, expr, output_fields, limit, offset)

                # 执行查询
                async with self.session_manager.get_async_cursor() as conn:
                    rows = await conn.fetch(query_sql)

                    # 转换结果
                    results = []
                    for row in rows:
                        entity = self._row_to_entity(row, output_fields)
                        results.append(entity)

                operation_time = time.time() - start_time
                self._update_stats(operation_time)

                logger.info(f"异步标量查询成功: {collection_name}, {len(results)}条记录")
                return results

            except Exception as e:
                error_str = str(e).lower()

                # 检查是否是PostgreSQL资源耗尽错误
                is_resource_exhaustion = (
                    "out of shared memory" in error_str or
                    "max_locks_per_transaction" in error_str or
                    "could not serialize access" in error_str or
                    "deadlock detected" in error_str
                )

                if is_resource_exhaustion and retry_attempt < max_retries:
                    # 计算指数退避延迟
                    delay = base_delay * (2 ** retry_attempt)
                    logger.warning(
                        f"PostgreSQL资源耗尽，第 {retry_attempt + 1}/{max_retries + 1} 次重试 "
                        f"(延迟 {delay:.1f}s): {e}"
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 非资源耗尽错误或重试次数用尽
                    if retry_attempt > 0:
                        logger.error(f"异步标量查询重试 {retry_attempt} 次后仍失败: {collection_name}, 错误: {e}")
                    else:
                        logger.error(f"异步标量查询失败: {collection_name}, 错误: {e}")
                    raise

    def batch_query(self, collection_name: str, exprs: List[str],
                   output_fields: Optional[List[str]] = None,
                   limit: Optional[int] = None, offset: Optional[int] = None,
                   **kwargs) -> List[Dict[str, Any]]:
        """批量查询数据（同步）

        Args:
            collection_name: 集合名称
            exprs: 查询表达式列表
            output_fields: 输出字段
            limit: 每个查询的结果数量限制
            offset: 偏移量
            **kwargs: 额外参数

        Returns:
            批量查询结果列表
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            if not exprs:
                return []

            # 批量执行查询
            batch_results = []

            with self.session_manager.get_cursor() as cursor:
                for i, expr in enumerate(exprs):
                    try:
                        # 构建查询SQL
                        query_sql = self._build_query_sql(collection_name, expr, output_fields, limit, offset)

                        # 执行查询
                        cursor.execute(query_sql)
                        rows = cursor.fetchall()

                        # 转换结果
                        results = []
                        for row in rows:
                            entity = self._row_to_entity(row, output_fields)
                            results.append(entity)

                        batch_results.append({
                            'expr': expr,
                            'results': results,
                            'count': len(results),
                            'success': True
                        })

                    except Exception as e:
                        batch_results.append({
                            'expr': expr,
                            'results': [],
                            'count': 0,
                            'success': False,
                            'error': str(e)
                        })
                        logger.warning(f"批量查询第{i+1}个条件失败: {expr}, 错误: {e}")

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            total_results = sum(result['count'] for result in batch_results)
            logger.info(f"批量查询成功: {collection_name}, 总计查询到{total_results}条记录")
            return batch_results

        except Exception as e:
            logger.error(f"批量查询失败: {collection_name}, 错误: {e}")
            raise

    async def abatch_query(self, collection_name: str, exprs: List[str],
                          output_fields: Optional[List[str]] = None,
                          limit: Optional[int] = None, offset: Optional[int] = None,
                          **kwargs) -> List[Dict[str, Any]]:
        """批量查询数据（异步）- 带PostgreSQL资源耗尽重试机制

        Args:
            collection_name: 集合名称
            exprs: 查询表达式列表
            output_fields: 输出字段
            limit: 每个查询的结果数量限制
            offset: 偏移量
            **kwargs: 额外参数

        Returns:
            批量查询结果列表
        """
        import time
        import asyncio
        start_time = time.time()

        # PostgreSQL资源耗尽重试配置
        max_retries = 3
        base_delay = 1.0

        for retry_attempt in range(max_retries + 1):
            try:
                await self._aensure_connected()

                # 验证集合存在
                if not await self.ahas_collection(collection_name):
                    raise CollectionNotFoundError(
                        collection_name,
                        database_type=VectorDBType.PGVECTOR
                    )

                if not exprs:
                    return []

                # 优化的并发批量查询 - 使用真正的并发执行
                # 控制并发数量，避免连接池耗尽
                max_concurrency = min(len(exprs), 20)  # 限制最大并发数
                semaphore = asyncio.Semaphore(max_concurrency)

                async def query_single(expr: str) -> Dict[str, Any]:
                    """执行单个查询"""
                    async with semaphore:
                        try:
                            async with self.session_manager.get_async_cursor() as conn:
                                # 构建查询SQL
                                query_sql = self._build_query_sql(collection_name, expr, output_fields, limit, offset)

                                # 执行查询
                                rows = await conn.fetch(query_sql)

                                # 转换结果
                                results = []
                                for row in rows:
                                    entity = self._row_to_entity(row, output_fields)
                                    results.append(entity)

                                return {
                                    'expr': expr,
                                    'results': results,
                                    'count': len(results),
                                    'success': True
                                }

                        except Exception as e:
                            return {
                                'expr': expr,
                                'results': [],
                                'count': 0,
                                'success': False,
                                'error': str(e)
                            }

                # 并发执行所有查询
                tasks = [query_single(expr) for expr in exprs]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理异常结果
                processed_results = []
                for i, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        processed_results.append({
                            'expr': exprs[i],
                            'results': [],
                            'count': 0,
                            'success': False,
                            'error': str(result)
                        })
                        logger.warning(f"异步批量查询第{i+1}个条件失败: {exprs[i]}, 错误: {result}")
                    else:
                        processed_results.append(result)

                batch_results = processed_results

                operation_time = time.time() - start_time
                self._update_stats(operation_time)

                total_results = sum(result['count'] for result in batch_results)
                logger.info(f"异步批量查询成功: {collection_name}, 总计查询到{total_results}条记录")
                return batch_results

            except Exception as e:
                error_str = str(e).lower()

                # 检查是否是PostgreSQL资源耗尽错误
                is_resource_exhaustion = (
                    "out of shared memory" in error_str or
                    "max_locks_per_transaction" in error_str or
                    "could not serialize access" in error_str or
                    "deadlock detected" in error_str
                )

                if is_resource_exhaustion and retry_attempt < max_retries:
                    # 计算指数退避延迟
                    delay = base_delay * (2 ** retry_attempt)
                    logger.warning(
                        f"PostgreSQL资源耗尽，第 {retry_attempt + 1}/{max_retries + 1} 次重试 "
                        f"(延迟 {delay:.1f}s): {e}"
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 非资源耗尽错误或重试次数用尽
                    if retry_attempt > 0:
                        logger.error(f"异步批量查询重试 {retry_attempt} 次后仍失败: {collection_name}, 错误: {e}")
                    else:
                        logger.error(f"异步批量查询失败: {collection_name}, 错误: {e}")
                    raise

    def get(self, collection_name: str, ids: List[Any], output_fields: Optional[List[str]] = None,
            **kwargs) -> List[Entity]:
        """根据ID获取数据（同步）

        Args:
            collection_name: 集合名称
            ids: ID列表
            output_fields: 输出字段
            **kwargs: 额外参数

        Returns:
            实体列表
        """
        if not ids:
            return []

        # 构建ID查询表达式
        id_list = ', '.join([f"'{id_}'" if isinstance(id_, str) else str(id_) for id_ in ids])
        expr = f"id in ({id_list})"

        return self.query(collection_name, expr, output_fields, **kwargs)

    async def aget(self, collection_name: str, ids: List[Any], output_fields: Optional[List[str]] = None,
                  **kwargs) -> List[Entity]:
        """根据ID获取数据（异步）"""
        if not ids:
            return []

        # 构建ID查询表达式
        id_list = ', '.join([f"'{id_}'" if isinstance(id_, str) else str(id_) for id_ in ids])
        expr = f"id in ({id_list})"

        return await self.aquery(collection_name, expr, output_fields, **kwargs)

    # ==================== 混合搜索 ====================

    def hybrid_search(self, collection_name: str, reqs: List[SearchRequest],
                     ranker: Any, limit: int = 10, min_score: Optional[float] = None, **kwargs) -> List[SearchResult]:
        """混合搜索（同步）

        Args:
            collection_name: 集合名称
            reqs: 搜索请求列表
            ranker: 排序器
            limit: 最终返回结果数量
            min_score: 最小相似度分数阈值
            **kwargs: 额外参数

        Returns:
            混合搜索结果
        """
        import time
        start_time = time.time()

        try:
            self._ensure_connected()

            # 验证集合存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 执行多个搜索请求
            all_results = []
            for req in reqs:
                # 转换SearchRequest为search参数
                search_results = self.search(
                    collection_name=collection_name,
                    data=req.data,
                    anns_field=req.anns_field,
                    param=req.param,
                    limit=req.limit,
                    expr=req.expr,
                    min_score=min_score  # 🔧 传递min_score参数
                )

                # 展平结果（每个查询向量的结果）
                for vector_results in search_results:
                    all_results.extend(vector_results)

            # 使用ranker进行排序
            if hasattr(ranker, 'rank'):
                ranked_results = ranker.rank(all_results, limit)
            else:
                # 简单排序：按距离排序
                all_results.sort(key=lambda x: x.distance)
                ranked_results = all_results[:limit]

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            logger.info(f"混合搜索成功: {collection_name}, {len(reqs)}个请求, {len(ranked_results)}个结果")
            return ranked_results

        except Exception as e:
            logger.error(f"混合搜索失败: {collection_name}, 错误: {e}")
            raise

    async def ahybrid_search(self, collection_name: str, reqs: List[SearchRequest],
                            ranker: Any, limit: int = 10, min_score: Optional[float] = None, **kwargs) -> List[SearchResult]:
        """混合搜索（异步）"""
        import time
        start_time = time.time()

        try:
            await self._aensure_connected()

            # 验证集合存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 执行多个搜索请求
            search_results_list = []
            for req in reqs:
                # 转换SearchRequest为search参数
                search_results = await self.asearch(
                    collection_name=collection_name,
                    data=req.data,
                    anns_field=req.anns_field,
                    param=req.param,
                    limit=req.limit,
                    expr=req.expr,
                    min_score=min_score  # 🔧 传递min_score参数
                )

                # 展平结果并转换为字典格式
                flattened_results = []
                for vector_results in search_results:
                    for result in vector_results:
                        # 转换SearchResult为字典格式
                        # 优先使用SQL计算的similarity_score，如果没有则使用客户端计算
                        if hasattr(result.entity, 'data') and 'similarity_score' in result.entity.data:
                            cosine_similarity = result.entity.data['similarity_score']
                        else:
                            cosine_similarity = max(0.0, min(1.0, 1.0 - result.distance))

                        result_dict = {
                            'id': result.id,
                            'distance': result.distance,
                            'score': cosine_similarity,
                            'entity': result.entity or {}
                        }
                        flattened_results.append(result_dict)

                search_results_list.append(flattened_results)

            # 使用ranker进行排序
            if hasattr(ranker, 'rank'):
                # ranker期望List[List[Dict]]格式
                ranked_results_dicts = ranker.rank(search_results_list, limit)
                # 转换回SearchResult格式
                ranked_results = []
                for result_dict in ranked_results_dicts:
                    # 创建SearchResult对象
                    from base.db.base.vdb.core.models import SearchResult
                    search_result = SearchResult(
                        id=result_dict['id'],
                        distance=result_dict['distance'],
                        entity=result_dict.get('entity', {})
                    )
                    ranked_results.append(search_result)
            else:
                # 简单排序：合并所有结果并按距离排序
                all_results = []
                for results in search_results_list:
                    all_results.extend(results)
                all_results.sort(key=lambda x: x['distance'])

                # 转换为SearchResult格式
                ranked_results = []
                for result_dict in all_results[:limit]:
                    from base.db.base.vdb.core.models import SearchResult
                    search_result = SearchResult(
                        id=result_dict['id'],
                        distance=result_dict['distance'],
                        entity=result_dict.get('entity', {})
                    )
                    ranked_results.append(search_result)

            operation_time = time.time() - start_time
            self._update_stats(operation_time)

            logger.info(f"异步混合搜索成功: {collection_name}, {len(reqs)}个请求, {len(ranked_results)}个结果")
            return ranked_results

        except Exception as e:
            logger.error(f"异步混合搜索失败: {collection_name}, 错误: {e}")
            raise

    # ==================== SQL辅助方法 ====================

    def _convert_vector_to_pg_format(self, value: Any) -> Any:
        """将向量数据转换为PostgreSQL vector格式

        Args:
            value: 要转换的值

        Returns:
            转换后的值
        """
        if value is None:
            return None

        try:
            # 检查是否为向量数据
            if isinstance(value, list) and len(value) > 0:
                # 检查是否所有元素都是数字
                if all(isinstance(x, (int, float)) for x in value):
                    # 转换为PostgreSQL vector格式: '[1,2,3]'
                    return '[' + ','.join(map(str, value)) + ']'

            # 检查numpy数组
            elif hasattr(value, 'dtype') and hasattr(value, 'tolist'):
                # 转换numpy数组为列表，然后转换为PostgreSQL格式
                vector_list = value.tolist()
                return '[' + ','.join(map(str, vector_list)) + ']'

            # 非向量数据，直接返回
            return value

        except Exception as e:
            value_str = str(value)
            value_preview = value_str[:100] if len(value_str) > 100 else value_str
            logger.warning(f"向量转换失败: {e}, 值类型: {type(value)}, 值: {value_preview}")
            return value

    def _build_insert_sql(self, table_name: str, data: List[Dict[str, Any]],
                         schema: CollectionSchema) -> tuple:
        """构建批量插入SQL（支持RETURNING优化）"""
        if not data:
            return "", []

        try:
            # 获取字段列表
            fields = list(data[0].keys())
            logger.debug(f"同步插入字段列表: {fields}")
            field_names = ', '.join([f'"{field}"' for field in fields])
            logger.debug(f"同步插入字段名: {field_names}")
        except Exception as e:
            logger.error(f"获取字段列表失败: {e}, 数据: {data}")
            raise

        # 检查是否需要RETURNING子句
        needs_returning = False
        returning_fields = ""
        try:
            primary_fields = schema.get_primary_fields()
            auto_id_fields = [field for field in primary_fields if field.auto_id]
            if auto_id_fields:
                needs_returning = True
                returning_fields = ', '.join(f'"{field.name}"' for field in auto_id_fields)
        except ValueError:
            # 没有主键字段，跳过RETURNING子句
            pass

        # 构建批量VALUES子句
        values_placeholders = []
        all_values = []

        try:
            for i, record in enumerate(data):
                logger.debug(f"处理记录 {i+1}: {list(record.keys())}")

                # 为每条记录构建占位符
                row_placeholders = ', '.join(['%s'] * len(fields))
                values_placeholders.append(f"({row_placeholders})")

                # 处理每个字段的值
                for field in fields:
                    value = record.get(field)
                    # 使用统一的向量转换函数
                    converted_value = self._convert_vector_to_pg_format(value)
                    all_values.append(converted_value)

        except Exception as e:
            logger.error(f"构建VALUES子句失败: {e}, 记录索引: {i if 'i' in locals() else 'unknown'}")
            raise

        # 构建完整的批量INSERT SQL
        try:
            values_clause = ', '.join(values_placeholders)
            sql = f'INSERT INTO "{table_name}" ({field_names}) VALUES {values_clause}'
            logger.debug(f"基础SQL构建完成，长度: {len(sql)}")

            # 添加RETURNING子句（如果需要）
            if needs_returning:
                sql += f' RETURNING {returning_fields}'
                logger.debug(f"添加RETURNING子句后SQL长度: {len(sql)}")

            logger.debug(f"最终SQL参数数量: {len(all_values)}")
            return sql, all_values

        except Exception as e:
            logger.error(f"构建最终SQL失败: {e}")
            logger.error(f"values_placeholders数量: {len(values_placeholders) if 'values_placeholders' in locals() else 'unknown'}")
            logger.error(f"all_values数量: {len(all_values) if 'all_values' in locals() else 'unknown'}")
            raise

    def _build_async_insert_sql(self, table_name: str, data: List[Dict[str, Any]],
                               schema: CollectionSchema) -> tuple:
        """构建异步批量插入SQL（使用$1, $2格式占位符，支持RETURNING优化）"""
        if not data:
            return "", []

        # 获取字段列表
        fields = list(data[0].keys())
        field_names = ', '.join([f'"{field}"' for field in fields])

        # 检查是否需要RETURNING子句
        needs_returning = False
        returning_fields = ""
        try:
            primary_fields = schema.get_primary_fields()
            auto_id_fields = [field for field in primary_fields if field.auto_id]
            if auto_id_fields:
                needs_returning = True
                returning_fields = ', '.join(f'"{field.name}"' for field in auto_id_fields)
        except ValueError:
            # 没有主键字段，跳过RETURNING子句
            pass

        # 构建批量VALUES子句（asyncpg使用$1, $2格式占位符）
        values_placeholders = []
        all_values = []
        placeholder_index = 1

        for record in data:
            # 为每条记录构建占位符
            row_placeholders = []
            for field in fields:
                row_placeholders.append(f'${placeholder_index}')
                placeholder_index += 1

            values_placeholders.append(f"({', '.join(row_placeholders)})")

            # 处理每个字段的值
            for field in fields:
                value = record.get(field)
                # 使用统一的向量转换函数
                converted_value = self._convert_vector_to_pg_format(value)
                all_values.append(converted_value)

        # 构建完整的批量INSERT SQL
        try:
            values_clause = ', '.join(values_placeholders)
            sql = f'INSERT INTO "{table_name}" ({field_names}) VALUES {values_clause}'
            logger.debug(f"基础SQL构建完成，长度: {len(sql)}")

            # 添加RETURNING子句（如果需要）
            if needs_returning:
                sql += f' RETURNING {returning_fields}'
                logger.debug(f"添加RETURNING子句后SQL长度: {len(sql)}")

            logger.debug(f"最终SQL参数数量: {len(all_values)}")
            return sql, all_values

        except Exception as e:
            logger.error(f"构建最终SQL失败: {e}")
            logger.error(f"values_placeholders数量: {len(values_placeholders) if 'values_placeholders' in locals() else 'unknown'}")
            logger.error(f"all_values数量: {len(all_values) if 'all_values' in locals() else 'unknown'}")
            raise

    def _build_update_sql(self, table_name: str, data: Dict[str, Any], expr: str,
                         schema: CollectionSchema) -> tuple:
        """构建UPDATE SQL语句（同步版本）"""
        if not data:
            return "", []

        try:
            # 构建SET子句
            set_clauses = []
            values = []

            for field, value in data.items():
                # 使用统一的向量转换函数
                converted_value = self._convert_vector_to_pg_format(value)
                set_clauses.append(f'"{field}" = %s')
                values.append(converted_value)

            # 构建WHERE子句
            where_clause = self._expr_to_where(expr)

            # 构建完整的UPDATE SQL
            sql = f'UPDATE "{table_name}" SET {", ".join(set_clauses)} WHERE {where_clause}'

            logger.debug(f"同步更新SQL: {sql}")
            logger.debug(f"更新参数数量: {len(values)}")

            return sql, values

        except Exception as e:
            logger.error(f"构建同步更新SQL失败: {e}")
            raise

    def _build_async_update_sql(self, table_name: str, data: Dict[str, Any], expr: str,
                               schema: CollectionSchema) -> tuple:
        """构建UPDATE SQL语句（异步版本）"""
        if not data:
            return "", []

        try:
            # 构建SET子句（asyncpg使用$1, $2格式占位符）
            set_clauses = []
            values = []
            placeholder_index = 1

            for field, value in data.items():
                # 使用统一的向量转换函数
                converted_value = self._convert_vector_to_pg_format(value)
                set_clauses.append(f'"{field}" = ${placeholder_index}')
                values.append(converted_value)
                placeholder_index += 1

            # 构建WHERE子句
            where_clause = self._expr_to_where(expr)

            # 构建完整的UPDATE SQL
            sql = f'UPDATE "{table_name}" SET {", ".join(set_clauses)} WHERE {where_clause}'

            logger.debug(f"异步更新SQL: {sql}")
            logger.debug(f"更新参数数量: {len(values)}")

            return sql, values

        except Exception as e:
            logger.error(f"构建异步更新SQL失败: {e}")
            raise

    def _build_search_sql(self, table_name: str, query_vector: List[float],
                         vector_field: str, param: Dict[str, Any], limit: int,
                         expr: Optional[str] = None, output_fields: Optional[List[str]] = None,
                         min_score: Optional[float] = None) -> str:
        """构建搜索SQL"""
        # 确定输出字段
        if output_fields:
            select_fields = ', '.join([f'"{field}"' for field in output_fields])
        else:
            select_fields = '*'

        # 获取距离度量
        metric_type = param.get('metric_type', 'cosine').lower()
        if metric_type == 'cosine':
            distance_op = '<=>'
        elif metric_type == 'l2':
            distance_op = '<->'
        elif metric_type == 'ip':
            distance_op = '<#>'
        else:
            distance_op = '<=>'  # 默认余弦距离

        # 验证和警告无效参数
        if 'params' in param:
            unsupported_params = param['params']
            if unsupported_params:
                logger.warning(f"PostgreSQL pgvector不支持以下参数，将被忽略: {unsupported_params}")
                logger.warning("pgvector使用基于索引的精确搜索，不需要nprobe等近似搜索参数")

        # 构建向量字符串
        vector_str = f"[{','.join(map(str, query_vector))}]"

        # 构建SQL - 参考标准pgvector实现
        sql = f'''
        SELECT {select_fields},
               "{vector_field}" {distance_op} '{vector_str}' AS distance,
               1 - ("{vector_field}" {distance_op} '{vector_str}') AS similarity_score
        FROM "{table_name}"
        '''

        # 构建WHERE条件列表
        where_conditions = []

        # 添加原有的过滤条件
        if expr:
            where_conditions.append(self._expr_to_where(expr))

        # 🔧 关键修复：在SQL层面添加min_score过滤
        if min_score is not None:
            where_conditions.append(f'1 - ("{vector_field}" {distance_op} \'{vector_str}\') >= {min_score}')

        # 组合WHERE条件
        if where_conditions:
            sql += f' WHERE {" AND ".join(where_conditions)}'

        # 添加排序和限制 - 按距离升序排序（距离越小越相似）
        sql += f' ORDER BY distance ASC LIMIT {limit}'

        # 详细日志记录
        logger.debug(f"构建向量搜索SQL:")
        logger.debug(f"  表名: {table_name}")
        logger.debug(f"  向量字段: {vector_field}")
        logger.debug(f"  距离度量: {metric_type} ({distance_op})")
        logger.debug(f"  向量维度: {len(query_vector)}")
        logger.debug(f"  限制数量: {limit}")
        logger.debug(f"  最小分数: {min_score}")
        logger.debug(f"  过滤条件: {expr}")
        logger.debug(f"  生成的SQL: {sql}")

        return sql

    def _build_query_sql(self, table_name: str, expr: str, output_fields: Optional[List[str]] = None,
                        limit: Optional[int] = None, offset: Optional[int] = None) -> str:
        """构建查询SQL"""
        # 确定输出字段
        if output_fields:
            select_fields = ', '.join([f'"{field}"' for field in output_fields])
        else:
            select_fields = '*'

        # 构建SQL
        sql = f'SELECT {select_fields} FROM "{table_name}"'

        # 添加WHERE条件
        if expr:
            where_clause = self._expr_to_where(expr)
            sql += f' WHERE {where_clause}'

        # 添加LIMIT和OFFSET
        if limit:
            sql += f' LIMIT {limit}'
        if offset:
            sql += f' OFFSET {offset}'

        return sql

    def _expr_to_where(self, expr: str) -> str:
        """将Milvus表达式转换为PostgreSQL WHERE子句"""
        # 简单的表达式转换
        # 这里可以根据需要实现更复杂的表达式解析

        # 替换常见的操作符
        where_clause = expr
        where_clause = where_clause.replace(' and ', ' AND ')
        where_clause = where_clause.replace(' or ', ' OR ')
        where_clause = where_clause.replace(' not ', ' NOT ')
        where_clause = where_clause.replace(' in ', ' IN ')
        where_clause = where_clause.replace(' like ', ' LIKE ')

        return where_clause

    def _build_exact_conditions_expr(self, conditions: Dict[str, Any]) -> str:
        """构建精确查询条件表达式"""
        if not conditions:
            return "1=1"  # 无条件时返回恒真表达式

        expr_parts = []
        for field, value in conditions.items():
            if isinstance(value, list):
                # 处理IN查询
                if all(isinstance(v, str) for v in value):
                    value_list = "', '".join(value)
                    expr_parts.append(f'"{field}" IN (\'{value_list}\')')
                else:
                    value_list = ", ".join(str(v) for v in value)
                    expr_parts.append(f'"{field}" IN ({value_list})')
            elif isinstance(value, str):
                expr_parts.append(f'"{field}" = \'{value}\'')
            else:
                expr_parts.append(f'"{field}" = {value}')

        return " AND ".join(expr_parts)

    def _build_fuzzy_conditions_expr(self, conditions: Dict[str, str], case_sensitive: bool = False) -> str:
        """构建模糊查询条件表达式"""
        if not conditions:
            return "1=1"  # 无条件时返回恒真表达式

        expr_parts = []
        like_op = "LIKE" if case_sensitive else "ILIKE"

        for field, pattern in conditions.items():
            # 自动添加通配符，支持部分匹配
            if not pattern.startswith('%') and not pattern.endswith('%'):
                pattern = f"%{pattern}%"
            expr_parts.append(f'"{field}" {like_op} \'{pattern}\'')

        return " OR ".join(expr_parts)  # 使用OR连接，任一字段匹配即可

    def _row_to_search_result(self, row, output_fields: Optional[List[str]] = None) -> SearchResult:
        """将数据库行转换为SearchResult"""
        # 处理RealDictRow对象或tuple
        if hasattr(row, 'get'):
            # RealDictRow对象
            distance = row.get('distance')

            # 🔧 验证distance值
            if distance is None:
                logger.warning(f"数据库返回的distance字段为None，使用默认值1.0")
                distance = 1.0  # 使用1.0而不是0.0作为默认值
            elif distance == 0.0:
                logger.warning(f"发现异常的distance=0.0，这表示完美匹配，请检查数据")
                logger.warning(f"行数据: {dict(row)}")
            elif distance < 0 or distance > 2:
                logger.warning(f"异常的distance值: {distance}，余弦距离应该在[0,2]范围内")

            entity_dict = {}
            entity_id = None

            if output_fields:
                for field in output_fields:
                    value = row.get(field)
                    entity_dict[field] = value
                    if entity_id is None:  # 假设第一个字段是ID
                        entity_id = value
            else:
                # 获取所有字段（除了distance和similarity_score）
                for key, value in row.items():
                    if key not in ('distance', 'similarity_score'):
                        entity_dict[key] = value
                        if entity_id is None:  # 假设第一个字段是ID
                            entity_id = value
        else:
            # tuple对象
            *entity_data, distance = row

            # 构建实体数据
            if output_fields:
                entity_dict = dict(zip(output_fields, entity_data))
            else:
                # 如果没有指定字段，使用默认字段名
                entity_dict = {'data': entity_data}

            # 获取ID（假设第一列是ID）
            entity_id = entity_data[0] if entity_data else None

        return SearchResult(
            id=entity_id,
            distance=float(distance),
            entity=Entity(data=entity_dict)
        )

    def _row_to_entity(self, row, output_fields: Optional[List[str]] = None) -> Entity:
        """将数据库行转换为Entity"""
        # 处理RealDictRow对象或tuple
        if hasattr(row, 'get'):
            # RealDictRow对象
            entity_dict = {}
            if output_fields:
                for field in output_fields:
                    entity_dict[field] = row.get(field)
            else:
                # 获取所有字段
                entity_dict = dict(row.items())
        else:
            # tuple对象
            if output_fields:
                entity_dict = dict(zip(output_fields, row))
            else:
                # 如果没有指定字段，使用默认字段名
                entity_dict = {'data': row}

        return Entity(data=entity_dict)

    def _combine_vector_exact_results(
        self,
        vector_results: List[SearchResult],
        exact_results: List[SearchResult],
        vector_weight: float,
        exact_weight: float
    ) -> List[SearchResult]:
        """融合向量搜索和精确搜索结果"""
        combined_results = []
        result_map = {}  # 用于去重和合并

        # 处理向量搜索结果
        for result in vector_results:
            result_id = result.id
            # 归一化向量距离分数 (距离越小分数越高)
            vector_score = max(0, 1.0 - result.distance)
            combined_score = vector_score * vector_weight

            # 添加详细信息到entity
            result.entity.data['vector_score'] = vector_score
            result.entity.data['exact_score'] = 0.0
            result.entity.data['combined_score'] = combined_score
            result.entity.data['search_type'] = 'vector'

            result_map[result_id] = result

        # 处理精确搜索结果
        for result in exact_results:
            result_id = result.id
            exact_score = 1.0  # 精确匹配给满分

            if result_id in result_map:
                # 已存在的结果，更新分数
                existing_result = result_map[result_id]
                existing_result.entity.data['exact_score'] = exact_score
                existing_result.entity.data['combined_score'] += exact_score * exact_weight
                existing_result.entity.data['search_type'] = 'both'
            else:
                # 新结果
                combined_score = exact_score * exact_weight
                result.entity.data['vector_score'] = 0.0
                result.entity.data['exact_score'] = exact_score
                result.entity.data['combined_score'] = combined_score
                result.entity.data['search_type'] = 'exact'
                result_map[result_id] = result

        return list(result_map.values())

    def _combine_vector_fuzzy_results(
        self,
        vector_results: List[SearchResult],
        fuzzy_results: List[SearchResult],
        vector_weight: float,
        fuzzy_weight: float
    ) -> List[SearchResult]:
        """融合向量搜索和模糊搜索结果"""
        combined_results = []
        result_map = {}  # 用于去重和合并

        # 处理向量搜索结果
        for result in vector_results:
            result_id = result.id
            # 归一化向量距离分数 (距离越小分数越高)
            vector_score = max(0, 1.0 - result.distance)
            combined_score = vector_score * vector_weight

            # 添加详细信息到entity
            result.entity.data['vector_score'] = vector_score
            result.entity.data['fuzzy_score'] = 0.0
            result.entity.data['combined_score'] = combined_score
            result.entity.data['search_type'] = 'vector'

            result_map[result_id] = result

        # 处理模糊搜索结果
        for result in fuzzy_results:
            result_id = result.id
            # 模糊搜索分数 (距离越小分数越高)
            fuzzy_score = max(0, 1.0 - result.distance)

            if result_id in result_map:
                # 已存在的结果，更新分数
                existing_result = result_map[result_id]
                existing_result.entity.data['fuzzy_score'] = fuzzy_score
                existing_result.entity.data['combined_score'] += fuzzy_score * fuzzy_weight
                existing_result.entity.data['search_type'] = 'both'
            else:
                # 新结果
                combined_score = fuzzy_score * fuzzy_weight
                result.entity.data['vector_score'] = 0.0
                result.entity.data['fuzzy_score'] = fuzzy_score
                result.entity.data['combined_score'] = combined_score
                result.entity.data['search_type'] = 'fuzzy'
                result_map[result_id] = result

        return list(result_map.values())

    def _calculate_text_similarity(self, entity: Entity, fuzzy_conditions: Dict[str, str]) -> float:
        """计算文本相似度分数（简单实现）"""
        if not fuzzy_conditions:
            return 0.0

        total_score = 0.0
        matched_fields = 0

        for field, pattern in fuzzy_conditions.items():
            field_value = entity.data.get(field, "")
            if isinstance(field_value, str):
                # 简单的包含匹配计算相似度
                pattern_clean = pattern.strip('%').lower()
                field_clean = field_value.lower()

                if pattern_clean in field_clean:
                    # 计算匹配度：匹配长度 / 字段总长度
                    if len(field_clean) > 0:
                        similarity = len(pattern_clean) / len(field_clean)
                        total_score += min(similarity, 1.0)
                        matched_fields += 1

        # 返回平均相似度
        return total_score / len(fuzzy_conditions) if fuzzy_conditions else 0.0

    # ==================== 内部辅助方法 ====================

    def _validate_vector_format(self, vector: Any, field_name: str = "vector") -> None:
        """验证向量格式

        Args:
            vector: 要验证的向量
            field_name: 字段名称，用于错误提示

        Raises:
            ValueError: 向量格式不正确时抛出
        """
        if vector is None:
            raise ValueError(f"向量字段 '{field_name}' 不能为空")

        # 检查是否为numpy数组
        if hasattr(vector, 'dtype') and hasattr(vector, 'tolist'):
            raise ValueError(
                f"向量字段 '{field_name}' 不支持numpy.ndarray格式。"
                f"请在embedding层使用 .tolist() 方法转换为List[float]格式。"
                f"例如: vector.tolist()"
            )

        # 检查是否为列表
        if not isinstance(vector, list):
            raise ValueError(
                f"向量字段 '{field_name}' 必须是List[float]格式，当前类型: {type(vector).__name__}"
            )

        # 检查列表是否为空
        if len(vector) == 0:
            raise ValueError(f"向量字段 '{field_name}' 不能为空列表")

        # 检查元素类型
        if not all(isinstance(x, (int, float)) for x in vector):
            raise ValueError(
                f"向量字段 '{field_name}' 的所有元素必须是数字类型(int/float)"
            )

    def _validate_search_vectors(self, data: Any) -> None:
        """验证搜索向量格式

        Args:
            data: 搜索向量数据

        Raises:
            ValueError: 向量格式不正确时抛出
        """
        if not isinstance(data, list):
            raise ValueError(
                f"搜索向量必须是List[List[float]]格式，当前类型: {type(data).__name__}"
            )

        if len(data) == 0:
            raise ValueError("搜索向量列表不能为空")

        for i, vector in enumerate(data):
            self._validate_vector_format(vector, f"search_vector[{i}]")

    def _validate_insert_data(self, data: List[Dict[str, Any]]) -> None:
        """验证插入数据格式

        Args:
            data: 要插入的数据列表

        Raises:
            ValueError: 数据格式不正确时抛出
        """
        if not isinstance(data, list):
            raise ValueError(
                f"插入数据必须是List[Dict[str, Any]]格式，当前类型: {type(data).__name__}"
            )

        if len(data) == 0:
            raise ValueError("插入数据列表不能为空")

        # 检查每条记录
        for i, record in enumerate(data):
            if not isinstance(record, dict):
                raise ValueError(
                    f"插入数据的第{i+1}条记录必须是字典格式，当前类型: {type(record).__name__}"
                )

            # 检查记录中的向量字段
            for field_name, value in record.items():
                # 简单启发式检测：如果是列表且第一个元素是数字，认为是向量
                if (isinstance(value, list) and len(value) > 0 and
                    isinstance(value[0], (int, float))):
                    self._validate_vector_format(value, f"record[{i+1}].{field_name}")
                # 检查numpy数组
                elif hasattr(value, 'dtype') and hasattr(value, 'tolist'):
                    raise ValueError(
                        f"记录{i+1}的字段'{field_name}'是numpy.ndarray格式。"
                        f"请在embedding层使用 .tolist() 方法转换为List[float]格式。"
                    )
    
    def _ensure_connected(self) -> None:
        """确保同步连接已建立 - 如果未连接则自动连接"""
        if not self.is_connected:
            logger.info("检测到同步连接未建立，正在自动建立同步连接...")
            try:
                self.connect()
                logger.info("自动同步连接成功")
            except Exception as e:
                logger.error(f"自动同步连接失败: {e}")
                raise ConnectionError(
                    f"无法建立同步数据库连接: {e}",
                    database_type=VectorDBType.PGVECTOR
                ) from e

    async def _aensure_connected(self) -> None:
        """确保异步连接已建立 - 如果未连接则自动连接（并发安全）"""
        # 使用异步锁防止多个任务同时初始化连接
        async with self._connection_lock:
            if not self.is_aconnected:
                logger.info("检测到异步连接未建立，正在自动建立异步连接...")
                try:
                    await self.aconnect()
                    logger.info("自动异步连接成功")
                except Exception as e:
                    logger.error(f"自动异步连接失败: {e}")
                    raise ConnectionError(
                        f"无法建立异步数据库连接: {e}",
                        database_type=VectorDBType.PGVECTOR
                    ) from e
            else:
                # 连接已存在，进行健康检查
                try:
                    async with self.session_manager.get_async_cursor() as conn:
                        await conn.fetchval("SELECT 1")
                except Exception as e:
                    logger.warning(f"异步连接健康检查失败，重新连接: {e}")
                    try:
                        await self.session_manager.adisconnect()
                    except:
                        pass  # 忽略断开连接时的错误
                    await self.aconnect()

    async def awarm_up_for_batch_operations(self, expected_concurrency: int = 5) -> None:
        """
        为批量操作预热连接池

        Args:
            expected_concurrency: 预期的并发数量
        """
        try:
            # 确保连接已建立
            await self._aensure_connected()

            # 记录预热前的连接池状态
            if hasattr(self.session_manager, 'log_connection_pool_status'):
                self.session_manager.log_connection_pool_status("预热前")

            # 预热连接池
            if hasattr(self.session_manager, 'awarm_up_connections'):
                await self.session_manager.awarm_up_connections(expected_concurrency)
                logger.info(f"连接池预热完成，预期并发数: {expected_concurrency}")

                # 记录预热后的连接池状态
                if hasattr(self.session_manager, 'log_connection_pool_status'):
                    self.session_manager.log_connection_pool_status("预热后")
            else:
                logger.debug("连接管理器不支持预热功能")

        except Exception as e:
            logger.warning(f"连接池预热失败: {e}")
            # 预热失败不应该阻止正常操作
    
    def _update_stats(self, operation_time: float) -> None:
        """更新操作统计"""
        self._stats['operations_count'] += 1
        self._stats['last_operation_time'] = operation_time
        self._stats['total_operation_time'] += operation_time

    async def _execute_with_retry(self, operation_name: str, operation_func, *args, **kwargs):
        """
        执行数据库操作，带PostgreSQL资源耗尽重试机制

        Args:
            operation_name: 操作名称，用于日志记录
            operation_func: 要执行的异步操作函数
            *args, **kwargs: 传递给操作函数的参数

        Returns:
            操作函数的返回值

        Raises:
            重试后仍失败的异常
        """
        import asyncio

        # PostgreSQL资源耗尽重试配置
        max_retries = 3
        base_delay = 1.0

        for retry_attempt in range(max_retries + 1):
            try:
                return await operation_func(*args, **kwargs)

            except Exception as e:
                error_str = str(e).lower()

                # 检查是否是PostgreSQL资源耗尽错误
                is_resource_exhaustion = (
                    "out of shared memory" in error_str or
                    "max_locks_per_transaction" in error_str or
                    "could not serialize access" in error_str or
                    "deadlock detected" in error_str or
                    "connection pool exhausted" in error_str or
                    "too many connections" in error_str
                )

                if is_resource_exhaustion and retry_attempt < max_retries:
                    # 计算指数退避延迟
                    delay = base_delay * (2 ** retry_attempt)
                    logger.warning(
                        f"PostgreSQL资源耗尽，{operation_name} 第 {retry_attempt + 1}/{max_retries + 1} 次重试 "
                        f"(延迟 {delay:.1f}s): {e}"
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 非资源耗尽错误或重试次数用尽
                    if retry_attempt > 0:
                        logger.error(f"{operation_name} 重试 {retry_attempt} 次后仍失败: {e}")
                    else:
                        logger.error(f"{operation_name} 失败: {e}")
                    raise
    
    # ==================== 上下文管理器支持 ====================
    
    def __enter__(self):
        """同步上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        self.disconnect()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.aconnect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.adisconnect()
    
    # ==================== 统计和监控 ====================
    
    def get_client_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        return {
            'client_stats': self._stats.copy(),
            'connection_stats': self.connection_manager.get_connection_stats(),
            'collection_stats': self.collection_manager.get_operation_stats(),
            'database_type': VectorDBType.PGVECTOR.value,
            'config': {
                'host': self.config.host,
                'port': self.config.port,
                'database': self.config.db_name
            }
        }
    
    # ==================== 字符串表示 ====================
    
    def __str__(self) -> str:
        """字符串表示"""
        status = "connected" if self.is_connected else "disconnected"
        return f"PGVectorClient({self.config.host}:{self.config.port}, {status})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"PGVectorClient(host='{self.config.host}', port={self.config.port}, "
                f"database='{self.config.database}', connected={self.is_connected})")

    # ==================== 批处理优化辅助方法 ====================

    def _group_updates_by_structure(self, updates: List[Tuple[int, Dict[str, Any]]]) -> Dict[str, List]:
        """
        按更新结构分组以优化批处理

        Args:
            updates: 更新项列表，每个元素为 (原始索引, 更新项)

        Returns:
            按结构分组的更新字典
        """
        groups = {}
        for original_index, update_item in updates:
            data = update_item['data']

            # 只基于更新字段结构分组，不考虑具体的条件表达式
            # 这样相同字段结构的更新会被分到同一组，便于批处理
            data_keys = tuple(sorted(data.keys()))
            group_key = str(data_keys)  # 简化分组键

            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append((original_index, update_item))

        return groups

    def _execute_batch_update_chunk(self, cursor, collection_name: str,
                                  chunk_updates: List[Dict[str, Any]],
                                  schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        执行一个批次的更新操作（同步版本）

        Args:
            cursor: 数据库游标
            collection_name: 集合名称
            chunk_updates: 批次更新列表
            schema: 集合模式

        Returns:
            批次执行结果列表
        """
        if not chunk_updates:
            return []

        chunk_results = []

        # 在单个事务中执行整个批次
        for update_item in chunk_updates:
            try:
                data = update_item['data']
                expr = update_item['expr']

                if not data:
                    chunk_results.append({
                        'data': data,
                        'expr': expr,
                        'update_count': 0,
                        'success': True
                    })
                    continue

                # 构建并执行更新SQL
                update_sql, values = self._build_update_sql(collection_name, data, expr, schema)
                cursor.execute(update_sql, values)
                update_count = cursor.rowcount

                chunk_results.append({
                    'data': data,
                    'expr': expr,
                    'update_count': update_count,
                    'success': True
                })

            except Exception as e:
                chunk_results.append({
                    'data': update_item.get('data', {}),
                    'expr': update_item.get('expr', ''),
                    'update_count': 0,
                    'success': False,
                    'error': str(e)
                })
                logger.warning(f"批次更新操作失败: {e}")

        return chunk_results

    async def _execute_async_batch_update_chunk(self, conn, collection_name: str,
                                              chunk_updates: List[Dict[str, Any]],
                                              schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        异步执行一个批次的更新操作

        Args:
            conn: 异步数据库连接
            collection_name: 集合名称
            chunk_updates: 批次更新列表
            schema: 集合模式

        Returns:
            批次执行结果列表
        """
        if not chunk_updates:
            return []

        chunk_results = []

        # 在单个事务中执行整个批次
        async with conn.transaction():
            for update_item in chunk_updates:
                try:
                    data = update_item['data']
                    expr = update_item['expr']

                    if not data:
                        chunk_results.append({
                            'data': data,
                            'expr': expr,
                            'update_count': 0,
                            'success': True
                        })
                        continue

                    # 构建并执行异步更新SQL
                    update_sql, values = self._build_async_update_sql(collection_name, data, expr, schema)
                    result = await conn.execute(update_sql, *values)
                    update_count = int(result.split()[-1]) if result.split() else 0

                    chunk_results.append({
                        'data': data,
                        'expr': expr,
                        'update_count': update_count,
                        'success': True
                    })

                except Exception as e:
                    chunk_results.append({
                        'data': update_item.get('data', {}),
                        'expr': update_item.get('expr', ''),
                        'update_count': 0,
                        'success': False,
                        'error': str(e)
                    })
                    logger.warning(f"异步批次更新操作失败: {e}")

        return chunk_results


# ==================== 便捷创建函数 ====================

def create_pgvector_client(host: str, port: int, user: str, password: str,
                          database: str, **kwargs) -> PGVectorClient:
    """
    便捷创建PGVector客户端
    
    Args:
        host: 数据库主机
        port: 数据库端口
        user: 用户名
        password: 密码
        database: 数据库名
        **kwargs: 额外配置
        
    Returns:
        PGVector客户端实例
    """
    config = VDBConnectionConfig(
        host=host,
        port=port,
        user=user,
        password=password,
        db_name=database
    )
    
    return PGVectorClient(config, **kwargs)


def create_pgvector_client_from_config(config: VDBConnectionConfig,
                                      **kwargs) -> PGVectorClient:
    """
    从配置创建PGVector客户端
    
    Args:
        config: 数据库连接配置
        **kwargs: 额外配置
        
    Returns:
        PGVector客户端实例
    """
    return PGVectorClient(config, **kwargs)
