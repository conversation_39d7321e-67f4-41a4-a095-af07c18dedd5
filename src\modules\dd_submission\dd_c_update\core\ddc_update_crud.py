"""
DD-C数据库操作组件 - 完全独立实现

支持复杂的字段覆盖、Range聚合和metadata查询功能
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.metadata.crud import MetadataCrud

logger = logging.getLogger(__name__)


class DDCUpdateCrud:
    """DD-C数据库操作组件 - 完全独立实现"""
    
    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化DD-C数据库操作组件
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        
        # 初始化依赖组件
        self.dd_crud = DDCrud(rdb_client)
        self.metadata_crud = MetadataCrud(rdb_client, vdb_client, embedding_client)
        
        logger.debug("DD-C数据库操作组件初始化完成")
    
    async def get_record_by_identifiers(
        self, 
        report_code: str, 
        dept_id: str, 
        entry_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        根据标识符获取记录
        
        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID
            entry_id: 条目ID（submission_id）
            
        Returns:
            记录数据，包含完整的SDR和BDR字段
        """
        try:
            logger.debug(f"查询DD-C记录: report_code={report_code}, dept_id={dept_id}, entry_id={entry_id}")
            
            # 查询post_distribution记录
            post_conditions = [{
                "submission_id": entry_id,
                "version": report_code,
                "dept_id": dept_id
            }]
            
            post_records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=post_conditions,
                batch_size=1,
                max_concurrency=1
            )
            
            if not post_records:
                logger.debug(f"未找到DD-C记录: {entry_id}")
                return None
            
            post_record = post_records[0]
            
            # 查询关联的pre_distribution记录
            pre_id = post_record.get('pre_distribution_id')
            if pre_id:
                pre_conditions = [{"id": pre_id}]
                pre_records = await self.dd_crud.batch_query_pre_distributions(
                    conditions_list=pre_conditions,
                    batch_size=1,
                    max_concurrency=1
                )
                
                if pre_records:
                    pre_record = pre_records[0]
                    # 合并dr字段
                    post_record['dr01'] = pre_record.get('dr01', '')
                    post_record['dr09'] = pre_record.get('dr09', '')
                    post_record['dr17'] = pre_record.get('dr17', '')
            
            # 查询table_ids
            table_ids = await self._get_table_ids_for_dept(dept_id)
            post_record['table_ids'] = table_ids
            
            logger.debug(f"DD-C记录查询成功: id={post_record.get('id')}")
            return post_record
            
        except Exception as e:
            logger.error(f"查询DD-C记录失败: {e}")
            return None
    
    async def update_record_fields(
        self, 
        record_id: int, 
        update_fields: Dict[str, Any],
        override_fields: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新记录字段，支持字段覆盖逻辑
        
        Args:
            record_id: 记录ID
            update_fields: 要更新的字段
            override_fields: 覆盖字段（优先级更高）
            
        Returns:
            更新是否成功
        """
        try:
            if not update_fields and not override_fields:
                logger.debug(f"没有字段需要更新: record_id={record_id}")
                return True
            
            # 合并字段，override_fields优先级更高
            final_fields = {}
            if update_fields:
                final_fields.update(update_fields)
            if override_fields:
                final_fields.update(override_fields)

            logger.debug(f"更新DD-C记录字段: record_id={record_id}, 原始字段={list(final_fields.keys())}")

            # 字段名映射：前端字段名 → 数据库字段名
            field_mapping = {
                'entry_type': 'submission_type',  # 前端entry_type → 数据库submission_type
                'report_code': 'version',         # 前端report_code → 数据库version
                'entry_id': 'submission_id',     # 前端entry_id → 数据库submission_id
                # SDR和BDR字段保持不变，但转换为小写
            }

            # entry_type值映射：前端值 → 数据库值
            entry_type_mapping = {
                'ITEM': 'SUBMISSION',  # 前端ITEM → 数据库SUBMISSION
                'TABLE': 'RANGE'      # 前端TABLE → 数据库RANGE
            }

            # 应用字段名映射
            mapped_fields = {}
            for field_name, field_value in final_fields.items():
                # 检查是否需要映射
                if field_name in field_mapping:
                    db_field_name = field_mapping[field_name]

                    # 特殊处理entry_type值映射
                    if field_name == 'entry_type' and field_value in entry_type_mapping:
                        db_field_value = entry_type_mapping[field_value]
                        mapped_fields[db_field_name] = db_field_value
                        logger.debug(f"字段和值映射: {field_name}={field_value} → {db_field_name}={db_field_value}")
                    else:
                        mapped_fields[db_field_name] = field_value
                        logger.debug(f"字段映射: {field_name} → {db_field_name}")
                else:
                    # SDR/BDR字段转换为小写（数据库字段名是小写）
                    db_field_name = field_name.lower()
                    mapped_fields[db_field_name] = field_value
                    if field_name != db_field_name:
                        logger.debug(f"字段小写转换: {field_name} → {db_field_name}")

            logger.debug(f"映射后字段: record_id={record_id}, fields={list(mapped_fields.keys())}")

            # 使用DDCrud的更新方法
            success = await self.dd_crud.update_post_distributions(
                updates=mapped_fields,
                conditions={"id": record_id}
            )
            
            if success:
                logger.debug(f"DD-C记录更新成功: record_id={record_id}")
                return True
            else:
                logger.warning(f"DD-C记录更新失败: record_id={record_id}")
                return False
            
        except Exception as e:
            logger.error(f"更新DD-C记录字段失败: record_id={record_id}, error={e}")
            return False
    
    async def batch_update_records(
        self, 
        updates: List[Tuple[int, Dict[str, Any], Optional[Dict[str, Any]]]]
    ) -> Tuple[int, int]:
        """
        批量更新记录，支持字段覆盖逻辑
        
        Args:
            updates: 更新列表，每个元素为(record_id, update_fields, override_fields)
            
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        try:
            logger.debug(f"开始DD-C批量更新: 总数={len(updates)}")
            
            for record_id, update_fields, override_fields in updates:
                success = await self.update_record_fields(record_id, update_fields, override_fields)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
            
            logger.info(f"DD-C批量更新完成: 成功={success_count}, 失败={failed_count}")
            
        except Exception as e:
            logger.error(f"DD-C批量更新失败: error={e}")
            failed_count = len(updates) - success_count
        
        return success_count, failed_count
    
    async def generate_table_chinese_names(self, table_list: List[str]) -> Dict[str, str]:
        """
        生成表中文名映射
        
        Args:
            table_list: 表英文名列表
            
        Returns:
            表中文名映射 {table_name: table_name_cn}
        """
        try:
            logger.debug(f"开始查询表中文名映射: {table_list}")
            
            table_cn_mapping = {}
            for table_name in table_list:
                try:
                    # 使用metadata查询表中文名
                    table_info = await self.metadata_crud.get_source_table(table_name=table_name.strip())
                    if table_info and table_info.get("table_name_cn"):
                        table_cn_mapping[table_name.strip()] = table_info["table_name_cn"]
                        logger.debug(f"查询到表中文名: {table_name} -> {table_info['table_name_cn']}")
                    else:
                        table_cn_mapping[table_name.strip()] = table_name.strip()  # 使用原名作为备选
                        logger.debug(f"未找到表中文名，使用原名: {table_name}")
                except Exception as e:
                    logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                    table_cn_mapping[table_name.strip()] = table_name.strip()
            
            return table_cn_mapping
            
        except Exception as e:
            logger.error(f"生成表中文名映射失败: {e}")
            return {table: table for table in table_list}  # 返回原名映射
    
    async def generate_column_chinese_names(self, field_mapping: Dict[str, List[str]]) -> Dict[str, str]:
        """
        生成字段中文名映射
        
        Args:
            field_mapping: 字段映射 {table_name: [column_names]}
            
        Returns:
            字段中文名映射 {column_name: column_name_cn}
        """
        try:
            logger.debug(f"开始查询字段中文名映射: {field_mapping}")
            
            column_cn_mapping = {}
            
            # 提取所有字段名
            all_columns = set()
            for table_name, columns in field_mapping.items():
                all_columns.update(columns)
            
            # 查询每个字段的中文名
            for column_name in all_columns:
                try:
                    # 使用metadata查询字段中文名
                    column_info = await self.metadata_crud.get_source_column(column_name=column_name.strip())
                    if column_info and column_info.get("column_name_cn"):
                        column_cn_mapping[column_name.strip()] = column_info["column_name_cn"]
                        logger.debug(f"查询到字段中文名: {column_name} -> {column_info['column_name_cn']}")
                    else:
                        column_cn_mapping[column_name.strip()] = column_name.strip()  # 使用原名作为备选
                        logger.debug(f"未找到字段中文名，使用原名: {column_name}")
                except Exception as e:
                    logger.warning(f"查询字段中文名失败: {column_name}, error={e}")
                    column_cn_mapping[column_name.strip()] = column_name.strip()
            
            return column_cn_mapping
            
        except Exception as e:
            logger.error(f"生成字段中文名映射失败: {e}")
            return {}
    
    async def check_range_aggregation_needed(
        self, 
        report_code: str, 
        dept_id: str, 
        modified_entries: Set[str]
    ) -> bool:
        """
        检查是否需要Range聚合
        
        Args:
            report_code: 报告代码
            dept_id: 部门ID
            modified_entries: 修改的条目ID集合
            
        Returns:
            是否需要Range聚合
        """
        try:
            logger.debug(f"检查Range聚合需求: report_code={report_code}, dept_id={dept_id}, entries={modified_entries}")
            
            # 查询所有相关记录
            conditions_list = []
            for entry_id in modified_entries:
                conditions_list.append({
                    "submission_id": entry_id,
                    "version": report_code,
                    "dept_id": dept_id
                })
            
            records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=conditions_list,
                batch_size=len(conditions_list),
                max_concurrency=5
            )
            
            # 检查是否有记录修改了SDR05/SDR08/SDR10中的任意组合
            for record in records:
                sdr05 = record.get('sdr05', '')
                sdr08 = record.get('sdr08', '')
                sdr10 = record.get('sdr10', '')
                
                # 检查是否至少有一个字段不为空
                if any([sdr05 and str(sdr05).strip(), 
                       sdr08 and str(sdr08).strip(), 
                       sdr10 and str(sdr10).strip()]):
                    logger.debug(f"检测到需要Range聚合的记录: entry_id={record.get('submission_id')}")
                    return True
            
            logger.debug("无需Range聚合")
            return False
            
        except Exception as e:
            logger.error(f"检查Range聚合需求失败: {e}")
            return False
    
    async def get_all_records_for_aggregation(
        self,
        report_code: str,
        dept_id: str
    ) -> List[Dict[str, Any]]:
        """
        获取所有需要聚合的记录

        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID

        Returns:
            所有相关记录列表
        """
        try:
            logger.debug(f"查询聚合记录: report_code={report_code}, dept_id={dept_id}")

            # 查询所有相关的post_distribution记录
            conditions_list = [{
                "version": report_code,
                "dept_id": dept_id
            }]

            records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=conditions_list,
                batch_size=1000,  # 假设不会有太多记录
                max_concurrency=5
            )

            logger.debug(f"找到 {len(records)} 个相关记录")
            return records

        except Exception as e:
            logger.error(f"查询聚合记录失败: {e}")
            return []

    async def aggregate_submission_records(
        self,
        submission_records: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        聚合SUBMISSION记录的数据

        Args:
            submission_records: SUBMISSION类型记录列表

        Returns:
            聚合后的数据
        """
        try:
            logger.debug(f"开始聚合 {len(submission_records)} 个SUBMISSION记录")

            # 聚合逻辑：收集所有非空的SDR和BDR字段
            aggregated = {}

            # 需要聚合的字段
            fields_to_aggregate = [
                'sdr05', 'sdr06', 'sdr08', 'sdr09', 'sdr10', 'sdr12',
                'bdr09', 'bdr10', 'bdr11', 'bdr16'
            ]

            for field in fields_to_aggregate:
                field_values = []
                for record in submission_records:
                    value = record.get(field, '')
                    if value and str(value).strip():
                        field_values.append(value)

                if field_values:
                    # 对于JSON字段，合并为列表；对于字符串字段，连接
                    if field in ['sdr05', 'sdr06', 'sdr08', 'sdr09', 'bdr09', 'bdr10', 'bdr11']:
                        # JSON字段：尝试解析并合并
                        merged_data = self._merge_json_fields(field_values)
                        if merged_data:
                            aggregated[field] = json.dumps(merged_data, ensure_ascii=False)
                    else:
                        # 字符串字段：取最新的非空值
                        aggregated[field] = field_values[-1]

            logger.debug(f"聚合完成，生成 {len(aggregated)} 个字段")
            return aggregated

        except Exception as e:
            logger.error(f"聚合SUBMISSION记录失败: {e}")
            return {}

    async def update_range_record(
        self,
        range_record_id: int,
        aggregated_data: Dict[str, Any]
    ) -> bool:
        """
        更新RANGE记录

        Args:
            range_record_id: RANGE记录ID
            aggregated_data: 聚合后的数据

        Returns:
            更新是否成功
        """
        try:
            logger.debug(f"更新RANGE记录: record_id={range_record_id}, fields={list(aggregated_data.keys())}")

            success = await self.dd_crud.update_post_distributions(
                updates=aggregated_data,
                conditions={"id": range_record_id}
            )

            if success:
                logger.debug(f"RANGE记录更新成功: record_id={range_record_id}")
            else:
                logger.warning(f"RANGE记录更新失败: record_id={range_record_id}")

            return success

        except Exception as e:
            logger.error(f"更新RANGE记录失败: record_id={range_record_id}, error={e}")
            return False

    def _merge_json_fields(self, field_values: List[str]) -> Any:
        """
        合并JSON字段值

        Args:
            field_values: JSON字符串列表

        Returns:
            合并后的数据
        """
        try:
            merged_lists = []
            merged_dicts = {}

            for value in field_values:
                try:
                    parsed = json.loads(value) if isinstance(value, str) else value

                    if isinstance(parsed, list):
                        merged_lists.extend(parsed)
                    elif isinstance(parsed, dict):
                        merged_dicts.update(parsed)

                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON值: {value}")
                    continue

            # 返回合并结果
            if merged_lists and merged_dicts:
                # 如果既有列表又有字典，优先返回字典
                return merged_dicts
            elif merged_lists:
                # 去重并返回列表
                return list(set(merged_lists))
            elif merged_dicts:
                return merged_dicts
            else:
                return None

        except Exception as e:
            logger.warning(f"合并JSON字段失败: {e}")
            return None

    async def _get_table_ids_for_dept(self, dept_id: str) -> Dict[str, int]:
        """
        获取部门的table_ids映射

        Args:
            dept_id: 部门ID

        Returns:
            table_ids映射
        """
        try:
            # 这里应该实现具体的table_ids查询逻辑
            # 暂时返回空字典
            return {}
        except Exception as e:
            logger.warning(f"获取table_ids失败: dept_id={dept_id}, error={e}")
            return {}
