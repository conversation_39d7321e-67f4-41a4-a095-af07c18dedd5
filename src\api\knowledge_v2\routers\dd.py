"""
Knowledge V2 API - DD数据管理路由

提供DD数据的4个核心接口：
1. list_dd_report_data - 列出所有dd_report_data信息
2. get_dd_report_data - 获取某个具体的dd_report_data
3. get_dd_submission_data_by_report - 根据report_data_id和dept_id查询dd_submission_data
4. search_dd_data - 使用混合搜索功能搜索DD数据

基于现有的DD CRUD模块和元数据搜索模块构建
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from datetime import datetime

# 导入Knowledge V2的统一模型
from ..models.request_models import UnifiedListRequest
from ..models.response_models import UnifiedResponse, UnifiedListResponse
from ..dependencies.common import get_dd_crud, get_metadata_search, handle_api_errors

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/dd",
    tags=["DD数据管理"],
    responses={404: {"description": "Not found"}}
)


# ==================== DD报表数据接口 ====================

@router.get("/report-data/", response_model=UnifiedListResponse, summary="列出所有DD报表数据")
@handle_api_errors
async def list_dd_report_data(
    knowledge_id: Optional[str] = Query(None, description="知识库ID"),
    version: Optional[str] = Query(None, description="版本号"),
    report_layer: Optional[str] = Query(None, description="报表层级"),
    report_type: Optional[str] = Query(None, description="报表类型"),
    dd_crud = Depends(get_dd_crud)
):
    """
    列出所有DD报表数据信息

    返回全量数据，不进行分页限制，由前端控制分页显示
    默认列出没有条件的所有dd_report_data，支持可选的过滤条件
    """
    try:
        logger.info(f"开始查询DD报表数据列表: knowledge_id={knowledge_id}, version={version}")
        
        # 调用DD CRUD的list_report_data方法，不设置limit和offset，返回全量数据
        report_data_list = await dd_crud.list_report_data(
            knowledge_id=knowledge_id,
            version=version,
            report_layer=report_layer,
            report_type=report_type,
            limit=None,  # 不限制数量，返回全量数据
            offset=None  # 不设置偏移量
        )
        
        logger.info(f"成功查询DD报表数据: 返回 {len(report_data_list)} 条记录")
        
        return UnifiedListResponse(
            success=True,
            message=f"成功查询到 {len(report_data_list)} 条DD报表数据",
            data=report_data_list,
            pagination={"total_records": len(report_data_list)},  # 只保留总记录数
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"查询DD报表数据列表失败: {e}")
        return UnifiedListResponse(
            success=False,
            message=f"查询DD报表数据失败: {str(e)}",
            data=[],
            pagination={"total_records": 0},  # 只保留总记录数
            timestamp=datetime.now()
        )


@router.get("/report-data/{report_id}", response_model=UnifiedResponse, summary="获取具体的DD报表数据")
@handle_api_errors
async def get_dd_report_data(
    report_id: int,
    dd_crud = Depends(get_dd_crud)
):
    """
    获取某个具体的dd_report_data
    
    根据主键ID获取完整的报表数据信息
    """
    try:
        logger.info(f"开始获取DD报表数据: report_id={report_id}")
        
        # 调用DD CRUD的get_report_data方法
        report_data = await dd_crud.get_report_data(report_id=report_id)
        
        if not report_data:
            logger.warning(f"DD报表数据不存在: report_id={report_id}")
            return UnifiedResponse(
                success=False,
                message=f"DD报表数据不存在: ID={report_id}",
                data=None,
                timestamp=datetime.now()
            )
        
        logger.info(f"成功获取DD报表数据: report_id={report_id}")
        
        return UnifiedResponse(
            success=True,
            message="成功获取DD报表数据",
            data=report_data,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"获取DD报表数据失败: report_id={report_id}, error={e}")
        return UnifiedResponse(
            success=False,
            message=f"获取DD报表数据失败: {str(e)}",
            data=None,
            timestamp=datetime.now()
        )


# ==================== DD填报数据接口 ====================

@router.get("/submission-data", response_model=UnifiedListResponse, summary="根据报表ID和部门ID查询填报数据")
@handle_api_errors
async def get_dd_submission_data_by_report(
    report_data_id: int = Query(..., description="报表数据ID（dd_submission_data表中的report_data_id外键）"),
    dept_id: str = Query(..., description="部门ID（dd_submission_data表中的dr22字段，字符串类型）"),
    dd_crud = Depends(get_dd_crud)
):
    """
    根据报表数据ID和部门ID查询DD填报数据

    返回全量数据，不进行分页限制，由前端控制分页显示

    查询条件：
    - report_data_id: dd_submission_data表中的report_data_id字段（外键）
    - dept_id: dd_submission_data表中的dr22字段（字符串类型）
    """
    try:
        logger.info(f"开始查询DD填报数据: report_data_id={report_data_id}, dept_id={dept_id}")
        
        # 构建查询条件
        where_conditions = {
            "report_data_id": report_data_id,
            "dr22": dept_id  # dr22字段对应dept_id
        }
        
        # 调用DD CRUD的批量查询方法
        submission_data_list = await dd_crud.batch_query_submission_data(
            conditions_list=[where_conditions],
            batch_size=100,
            max_concurrency=5
        )
        
        logger.info(f"成功查询DD填报数据: report_data_id={report_data_id}, dept_id={dept_id}, 返回 {len(submission_data_list)} 条记录")

        return UnifiedListResponse(
            success=True,
            message=f"成功查询到 {len(submission_data_list)} 条DD填报数据",
            data=submission_data_list,
            pagination={"total_records": len(submission_data_list)},  # 只保留总记录数
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"查询DD填报数据失败: report_data_id={report_data_id}, dept_id={dept_id}, error={e}")
        return UnifiedListResponse(
            success=False,
            message=f"查询DD填报数据失败: {str(e)}",
            data=[],
            pagination={"total_records": 0},  # 只保留总记录数
            timestamp=datetime.now()
        )


# ==================== DD数据搜索接口 ====================

@router.get("/search", response_model=UnifiedListResponse, summary="DD数据混合搜索")
@handle_api_errors
async def search_dd_data(
    query: str = Query(..., description="查询文本（必需）"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID（可选）"),
    data_layer: Optional[str] = Query(None, description="数据层（可选）"),
    limit: int = Query(10, description="返回数量限制", ge=1, le=100),
    min_score: float = Query(0.3, description="最小相似度分数", ge=0.0, le=1.0),
    vector_weight: float = Query(0.7, description="向量搜索权重", ge=0.0, le=1.0),
    fuzzy_weight: float = Query(0.3, description="模糊搜索权重", ge=0.0, le=1.0),
    metadata_search = Depends(get_metadata_search)
):
    """
    DD数据混合搜索接口
    
    使用元数据搜索模块的hybrid_search方法进行混合搜索（向量搜索 + 模糊搜索）
    
    参数说明：
    - query: 查询文本（必需）
    - knowledge_id: 知识库ID（可选）
    - data_layer: 数据层（可选）
    - limit: 返回数量限制（默认10）
    - min_score: 最小相似度分数（默认0.3）
    - vector_weight: 向量搜索权重（默认0.7）
    - fuzzy_weight: 模糊搜索权重（默认0.3）
    """
    try:
        logger.info(f"开始DD数据混合搜索: query='{query}', knowledge_id={knowledge_id}, data_layer={data_layer}")
        
        # 验证权重参数
        if abs(vector_weight + fuzzy_weight - 1.0) > 0.01:
            logger.warning(f"权重参数不合理: vector_weight={vector_weight}, fuzzy_weight={fuzzy_weight}")
            # 自动调整权重
            total_weight = vector_weight + fuzzy_weight
            if total_weight > 0:
                vector_weight = vector_weight / total_weight
                fuzzy_weight = fuzzy_weight / total_weight
            else:
                vector_weight = 0.7
                fuzzy_weight = 0.3
        
        # 调用元数据搜索模块的hybrid_search方法
        search_results = await metadata_search.hybrid_search(
            query=query,
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            limit=limit,
            min_score=min_score,
            vector_weight=vector_weight,
            fuzzy_weight=fuzzy_weight
        )
        
        logger.info(f"DD数据混合搜索完成: query='{query}', 返回 {len(search_results)} 条结果")
        
        return UnifiedListResponse(
            success=True,
            message=f"搜索完成，找到 {len(search_results)} 条相关结果",
            data=search_results,
            pagination={"total_records": len(search_results)},  # 只保留总记录数
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"DD数据混合搜索失败: query='{query}', error={e}")
        return UnifiedListResponse(
            success=False,
            message=f"搜索失败: {str(e)}",
            data=[],
            pagination={"total_records": 0},  # 只保留总记录数
            timestamp=datetime.now()
        )


# ==================== 健康检查接口 ====================

@router.get("/health", summary="DD数据管理健康检查")
async def dd_data_health_check():
    """DD数据管理模块健康检查"""
    return {
        "status": "healthy",
        "module": "DD数据管理",
        "version": "2.0.0",
        "endpoints": {
            "list_report_data": "/dd/report-data/",
            "get_report_data": "/dd/report-data/{report_id}",
            "get_submission_data": "/dd/submission-data",
            "search_dd_data": "/dd/search"
        },
        "features": [
            "DD报表数据列表查询",
            "DD报表数据详情获取",
            "DD填报数据关联查询",
            "DD数据混合搜索"
        ]
    }
