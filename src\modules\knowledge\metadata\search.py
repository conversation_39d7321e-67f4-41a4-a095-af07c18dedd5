"""
Metadata系统搜索功能

参考DD系统的search.py设计，提供多种搜索策略：
- 纯向量搜索（对所有向量化字段搜索）
- 混合搜索（向量搜索 + 文本搜索）
- 精确匹配搜索
- 便捷搜索方法

支持的搜索实体：
- 源数据库/指标数据库
- 源表/指标表
- 源字段/指标字段
- 码值集/码值
- 数据主题
"""

from typing import Any, Dict, List, Optional, Tuple
import logging
from datetime import datetime

from .shared.exceptions import MetadataError
from .shared.constants import MetadataConstants, MetadataTableNames
from .shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataSearch:
    """Metadata系统搜索类"""

    def __init__(self, rdb_client: Any, vdb_client: Any, embedding_client: Any = None):
        """
        初始化搜索功能

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端（可选）
        """
        # 直接使用数据库客户端，不需要适配器
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
    
    async def vector_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        entity_types: Optional[List[str]] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        纯向量搜索（对所有向量化字段搜索）

        Args:
            query: 查询文本
            knowledge_id: 知识库ID（可选）
            entity_types: 实体类型列表，如 ['source_table', 'source_column', 'code_value']
            limit: 返回数量限制
            min_score: 最小相似度分数

        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"开始向量搜索: query='{query}', knowledge_id={knowledge_id}")

            # 如果没有向量客户端，返回空结果
            if not self.vdb_client or not self.embedding_client:
                logger.warning("向量客户端未配置，跳过向量搜索")
                return []

            # 生成查询向量
            embedding_result = await self.embedding_client.ainvoke(texts=[query])
            if not embedding_result or not embedding_result.embeddings:
                logger.warning("查询向量生成失败")
                return []

            query_vector = embedding_result.embeddings[0]

            all_results = []

            # 确定要搜索的实体类型
            search_entity_types = entity_types or ['source_table', 'source_column', 'code_value']

            # 按向量集合分组搜索
            from .shared.constants import MetadataVectorCollections
            collections_to_search = {}

            for entity_type in search_entity_types:
                if entity_type in MetadataVectorCollections.ENTITY_TYPE_TO_COLLECTION:
                    collection = MetadataVectorCollections.ENTITY_TYPE_TO_COLLECTION[entity_type]
                    if collection not in collections_to_search:
                        collections_to_search[collection] = []
                    collections_to_search[collection].append(entity_type)

            # 在每个向量集合中搜索
            for collection_name, types in collections_to_search.items():
                try:
                    # 构建过滤条件
                    filters = {}
                    if knowledge_id:
                        filters['knowledge_id'] = knowledge_id

                    # 构建搜索参数
                    search_param = {
                        "metric_type": "cosine",
                        "params": {}
                    }

                    # 构建过滤表达式
                    expr = None
                    if filters:
                        expr_parts = []
                        for key, value in filters.items():
                            if isinstance(value, str):
                                expr_parts.append(f"{key} = '{value}'")
                            else:
                                expr_parts.append(f"{key} = {value}")
                        expr = " and ".join(expr_parts) if expr_parts else None

                    # 执行向量搜索
                    search_results = await self.vdb_client.asearch(
                        collection_name=collection_name,
                        data=[query_vector],  # 需要是向量列表
                        anns_field="embedding",  # 向量字段名
                        param=search_param,
                        limit=limit * 2,  # 多获取一些结果用于过滤
                        expr=expr,
                        output_fields=None,  # 获取所有字段
                        min_score=min_score  # 🔧 传递min_score参数
                    )

                    # 处理搜索结果（search_results是List[List[SearchResult]]）
                    if search_results and len(search_results) > 0:
                        for result in search_results[0]:  # 取第一个查询的结果
                            score = result.score if hasattr(result, 'score') else 0
                            if score >= min_score:
                                # 提取实体信息
                                entity_data = result.entity if hasattr(result, 'entity') else {}

                                # 根据向量集合类型提取实体ID和类型
                                entity_id = None
                                entity_type = None

                                if collection_name == MetadataVectorCollections.MD_TABLE_EMBEDDINGS:
                                    # 表级集合：可能是数据库或表
                                    table_id = entity_data.get('table_id', 0)
                                    db_id = entity_data.get('db_id', 0)
                                    source_type = entity_data.get('source_type', 'SOURCE')

                                    if table_id == 0:  # 数据库级别
                                        entity_id = db_id
                                        entity_type = 'source_database' if source_type == 'SOURCE' else 'index_database'
                                    else:  # 表级别
                                        entity_id = table_id
                                        entity_type = 'source_table' if source_type == 'SOURCE' else 'index_table'

                                elif collection_name == MetadataVectorCollections.MD_COLUMN_EMBEDDINGS:
                                    # 字段级集合
                                    entity_id = entity_data.get('column_id')
                                    source_type = entity_data.get('source_type', 'SOURCE')
                                    entity_type = 'source_column' if source_type == 'SOURCE' else 'index_column'

                                elif collection_name == MetadataVectorCollections.MD_CODE_EMBEDDINGS:
                                    # 码值级集合
                                    entity_id = entity_data.get('code_value_id')
                                    entity_type = 'code_value'

                                if entity_type in types and entity_id:
                                    all_results.append({
                                        'entity_id': entity_id,
                                        'entity_type': entity_type,
                                        'score': score,
                                        'matched_field': collection_name,
                                        'search_type': 'vector',
                                        'metadata': entity_data
                                    })

                except Exception as e:
                    logger.warning(f"向量集合 {collection_name} 搜索失败: {e}")
                    continue

            # 按相似度分数排序并去重
            unique_results = self._deduplicate_results(all_results, "entity_id")
            sorted_results = sorted(unique_results, key=lambda x: x.get("score", 0), reverse=True)

            # 获取完整的元数据
            enriched_results = await self._enrich_with_metadata(sorted_results[:limit])

            logger.info(f"向量搜索完成: 找到 {len(enriched_results)} 条结果")
            return enriched_results

        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            raise MetadataError(f"向量搜索失败: {e}")
    
    async def hybrid_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        entity_types: Optional[List[str]] = None,
        limit: int = 10,
        min_score: float = 0.3,
        vector_weight: float = 0.7,
        text_weight: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        混合搜索（向量搜索 + 文本搜索）
        
        Args:
            query: 查询文本
            knowledge_id: 知识库ID（可选）
            entity_types: 实体类型列表
            limit: 返回数量限制
            min_score: 最小相似度分数
            vector_weight: 向量搜索权重
            text_weight: 文本搜索权重
            
        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"开始混合搜索: query='{query}', knowledge_id={knowledge_id}")
            
            # 1. 向量搜索
            vector_results = await self.vector_search(
                query=query,
                knowledge_id=knowledge_id,
                entity_types=entity_types,
                limit=limit * 2,  # 获取更多结果用于混合
                min_score=min_score
            )
            
            # 2. 文本搜索
            text_results = await self._text_search(
                query=query,
                knowledge_id=knowledge_id,
                entity_types=entity_types,
                limit=limit * 2
            )
            
            # 3. 合并和重新评分
            combined_results = self._combine_search_results(
                vector_results=vector_results,
                text_results=text_results,
                vector_weight=vector_weight,
                text_weight=text_weight
            )
            
            # 4. 过滤低分结果并排序
            filtered_results = [
                result for result in combined_results 
                if result.get("combined_score", 0) >= min_score
            ]
            
            sorted_results = sorted(
                filtered_results, 
                key=lambda x: x.get("combined_score", 0), 
                reverse=True
            )
            
            final_results = sorted_results[:limit]
            
            logger.info(f"混合搜索完成: 向量结果 {len(vector_results)}, 文本结果 {len(text_results)}, 最终结果 {len(final_results)}")
            return final_results
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise MetadataError(f"混合搜索失败: {e}")
    
    async def exact_match_search(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]] = None,
        knowledge_id: Optional[str] = None,
        entity_types: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        精确匹配搜索

        Args:
            query_text: 查询文本（用于日志记录）
            filters: 精确匹配过滤条件
            knowledge_id: 知识库ID（可选）
            entity_types: 实体类型列表
            limit: 返回数量限制

        Returns:
            精确匹配的搜索结果列表
        """
        try:
            logger.info(f"开始精确匹配搜索: query='{query_text}', filters={filters}")

            # 构建精确匹配查询条件
            search_filters = {}
            if knowledge_id:
                search_filters["knowledge_id"] = knowledge_id

            # 添加精确匹配条件
            if filters:
                search_filters.update(filters)

            # 搜索所有相关实体
            all_results = []
            
            # 根据entity_types搜索不同的表
            search_tables = self._get_search_tables(entity_types)
            
            for table_name in search_tables:
                try:
                    table_results = await self._search_in_table(
                        table_name=table_name,
                        filters=search_filters,
                        limit=limit
                    )
                    
                    # 添加实体类型信息
                    for result in table_results:
                        result["entity_type"] = self._get_entity_type_from_table(table_name)
                        result["search_type"] = "exact_match"
                        result["score"] = 1.0  # 精确匹配分数为1.0
                    
                    all_results.extend(table_results)
                    
                except Exception as e:
                    logger.warning(f"表 {table_name} 精确匹配搜索失败: {e}")
                    continue

            # 限制返回数量
            exact_matches = all_results[:limit]

            logger.info(f"精确匹配搜索完成: 找到 {len(exact_matches)} 条结果")
            return exact_matches

        except Exception as e:
            logger.error(f"精确匹配搜索失败: {e}")
            raise MetadataError(f"精确匹配搜索失败: {e}")

    # ==================== 专门的向量搜索方法 ====================

    async def search_tables_by_vector(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        表的向量搜索，返回表的全量信息

        Args:
            query: 查询文本
            knowledge_id: 知识库ID
            limit: 返回数量限制
            min_score: 最小相似度分数

        Returns:
            表的完整信息列表
        """
        return await self.vector_search(
            query=query,
            knowledge_id=knowledge_id,
            entity_types=['source_table', 'index_table'],
            limit=limit,
            min_score=min_score
        )

    async def search_columns_by_vector(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        字段的向量搜索，返回字段的全量信息

        Args:
            query: 查询文本
            knowledge_id: 知识库ID
            limit: 返回数量限制
            min_score: 最小相似度分数

        Returns:
            字段的完整信息列表
        """
        return await self.vector_search(
            query=query,
            knowledge_id=knowledge_id,
            entity_types=['source_column', 'index_column'],
            limit=limit,
            min_score=min_score
        )

    async def search_codes_by_vector(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        码值的向量搜索，返回码值、码值集的全量信息

        Args:
            query: 查询文本
            knowledge_id: 知识库ID
            limit: 返回数量限制
            min_score: 最小相似度分数

        Returns:
            码值和码值集的完整信息列表
        """
        return await self.vector_search(
            query=query,
            knowledge_id=knowledge_id,
            entity_types=['code_value'],
            limit=limit,
            min_score=min_score
        )

    # ==================== 便捷搜索方法 ====================

    async def search_by_database_name(
        self,
        db_name: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据数据库名称搜索"""
        return await self.hybrid_search(
            query=db_name,
            knowledge_id=knowledge_id,
            entity_types=['source_database', 'index_database'],
            limit=limit
        )

    async def search_by_table_name(
        self,
        table_name: str,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据表名称搜索"""
        return await self.hybrid_search(
            query=table_name,
            knowledge_id=knowledge_id,
            entity_types=['source_table', 'index_table'],
            limit=limit
        )

    async def search_by_column_name(
        self,
        column_name: str,
        knowledge_id: Optional[str] = None,
        table_id: Optional[int] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据字段名称搜索"""
        return await self.hybrid_search(
            query=column_name,
            knowledge_id=knowledge_id,
            entity_types=['source_column', 'index_column'],
            limit=limit
        )

    async def search_by_column_description(
        self,
        description: str,
        knowledge_id: Optional[str] = None,
        data_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据字段描述搜索"""
        return await self.vector_search(
            query=description,
            knowledge_id=knowledge_id,
            entity_types=['source_column', 'index_column'],
            limit=limit
        )

    async def search_by_code_set_name(
        self,
        code_set_name: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据码值集名称搜索"""
        return await self.hybrid_search(
            query=code_set_name,
            knowledge_id=knowledge_id,
            entity_types=['code_set'],
            limit=limit
        )

    async def search_by_code_value(
        self,
        code_value: str,
        knowledge_id: Optional[str] = None,
        code_set_id: Optional[int] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据码值搜索"""
        return await self.hybrid_search(
            query=code_value,
            knowledge_id=knowledge_id,
            entity_types=['code_value'],
            limit=limit
        )

    async def search_by_entity_type(
        self,
        entity_type: str,
        knowledge_id: Optional[str] = None,
        query: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """根据实体类型搜索所有相关数据"""
        if query:
            return await self.hybrid_search(
                query=query,
                knowledge_id=knowledge_id,
                entity_types=[entity_type],
                limit=limit
            )
        else:
            # 直接查询数据库
            search_tables = self._get_search_tables([entity_type])
            all_results = []

            for table_name in search_tables:
                try:
                    filters = {}
                    if knowledge_id:
                        filters['knowledge_id'] = knowledge_id

                    table_results = await self._search_in_table(
                        table_name=table_name,
                        filters=filters,
                        limit=limit
                    )

                    # 添加实体类型信息
                    for result in table_results:
                        result["entity_type"] = self._get_entity_type_from_table(table_name)
                        result["search_type"] = "list_all"
                        result["score"] = 1.0

                    all_results.extend(table_results)

                except Exception as e:
                    logger.warning(f"表 {table_name} 查询失败: {e}")
                    continue

            return all_results[:limit]

    # ==================== 私有方法：数据库操作 ====================

    async def _text_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        entity_types: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        文本搜索（在向量化字段中进行LIKE搜索）
        """
        try:
            # 构建搜索条件
            filters = {}
            if knowledge_id:
                filters["knowledge_id"] = knowledge_id

            # 根据entity_types搜索不同的表
            search_tables = self._get_search_tables(entity_types)
            all_results = []

            query_lower = query.lower()

            for table_name in search_tables:
                try:
                    # 获取表中的所有数据
                    table_data = await self._search_in_table(
                        table_name=table_name,
                        filters=filters,
                        limit=1000  # 获取较多数据用于文本匹配
                    )

                    # 在内存中进行文本匹配
                    for record in table_data:
                        score = 0.0
                        matched_fields = []

                        # 检查向量化字段
                        for field_code in MetadataConstants.VECTORIZED_FIELDS:
                            field_value = record.get(field_code, "") or ""
                            if isinstance(field_value, str) and query_lower in field_value.lower():
                                # 简单的文本匹配评分
                                field_score = len(query) / max(len(field_value), 1)
                                score += field_score
                                matched_fields.append(field_code)

                        # 检查名称字段
                        name_fields = ['db_name', 'table_name', 'column_name', 'code_set_name', 'subject_name']
                        for name_field in name_fields:
                            field_value = record.get(name_field, "") or ""
                            if isinstance(field_value, str) and query_lower in field_value.lower():
                                field_score = len(query) / max(len(field_value), 1)
                                score += field_score * 2  # 名称匹配权重更高
                                matched_fields.append(name_field)

                        if score > 0:
                            all_results.append({
                                **record,
                                "score": min(score, 1.0),  # 限制最大分数为1.0
                                "matched_fields": matched_fields,
                                "search_type": "text",
                                "entity_type": self._get_entity_type_from_table(table_name)
                            })

                except Exception as e:
                    logger.warning(f"表 {table_name} 文本搜索失败: {e}")
                    continue

            # 按分数排序
            all_results.sort(key=lambda x: x.get("score", 0), reverse=True)

            return all_results[:limit]

        except Exception as e:
            logger.warning(f"文本搜索失败: {e}")
            return []

    async def _search_in_table(
        self,
        table_name: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """在指定表中搜索数据"""
        try:
            query_request = {
                "table": table_name,
                "sorts": [{"field": "create_time", "order": "desc"}]
            }

            # 只有在有条件时才添加filters
            if filters:
                query_request["filters"] = filters

            # 只有在有值时才添加limit
            if limit is not None:
                query_request["limit"] = limit

            result = await self.rdb_client.aquery(query_request)
            return result.data if result else []

        except Exception as e:
            logger.error(f"表 {table_name} 查询失败: {e}")
            return []

    def _get_search_tables(self, entity_types: Optional[List[str]] = None) -> List[str]:
        """根据实体类型获取需要搜索的表名列表"""
        if not entity_types:
            # 只返回需要向量化的表
            return [
                MetadataTableNames.MD_SOURCE_DATABASE,
                MetadataTableNames.MD_INDEX_DATABASE,
                MetadataTableNames.MD_SOURCE_TABLES,
                MetadataTableNames.MD_INDEX_TABLES,
                MetadataTableNames.MD_SOURCE_COLUMNS,
                MetadataTableNames.MD_INDEX_COLUMNS,
                MetadataTableNames.MD_REFERENCE_CODE_VALUE
                # 注意：移除了MD_DATA_SUBJECT和MD_REFERENCE_CODE_SET，因为它们不需要向量化
            ]

        table_mapping = {
            'source_database': [MetadataTableNames.MD_SOURCE_DATABASE],
            'index_database': [MetadataTableNames.MD_INDEX_DATABASE],
            'database': [MetadataTableNames.MD_SOURCE_DATABASE, MetadataTableNames.MD_INDEX_DATABASE],
            'source_table': [MetadataTableNames.MD_SOURCE_TABLES],
            'index_table': [MetadataTableNames.MD_INDEX_TABLES],
            'table': [MetadataTableNames.MD_SOURCE_TABLES, MetadataTableNames.MD_INDEX_TABLES],
            'source_column': [MetadataTableNames.MD_SOURCE_COLUMNS],
            'index_column': [MetadataTableNames.MD_INDEX_COLUMNS],
            'column': [MetadataTableNames.MD_SOURCE_COLUMNS, MetadataTableNames.MD_INDEX_COLUMNS],
            'code_value': [MetadataTableNames.MD_REFERENCE_CODE_VALUE]
            # 注意：移除了code_set和data_subject的映射，因为它们不需要向量化搜索
        }

        search_tables = []
        for entity_type in entity_types:
            if entity_type in table_mapping:
                search_tables.extend(table_mapping[entity_type])

        return list(set(search_tables))  # 去重

    def _get_entity_type_from_table(self, table_name: str) -> str:
        """根据表名获取实体类型"""
        table_mapping = {
            MetadataTableNames.MD_SOURCE_DATABASE: 'source_database',
            MetadataTableNames.MD_INDEX_DATABASE: 'index_database',
            MetadataTableNames.MD_SOURCE_TABLES: 'source_table',
            MetadataTableNames.MD_INDEX_TABLES: 'index_table',
            MetadataTableNames.MD_SOURCE_COLUMNS: 'source_column',
            MetadataTableNames.MD_INDEX_COLUMNS: 'index_column',
            MetadataTableNames.MD_REFERENCE_CODE_VALUE: 'code_value'
            # 注意：移除了不需要向量化的表的映射
        }

        return table_mapping.get(table_name, 'unknown')

    def _combine_search_results(
        self,
        vector_results: List[Dict[str, Any]],
        text_results: List[Dict[str, Any]],
        vector_weight: float,
        text_weight: float
    ) -> List[Dict[str, Any]]:
        """合并向量搜索和文本搜索结果"""

        # 创建结果字典，以entity_id为键
        combined_dict = {}

        # 处理向量搜索结果
        for result in vector_results:
            entity_id = result.get("entity_id")
            entity_type = result.get("entity_type", "")
            # 使用entity_type + entity_id作为唯一键
            unique_key = f"{entity_type}_{entity_id}"

            if unique_key:
                combined_dict[unique_key] = {
                    **result,
                    "vector_score": result.get("score", 0),
                    "text_score": 0,
                    "combined_score": result.get("score", 0) * vector_weight
                }

        # 处理文本搜索结果
        for result in text_results:
            entity_id = result.get("entity_id")
            entity_type = result.get("entity_type", "")
            unique_key = f"{entity_type}_{entity_id}"

            if unique_key:
                if unique_key in combined_dict:
                    # 已存在，更新分数
                    combined_dict[unique_key]["text_score"] = result.get("score", 0)
                    combined_dict[unique_key]["combined_score"] = (
                        combined_dict[unique_key]["vector_score"] * vector_weight +
                        result.get("score", 0) * text_weight
                    )
                    # 合并匹配字段
                    vector_fields = set(combined_dict[unique_key].get("matched_fields", []))
                    text_fields = set(result.get("matched_fields", []))
                    combined_dict[unique_key]["matched_fields"] = list(vector_fields | text_fields)
                else:
                    # 新结果，只有文本分数
                    combined_dict[unique_key] = {
                        **result,
                        "vector_score": 0,
                        "text_score": result.get("score", 0),
                        "combined_score": result.get("score", 0) * text_weight,
                        "matched_fields": result.get("matched_fields", []),
                        "search_type": "text"
                    }

        return list(combined_dict.values())

    def _deduplicate_results(self, results: List[Dict[str, Any]], key_field: str) -> List[Dict[str, Any]]:
        """根据指定字段去重，保留分数最高的"""
        unique_dict = {}

        for result in results:
            key = result.get(key_field)
            entity_type = result.get("entity_type", "")
            # 使用entity_type + key作为唯一键
            unique_key = f"{entity_type}_{key}"

            if unique_key:
                if unique_key not in unique_dict or result.get("score", 0) > unique_dict[unique_key].get("score", 0):
                    unique_dict[unique_key] = result

        return list(unique_dict.values())

    async def _enrich_with_metadata(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """用完整的元数据丰富搜索结果"""
        enriched_results = []

        for result in search_results:
            entity_type = result.get("entity_type")
            entity_id = result.get("entity_id")

            if entity_type and entity_id:
                try:
                    # 根据实体类型获取完整数据
                    full_data = await self._get_entity_data(entity_type, entity_id)
                    if full_data:
                        enriched_results.append({
                            **result,
                            "entity_data": full_data
                        })
                    else:
                        # 保留原结果
                        enriched_results.append(result)
                except Exception as e:
                    logger.warning(f"获取实体数据失败: entity_type={entity_type}, entity_id={entity_id}, error={e}")
                    # 保留原结果
                    enriched_results.append(result)
            else:
                enriched_results.append(result)

        return enriched_results

    async def _get_entity_data(self, entity_type: str, entity_id: Any) -> Optional[Dict[str, Any]]:
        """根据实体类型和ID获取完整的实体数据"""
        try:
            # 根据实体类型确定表名和主键字段
            entity_mapping = {
                'source_database': (MetadataTableNames.MD_SOURCE_DATABASE, 'db_id'),
                'index_database': (MetadataTableNames.MD_INDEX_DATABASE, 'db_id'),
                'source_table': (MetadataTableNames.MD_SOURCE_TABLES, 'table_id'),
                'index_table': (MetadataTableNames.MD_INDEX_TABLES, 'table_id'),
                'source_column': (MetadataTableNames.MD_SOURCE_COLUMNS, 'column_id'),
                'index_column': (MetadataTableNames.MD_INDEX_COLUMNS, 'column_id'),
                'code_set': (MetadataTableNames.MD_REFERENCE_CODE_SET, 'id'),
                'code_value': (MetadataTableNames.MD_REFERENCE_CODE_VALUE, 'id'),
                'data_subject': (MetadataTableNames.MD_DATA_SUBJECT, 'id')
            }

            if entity_type not in entity_mapping:
                return None

            table_name, id_field = entity_mapping[entity_type]

            # 使用 _aselect 方法而不是 aquery
            results = await self._aselect(
                table=table_name,
                where={id_field: entity_id},
                limit=1
            )

            if not results:
                return None

            entity_data = results[0]


            # 对于码值，需要额外获取码值集信息
            if entity_type == 'code_value' and 'code_set_id' in entity_data:
                try:
                    code_set_results = await self._aselect(
                        table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                        where={"id": entity_data['code_set_id']},
                        limit=1
                    )
                    code_set_data = code_set_results[0] if code_set_results else {}

                    # 合并码值和码值集信息
                    entity_data['code_set_info'] = code_set_data

                except Exception as e:
                    logger.warning(f"获取码值集信息失败: code_set_id={entity_data['code_set_id']}, error={e}")

            return entity_data

        except Exception as e:
            logger.error(f"获取实体数据失败: entity_type={entity_type}, entity_id={entity_id}, error={e}")
            return None

    async def _aselect(self, table: str, where: Optional[Dict[str, Any]] = None,
                      order_by: Optional[List[str]] = None, limit: Optional[int] = None,
                      offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        兼容性方法：将aselect调用转换为aquery调用

        Args:
            table: 表名
            where: WHERE条件
            order_by: 排序字段
            limit: 限制数量
            offset: 偏移量

        Returns:
            查询结果列表
        """
        query_request = {
            "table": table
        }

        # 只有在有条件时才添加filters
        if where:
            query_request["filters"] = where

        # 只有在有值时才添加limit和offset
        if limit is not None:
            query_request["limit"] = limit
        if offset is not None:
            query_request["offset"] = offset

        # 执行查询
        result = await self.rdb_client.aquery(query_request)
        return result.data if result else []
