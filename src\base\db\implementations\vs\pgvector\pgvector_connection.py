import asyncio
import time
import logging
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any
import psycopg2
from psycopg2.pool import SimpleConnectionPool, ThreadedConnectionPool
import asyncpg

from .config import PGVectorConnectionConfig
from .exceptions import ConnectionError, PoolError, handle_pgvector_error

logger = logging.getLogger(__name__)


class PGVectorConnectionManager:
    """
    PGVector连接管理器 - 简化版
    
    基于原生连接池的简洁实现，提供：
    1. 同步连接池管理（psycopg2）- 5个连接
    2. 异步连接池管理（asyncpg）- 75个连接
    3. 基本的健康检查
    4. 连接生命周期管理
    """
    
    def __init__(self, config: PGVectorConnectionConfig, **kwargs):
        """
        初始化PGVector连接管理器

        Args:
            config: 数据库连接配置
            **kwargs: 额外配置参数
        """
        self.config = config
        self.kwargs = kwargs
        
        # 连接池
        self.sync_pool: Optional[SimpleConnectionPool | ThreadedConnectionPool] = None
        self.async_pool: Optional[asyncpg.Pool] = None
        
        # 连接状态管理
        self._is_sync_connected = False
        self._is_async_connected = False
        
        # 基本统计信息
        self._connection_stats = {
            'sync_operations': 0,
            'async_operations': 0,
            'last_connect_time': None,
            'last_disconnect_time': None
        }
        
        logger.info(f"初始化PGVector连接管理器: {config.host}:{config.port}")

    @property
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self._is_sync_connected and self.sync_pool is not None

    @property
    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self._is_async_connected and self.async_pool is not None

    @property
    def is_any_connected(self) -> bool:
        """检查是否有任何连接已建立（同步或异步）"""
        return self.is_connected or self.is_aconnected

    # ==================== 同步连接管理 ====================
    
    @handle_pgvector_error
    def connect(self) -> None:
        """建立同步连接池 - 5个连接"""
        if self.sync_pool is not None:
            logger.warning("同步连接池已存在")
            return

        try:
            # 构建连接参数
            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                'password': self.config.password,
                'database': self.config.database,
                'connect_timeout': getattr(self.config, 'connect_timeout', 10),
                'application_name': getattr(self.config, 'application_name', 'pgvector-client')
            }

            # 添加SSL配置
            if hasattr(self.config, 'sslmode') and self.config.sslmode:
                connection_params['sslmode'] = self.config.sslmode

            # 连接池配置 - 您的需求：5个同步连接
            min_conn = getattr(self.config, 'sync_min_connections', 1)
            max_conn = getattr(self.config, 'sync_max_connections', 5)

            # 创建连接池
            if max_conn > 1:
                self.sync_pool = ThreadedConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
                logger.info(f"创建ThreadedConnectionPool: min={min_conn}, max={max_conn}")
            else:
                self.sync_pool = SimpleConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
                logger.info(f"创建SimpleConnectionPool: min={min_conn}, max={max_conn}")

            # 测试连接
            self._test_sync_connection()
            self._is_sync_connected = True
            self._connection_stats['last_connect_time'] = time.time()
            
            logger.info("同步连接池建立成功")

        except Exception as e:
            logger.error(f"建立同步连接池失败: {e}")
            if self.sync_pool:
                try:
                    self.sync_pool.closeall()
                except:
                    pass
                self.sync_pool = None
            raise ConnectionError(f"无法建立同步连接池: {e}")

    def _test_sync_connection(self) -> None:
        """测试同步连接"""
        if not self.sync_pool:
            raise ConnectionError("同步连接池未初始化")
        
        conn = None
        try:
            conn = self.sync_pool.getconn()
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if not result or result[0] != 1:
                    raise ConnectionError("连接测试失败")
            logger.debug("同步连接测试成功")
        except Exception as e:
            logger.error(f"同步连接测试失败: {e}")
            raise ConnectionError(f"同步连接测试失败: {e}")
        finally:
            if conn:
                self.sync_pool.putconn(conn)

    @contextmanager
    def get_cursor(self):
        """获取同步游标 - 客户端使用的接口"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            conn = self.sync_pool.getconn()
            with conn.cursor() as cursor:
                self._connection_stats['sync_operations'] += 1
                yield cursor
        finally:
            if conn:
                self.sync_pool.putconn(conn)

    @contextmanager
    def get_connection(self):
        """获取同步连接 - 与get_cursor()类似，但返回连接对象而非游标，为了向后兼容"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            conn = self.sync_pool.getconn()
            if conn is None:
                raise ConnectionError("无法从连接池获取连接")

            self._connection_stats['sync_operations'] += 1
            yield conn

        except Exception as e:
            logger.error(f"同步连接操作失败: {e}")
            raise
        finally:
            if conn:
                try:
                    self.sync_pool.putconn(conn)
                except Exception as e:
                    logger.warning(f"归还同步连接失败: {e}")

    def disconnect(self) -> None:
        """关闭同步连接池"""
        if self.sync_pool:
            try:
                self.sync_pool.closeall()
                logger.info("同步连接池已关闭")
            except Exception as e:
                logger.error(f"关闭同步连接池时出错: {e}")
            finally:
                self.sync_pool = None
                self._is_sync_connected = False
                self._connection_stats['last_disconnect_time'] = time.time()

    # ==================== 异步连接管理 ====================
    
    @handle_pgvector_error
    async def aconnect(self) -> None:
        """建立异步连接池 - 75个连接"""
        if self.async_pool is not None:
            logger.warning("异步连接池已存在")
            return

        try:
            # 构建连接参数
            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                'password': self.config.password,
                'database': self.config.database,
                'command_timeout': getattr(self.config, 'command_timeout', 60),
                'server_settings': {
                    'application_name': getattr(self.config, 'application_name', 'pgvector-client')
                }
            }

            # 添加SSL配置
            if hasattr(self.config, 'ssl') and self.config.ssl:
                connection_params['ssl'] = self.config.ssl

            # 连接池配置 - 您的需求：75个异步连接
            min_size = getattr(self.config, 'async_min_connections', 1)
            max_size = getattr(self.config, 'async_max_connections', 75)

            # 创建异步连接池
            self.async_pool = await asyncpg.create_pool(
                min_size=min_size,
                max_size=max_size,
                max_inactive_connection_lifetime=300,
                **connection_params
            )
            
            logger.info(f"创建asyncpg连接池: min={min_size}, max={max_size}")

            # 测试连接
            await self._test_async_connection()
            self._is_async_connected = True
            self._connection_stats['last_connect_time'] = time.time()
            
            logger.info("异步连接池建立成功")

        except Exception as e:
            logger.error(f"建立异步连接池失败: {e}")
            if self.async_pool:
                try:
                    await self.async_pool.close()
                except:
                    pass
                self.async_pool = None
            raise ConnectionError(f"无法建立异步连接池: {e}")

    async def _test_async_connection(self) -> None:
        """测试异步连接"""
        if not self.async_pool:
            raise ConnectionError("异步连接池未初始化")
        
        try:
            async with self.async_pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                if result != 1:
                    raise ConnectionError("连接测试失败")
            logger.debug("异步连接测试成功")
        except Exception as e:
            logger.error(f"异步连接测试失败: {e}")
            raise ConnectionError(f"异步连接测试失败: {e}")

    @asynccontextmanager
    async def get_async_cursor(self):
        """获取异步游标 - 客户端使用的接口"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        async with self.async_pool.acquire() as conn:
            self._connection_stats['async_operations'] += 1
            yield conn

    @asynccontextmanager
    async def get_async_connection(self):
        """获取异步连接 - 与get_async_cursor()等效，为了向后兼容"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        async with self.async_pool.acquire() as conn:
            self._connection_stats['async_operations'] += 1
            yield conn

    async def adisconnect(self) -> None:
        """关闭异步连接池"""
        if self.async_pool:
            try:
                await self.async_pool.close()
                logger.info("异步连接池已关闭")
            except Exception as e:
                logger.error(f"关闭异步连接池时出错: {e}")
            finally:
                self.async_pool = None
                self._is_async_connected = False
                self._connection_stats['last_disconnect_time'] = time.time()

    # ==================== 健康检查 ====================
    
    def health_check(self) -> Dict[str, Any]:
        """基本健康检查（同步）"""
        return {
            'sync_connected': self.is_connected,
            'async_connected': self.is_aconnected,
            'sync_operations': self._connection_stats['sync_operations'],
            'async_operations': self._connection_stats['async_operations'],
            'last_connect_time': self._connection_stats['last_connect_time']
        }

    async def ahealth_check(self) -> Dict[str, Any]:
        """基本健康检查（异步）"""
        # 异步健康检查可以包含更详细的连接测试
        health_info = {
            'sync_connected': self.is_connected,
            'async_connected': self.is_aconnected,
            'sync_operations': self._connection_stats['sync_operations'],
            'async_operations': self._connection_stats['async_operations'],
            'last_connect_time': self._connection_stats['last_connect_time']
        }

        # 如果异步连接可用，进行连接测试
        if self.is_aconnected:
            try:
                async with self.async_pool.acquire() as conn:
                    result = await conn.fetchval("SELECT 1")
                    health_info['async_connection_test'] = result == 1
            except Exception as e:
                health_info['async_connection_test'] = False
                health_info['async_connection_error'] = str(e)
        else:
            health_info['async_connection_test'] = False

        return health_info

    # ==================== 事务管理 ====================

    @contextmanager
    def transaction(self):
        """同步事务上下文管理器"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            conn = self.sync_pool.getconn()
            conn.autocommit = False
            try:
                yield conn
                conn.commit()
            except Exception:
                conn.rollback()
                raise
        finally:
            if conn:
                conn.autocommit = True
                self.sync_pool.putconn(conn)

    @asynccontextmanager
    async def atransaction(self):
        """异步事务上下文管理器"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        async with self.async_pool.acquire() as conn:
            async with conn.transaction():
                yield conn

    # ==================== 清理 ====================

    def close(self) -> None:
        """关闭所有连接"""
        self.disconnect()

    async def aclose(self) -> None:
        """异步关闭所有连接"""
        await self.adisconnect()

    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass
