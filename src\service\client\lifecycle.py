import logging
import asyncio
import weakref
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..exceptions import LifecycleError

logger = logging.getLogger(__name__)


class ClientState(Enum):
    """客户端状态"""
    CREATED = "created"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"


@dataclass
class ClientLifecycle:
    """客户端生命周期信息"""
    client_id: str
    client_ref: weakref.ref
    state: ClientState = ClientState.CREATED
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    error_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


class LifecycleManager:
    """
    生命周期管理器
    
    管理：
    - 客户端状态跟踪
    - 自动清理
    - 健康检查
    - 资源监控
    """
    
    def __init__(self):
        self._clients: Dict[str, ClientLifecycle] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        self._initialized = False
        self._cleanup_interval = 300  # 5分钟
        self._health_check_interval = 60  # 1分钟
        self._max_idle_time = 1800  # 30分钟
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化生命周期管理器"""
        if self._initialized:
            return
        
        # 启动后台任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        self._initialized = True
        logger.info("生命周期管理器初始化完成")
    
    async def register(self, client: Any, client_id: str, **metadata):
        """
        注册客户端
        
        Args:
            client: 客户端实例
            client_id: 客户端ID
            **metadata: 元数据
        """
        try:
            async with self._lock:
                # 创建弱引用
                client_ref = weakref.ref(client, self._on_client_deleted)
                
                # 创建生命周期信息
                lifecycle = ClientLifecycle(
                    client_id=client_id,
                    client_ref=client_ref,
                    metadata=metadata
                )
                
                self._clients[client_id] = lifecycle
                
                # 更新状态
                await self._update_state(client_id, ClientState.CREATED)
                
                logger.debug(f"客户端已注册: {client_id}")
                
        except Exception as e:
            logger.error(f"注册客户端失败: {e}")
            raise LifecycleError(f"Failed to register client: {e}") from e
    
    async def unregister(self, client: Any) -> bool:
        """
        注销客户端
        
        Args:
            client: 客户端实例
            
        Returns:
            是否成功注销
        """
        try:
            async with self._lock:
                # 查找客户端ID
                client_id = None
                for cid, lifecycle in self._clients.items():
                    if lifecycle.client_ref() is client:
                        client_id = cid
                        break
                
                if client_id:
                    await self._cleanup_client(client_id)
                    logger.debug(f"客户端已注销: {client_id}")
                    return True
                else:
                    logger.warning("未找到要注销的客户端")
                    return False
                    
        except Exception as e:
            logger.error(f"注销客户端失败: {e}")
            return False
    
    async def update_access(self, client_id: str):
        """
        更新客户端访问信息
        
        Args:
            client_id: 客户端ID
        """
        async with self._lock:
            if client_id in self._clients:
                lifecycle = self._clients[client_id]
                lifecycle.last_accessed = datetime.now()
                lifecycle.access_count += 1
    
    async def update_state(self, client_id: str, state: ClientState):
        """
        更新客户端状态
        
        Args:
            client_id: 客户端ID
            state: 新状态
        """
        await self._update_state(client_id, state)
    
    async def _update_state(self, client_id: str, state: ClientState):
        """内部状态更新方法"""
        if client_id in self._clients:
            old_state = self._clients[client_id].state
            self._clients[client_id].state = state
            logger.debug(f"客户端状态更新: {client_id} {old_state} -> {state}")
    
    async def get_client_info(self, client_id: str) -> Optional[ClientLifecycle]:
        """
        获取客户端生命周期信息
        
        Args:
            client_id: 客户端ID
            
        Returns:
            生命周期信息
        """
        return self._clients.get(client_id)
    
    async def list_clients(self) -> List[ClientLifecycle]:
        """列出所有客户端生命周期信息"""
        return list(self._clients.values())
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        async with self._lock:
            total_clients = len(self._clients)
            state_counts = {}
            
            for lifecycle in self._clients.values():
                state = lifecycle.state.value
                state_counts[state] = state_counts.get(state, 0) + 1
            
            # 计算平均访问次数
            total_access = sum(lc.access_count for lc in self._clients.values())
            avg_access = total_access / total_clients if total_clients > 0 else 0
            
            return {
                "total_clients": total_clients,
                "state_distribution": state_counts,
                "average_access_count": avg_access,
                "total_access_count": total_access
            }
    
    def _on_client_deleted(self, client_ref: weakref.ref):
        """客户端被垃圾回收时的回调"""
        # 查找并清理对应的生命周期信息
        for client_id, lifecycle in list(self._clients.items()):
            if lifecycle.client_ref is client_ref:
                asyncio.create_task(self._cleanup_client(client_id))
                break
    
    async def _cleanup_client(self, client_id: str):
        """清理客户端"""
        try:
            if client_id in self._clients:
                lifecycle = self._clients[client_id]
                
                # 尝试断开连接
                client = lifecycle.client_ref()
                if client and hasattr(client, 'disconnect'):
                    try:
                        if asyncio.iscoroutinefunction(client.disconnect):
                            await client.disconnect()
                        else:
                            client.disconnect()
                    except Exception as e:
                        logger.warning(f"断开客户端连接失败: {e}")
                
                # 移除生命周期信息
                del self._clients[client_id]
                logger.debug(f"客户端清理完成: {client_id}")
                
        except Exception as e:
            logger.error(f"清理客户端失败: {e}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self._initialized:
            try:
                await self._perform_cleanup()
                await asyncio.sleep(self._cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理循环错误: {e}")
                await asyncio.sleep(self._cleanup_interval)
    
    async def _perform_cleanup(self):
        """执行清理"""
        current_time = datetime.now()
        cleanup_candidates = []
        
        async with self._lock:
            for client_id, lifecycle in self._clients.items():
                # 检查客户端是否还存在
                if lifecycle.client_ref() is None:
                    cleanup_candidates.append(client_id)
                    continue
                
                # 检查是否超过最大空闲时间
                idle_time = current_time - lifecycle.last_accessed
                if idle_time > timedelta(seconds=self._max_idle_time):
                    cleanup_candidates.append(client_id)
        
        # 清理候选客户端
        for client_id in cleanup_candidates:
            await self._cleanup_client(client_id)
            logger.info(f"自动清理空闲客户端: {client_id}")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self._initialized:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(self._health_check_interval)
    
    async def _perform_health_check(self):
        """执行健康检查"""
        async with self._lock:
            for client_id, lifecycle in list(self._clients.items()):
                client = lifecycle.client_ref()
                if client is None:
                    continue
                
                try:
                    # 优先使用异步健康检查方法，避免强制同步连接
                    healthy = None

                    if hasattr(client, 'ahealth_check'):
                        # 优先使用异步健康检查
                        health_result = await client.ahealth_check()
                        healthy = self._parse_health_result(health_result)
                        logger.debug(f"使用异步健康检查: {client_id}, 结果: {healthy}, 原始结果: {health_result}")
                    elif hasattr(client, 'health_check'):
                        # 回退到同步健康检查
                        if asyncio.iscoroutinefunction(client.health_check):
                            health_result = await client.health_check()
                        else:
                            health_result = client.health_check()
                        healthy = self._parse_health_result(health_result)
                        logger.debug(f"使用同步健康检查: {client_id}, 结果: {healthy}, 原始结果: {health_result}")

                    if healthy is not None:
                        if not healthy:
                            lifecycle.error_count += 1
                            await self._update_state(client_id, ClientState.ERROR)
                            logger.warning(f"客户端健康检查失败: {client_id}")
                        else:
                            # 重置错误计数
                            lifecycle.error_count = 0
                            if lifecycle.state == ClientState.ERROR:
                                await self._update_state(client_id, ClientState.CONNECTED)
                    else:
                        logger.debug(f"客户端无健康检查方法: {client_id}")
                
                except Exception as e:
                    lifecycle.error_count += 1
                    await self._update_state(client_id, ClientState.ERROR)
                    logger.warning(f"客户端健康检查异常: {client_id}, {e}")

    def _parse_health_result(self, health_result: Any) -> bool:
        """解析健康检查结果，支持多种格式

        支持的格式：
        1. {"healthy": True/False}  - PGVector等客户端
        2. {"status": "healthy"/"unhealthy"/"disconnected"}  - MySQL等RDB客户端
        3. 直接返回布尔值
        4. 其他格式的兼容处理

        Args:
            health_result: 健康检查结果

        Returns:
            bool: 是否健康
        """
        if not isinstance(health_result, dict):
            # 非字典类型，直接转换为布尔值
            return bool(health_result)

        # 优先检查 'healthy' 字段（PGVector等客户端）
        if 'healthy' in health_result:
            return bool(health_result['healthy'])

        # 检查 'status' 字段（MySQL等RDB客户端）
        if 'status' in health_result:
            status = health_result['status']
            if isinstance(status, str):
                return status.lower() in ['healthy', 'connected', 'ok', 'success']
            else:
                return bool(status)

        # 检查其他可能的字段
        for field in ['health', 'is_healthy', 'connected', 'is_connected', 'ok']:
            if field in health_result:
                return bool(health_result[field])

        # 如果没有找到明确的健康状态字段，检查是否有错误信息
        if 'error' in health_result and health_result['error']:
            return False

        # 默认情况：如果返回了字典但没有明确的状态，认为是健康的
        return True

    async def cleanup(self):
        """清理所有资源"""
        logger.info("开始清理生命周期管理器...")
        
        self._initialized = False
        
        # 取消后台任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有客户端
        async with self._lock:
            for client_id in list(self._clients.keys()):
                await self._cleanup_client(client_id)
        
        logger.info("生命周期管理器清理完成")
