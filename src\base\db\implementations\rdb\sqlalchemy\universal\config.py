

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, quote_plus

from .exceptions import ConfigurationError
from .....base.rdb import ConnectionConfig


@dataclass
class UniversalConnectionConfig(ConnectionConfig):
    """Universal database connection configuration
    
    继承ConnectionConfig，添加Universal特有的功能
    """
    
    # Universal特有的配置
    database_url: Optional[str] = None  # 支持URL和组件两种初始化方式
    dialect: Optional[str] = None  # 数据库方言（统一database_type概念）
    
    # 性能和缓存设置
    enable_cache: bool = True
    cache_size: int = 1000
    enable_query_optimization: bool = True
    
    # 异步设置
    async_fallback: bool = True
    
    # 数据库特定选项
    dialect_options: Dict[str, Any] = field(default_factory=dict)
    
    # 连接重试设置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # SQLAlchemy引擎设置
    echo_pool: bool = False
    future: bool = True
    pool_pre_ping: bool = True

    def __post_init__(self):
        """初始化后处理"""
        # 如果提供了database_url，解析它来填充基础字段
        if self.database_url and not self.host:
            self._parse_database_url()
        
        # 验证配置
        self._validate_config()
    
    def _parse_database_url(self):
        """解析database_url并填充基础连接字段"""
        if not self.database_url:
            return
            
        try:
            parsed = urlparse(self.database_url)
            
            # 解析scheme获取数据库方言和驱动
            scheme_parts = parsed.scheme.split('+')
            if not self.dialect:
                self.dialect = scheme_parts[0]
            
            # 填充连接信息（如果尚未设置）
            if not self.host and parsed.hostname:
                self.host = parsed.hostname
            if not self.port and parsed.port:
                self.port = parsed.port
            if not self.database and parsed.path:
                self.database = parsed.path.lstrip('/')
            if not self.username and parsed.username:
                self.username = parsed.username
            if not self.password and parsed.password:
                self.password = parsed.password
            
            # 解析查询参数
            if parsed.query:
                query_params = {k: v[0] if len(v) == 1 else v 
                              for k, v in parse_qs(parsed.query).items()}
                
                # 将查询参数添加到dialect_options
                self.dialect_options.update(query_params)
                
        except Exception as e:
            raise ConfigurationError(f"Invalid database URL '{self.database_url}': {e}")
    
    def _validate_config(self):
        """验证配置参数"""
        # 基础验证 - SQLite不需要host
        if not self.database_url and not self.host and self.dialect != 'sqlite':
            raise ConfigurationError("Either database_url or host must be provided")
        
        if self.pool_size < 1:
            raise ConfigurationError("pool_size must be at least 1")
        
        if self.max_overflow < 0:
            raise ConfigurationError("max_overflow cannot be negative")
        
        if self.pool_timeout <= 0:
            raise ConfigurationError("pool_timeout must be positive")
    


    
    @staticmethod
    def _infer_dialect_from_port(port: int) -> str:
        """从端口推断数据库方言"""
        port_to_dialect = {
            3306: 'mysql',
            5432: 'postgresql',
            1521: 'oracle',
            1433: 'mssql',
            0: 'sqlite',  # SQLite不使用网络端口
        }

        return port_to_dialect.get(port, 'mysql')  # 默认为MySQL
    

    
    def build_database_url(self) -> str:
        """构建数据库URL"""
        if self.database_url:
            return self.database_url

        # 从组件构建URL
        dialect = self.dialect
        if not dialect:
            # 如果没有dialect，尝试从端口推断
            dialect = self._infer_dialect_from_port(self.port)

        if not dialect:
            raise ConfigurationError("dialect is required to build URL")

        dialect = dialect.lower()

        if dialect == 'sqlite':
            return f"sqlite:///{self.database}"
        
        # 默认驱动
        default_drivers = {
            'mysql': 'pymysql',
            'postgresql': 'psycopg2',
            'postgres': 'psycopg2',
            'oracle': 'cx_oracle',
            'mssql': 'pyodbc',
        }
        
        driver = default_drivers.get(dialect)
        scheme = f"{dialect}+{driver}" if driver else dialect
        
        # URL编码用户名和密码
        encoded_username = quote_plus(self.username)
        encoded_password = quote_plus(self.password)
        
        return f"{scheme}://{encoded_username}:{encoded_password}@{self.host}:{self.port}/{self.database}"
    
    def get_engine_kwargs(self) -> Dict[str, Any]:
        """获取SQLAlchemy引擎创建参数"""
        kwargs = {
            'echo': self.echo,
            'echo_pool': self.echo_pool,
            'future': self.future,
        }

        # SQLite不支持连接池参数
        if self.dialect and self.dialect.lower() != 'sqlite':
            kwargs.update({
                'pool_size': self.pool_size,
                'max_overflow': self.max_overflow,
                'pool_timeout': self.pool_timeout,
                'pool_recycle': self.pool_recycle,
                'pool_pre_ping': self.pool_pre_ping,
            })

        # 添加数据库特定选项
        if self.dialect_options:
            kwargs.update(self.dialect_options)

        # 移除None值
        kwargs = {k: v for k, v in kwargs.items() if v is not None}

        return kwargs
    
    def get_async_engine_kwargs(self) -> Dict[str, Any]:
        """获取异步SQLAlchemy引擎创建参数"""
        kwargs = self.get_engine_kwargs()
        
        # 移除异步不支持的选项
        kwargs.pop('echo_pool', None)
        
        return kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        
        # 添加ConnectionConfig的字段
        for field_name in ['host', 'port', 'database', 'username', 'password',
                          'charset', 'timezone', 'connect_timeout', 'read_timeout',
                          'write_timeout', 'ssl_enabled', 'ssl_cert', 'ssl_key',
                          'ssl_ca', 'pool_size', 'max_overflow', 'pool_timeout',
                          'pool_recycle', 'echo', 'autocommit', 'isolation_level']:
            if hasattr(self, field_name):
                result[field_name] = getattr(self, field_name)
        
        # 添加Universal特有的字段
        result.update({
            'database_url': self.database_url,
            'dialect': self.dialect,
            'enable_cache': self.enable_cache,
            'cache_size': self.cache_size,
            'enable_query_optimization': self.enable_query_optimization,
            'async_fallback': self.async_fallback,
            'dialect_options': self.dialect_options,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'echo_pool': self.echo_pool,
            'future': self.future,
            'pool_pre_ping': self.pool_pre_ping,
        })
        
        return result
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'UniversalConnectionConfig':
        """从字典创建配置 - 支持Hydra风格的简单配置，智能过滤冗余参数"""

        # 创建配置副本，避免修改原字典
        config = config_dict.copy()

        # 定义所有支持的参数（包括继承的ConnectionConfig参数）
        supported_params = {
            # ConnectionConfig 基础参数
            'host', 'port', 'database', 'username', 'password', 'charset', 'timezone',
            'connect_timeout', 'read_timeout', 'write_timeout', 'ssl_enabled', 'ssl_cert',
            'ssl_key', 'ssl_ca', 'pool_size', 'max_overflow', 'pool_timeout', 'pool_recycle',
            'echo', 'autocommit', 'isolation_level',

            # UniversalConnectionConfig 特有参数
            'database_url', 'dialect', 'enable_cache', 'cache_size', 'enable_query_optimization',
            'async_fallback', 'dialect_options', 'max_retries', 'retry_delay', 'echo_pool',
            'future', 'pool_pre_ping'
        }

        # 过滤配置，只保留支持的参数
        filtered_config = {k: v for k, v in config.items() if k in supported_params}

        # 记录被过滤的参数（用于调试）
        filtered_out = set(config.keys()) - supported_params
        if filtered_out:
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"UniversalConnectionConfig 过滤了冗余参数: {filtered_out}")

        # 如果没有指定dialect，尝试从端口推断
        if 'dialect' not in filtered_config and 'port' in filtered_config:
            filtered_config['dialect'] = cls._infer_dialect_from_port(filtered_config['port'])

        # 验证必需参数
        required_params = {'host', 'port', 'database', 'username', 'password'}
        missing_params = required_params - set(filtered_config.keys())
        if missing_params:
            raise ValueError(f"缺少必需的连接参数: {missing_params}")

        # 如果缺少可选字段，使用企业级优化的默认值
        defaults = {
            'charset': 'utf8mb4',
            'pool_size': 30,           # 优化：20 → 30 (支持更高并发)
            'max_overflow': 70,        # 优化：40 → 70 (总计100个连接)
            'pool_timeout': 60.0,      # 优化：30 → 60秒 (更宽松的超时)
            'pool_recycle': 3600,      # 优化：恢复到3600秒 (平衡性能和资源)
            'echo': False,
            'enable_cache': True,
            'cache_size': 1000,
            'echo_pool': False,
            'future': True,
            'pool_pre_ping': True,     # 保持连接健康检查
        }

        # 合并默认值（只对缺失的键）
        for key, default_value in defaults.items():
            if key not in filtered_config:
                filtered_config[key] = default_value

        return cls(**filtered_config)



