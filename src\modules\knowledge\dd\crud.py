"""
DD系统统一CRUD操作 - 完整批量操作版本

基于 UniversalSQLAlchemyClient 的强大批量操作功能，
提供5张表的统一高性能CRUD功能：
- dd_report_data
- dd_submission_data (包含向量操作)
- dd_departments
- biz_dd_pre_distribution
- biz_dd_post_distribution

设计原则：
- 统一的接口设计：每个实体只有一个update和delete方法，同时支持单条和批量操作
- 直接使用客户端的批量操作方法（abatch_insert, abatch_update, abatch_delete, abatch_query）
- 条件参数统一格式，避免重复方法
- 支持并发批量处理，提升性能

完整的批量操作支持：
1. 批量创建（Create）：
   - batch_create_departments() - 批量创建部门
   - batch_create_pre_distributions() - 批量创建分发前记录
   - batch_create_post_distributions() - 批量创建分发后记录
   - batch_create_report_data() - 批量创建报表数据
   - create_submission_data() - 已支持批量创建填报数据

2. 批量更新（Update）：
   - update_departments() - 统一更新方法，支持单条和批量
   - update_pre_distributions() - 统一更新方法，支持单条和批量
   - update_post_distributions() - 统一更新方法，支持单条和批量
   - update_report_data() - 统一更新方法，支持单条和批量
   - update_submission_data() - 统一更新方法，支持单条和批量

3. 批量删除（Delete）：
   - delete_departments() - 统一删除方法，支持单条和批量
   - delete_pre_distributions() - 统一删除方法，支持单条和批量
   - delete_post_distributions() - 统一删除方法，支持单条和批量
   - delete_report_data() - 统一删除方法，支持单条和批量
   - delete_submission_data() - 统一删除方法，支持单条和批量

4. 批量查询（Query）：
   - batch_query_departments() - 批量查询部门
   - batch_query_pre_distributions() - 批量查询分发前记录
   - batch_query_post_distributions() - 批量查询分发后记录
   - batch_query_report_data() - 批量查询报表数据
   - batch_query_submission_data() - 批量查询填报数据
   - batch_get_submission_data() - 按ID批量获取填报数据

条件格式统一：
- 单条操作：conditions={"id": 123} 或 {"dept_id": "dept001"}
- 批量操作：conditions=[{"id": 123}, {"id": 124}] 或 [{"dept_type": "temp"}]
- ID列表删除：conditions=[{"id": 1}, {"id": 2}, {"id": 3}]

性能优化特性：
- 所有批量方法都直接使用UniversalSQLAlchemyClient的高性能批量工具
- 支持自定义batch_size、max_concurrency、timeout_per_batch参数
- 并发批量处理，大幅提升大数据量操作性能
- 智能错误处理和结果汇总
"""

from re import I
from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime
import asyncio
from functools import lru_cache

from .shared.exceptions import DDError, DDValidationError, DDNotFoundError
from .shared.constants import DDConstants, DDTableNames
from .shared.utils import DDUtils

logger = logging.getLogger(__name__)


class DDCrud:
    """DD系统CRUD操作类 - 简化版本"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化CRUD操作

        Args:
            rdb_client: 关系型数据库客户端（MySQL）
            vdb_client: 向量数据库客户端（PGVector，可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # knowledge_id缓存（简单的内存缓存）
        self._knowledge_id_cache = {}
        self._cache_max_size = 1000
        self._cache_ttl = 300  # 5分钟TTL
    
    # ==================== 部门管理 ====================

    async def create_department(self, dept_data: Dict[str, Any]) -> str:
        """创建部门"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_DEPARTMENTS,
            data=[dept_data]
        )

        if not result.success:
            raise DDError(f"创建部门失败: {result.error_message}")

        return dept_data['dept_id']

    async def batch_create_departments(
        self,
        dept_data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[str]:
        """
        批量创建部门 - 高性能版本

        Args:
            dept_data_list: 部门数据列表
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[str]: 创建的部门ID列表

        Examples:
            dept_list = [
                {"dept_id": "DEPT001", "dept_name": "财务部", "dept_type": "normal"},
                {"dept_id": "DEPT002", "dept_name": "人事部", "dept_type": "normal"}
            ]
            dept_ids = await crud.batch_create_departments(dept_list)
        """
        if not dept_data_list:
            return []

        # 使用批量插入（时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_DEPARTMENTS,
            data=dept_data_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        if not result.success:
            raise DDError(f"批量创建部门失败: {result.error_message}")

        return [dept_data['dept_id'] for dept_data in dept_data_list]

    async def get_department(self, dept_id: str = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取部门

        Args:
            dept_id: 部门ID（主键，默认查询条件）
            **where_conditions: 其他查询条件，如 dept_name='xxx', dept_type='normal'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if dept_id:
            where = {'dept_id': dept_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise DDValidationError("必须提供 dept_id 或其他查询条件")

        results = await self._aselect(
            table=DDTableNames.BIZ_DEPARTMENTS,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_departments(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的部门更新方法 - 支持单条和批量操作

        Args:
            updates: 更新数据，支持以下格式：
                - 单条更新: {"dept_name": "新名称", "is_active": True}
                - 批量更新: [{"data": {...}, "filters": {...}}, ...]
            conditions: 更新条件（仅在单条更新时使用），支持：
                - 主键条件: {"dept_id": "dept001"}
                - 其他条件: {"dept_type": "normal", "is_active": True}
                - 批量条件: [{"dept_id": "dept001"}, {"dept_id": "dept002"}]
            batch_size: 每批处理的记录数，默认100
            max_concurrency: 最大并发批次数，默认3
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            bool: 操作是否成功

        Examples:
            # 单条更新（按主键）
            await crud.update_departments(
                updates={"dept_name": "新部门名称"},
                conditions={"dept_id": "dept001"}
            )

            # 单条更新（按其他条件）
            await crud.update_departments(
                updates={"is_active": False},
                conditions={"dept_type": "temporary"}
            )

            # 批量更新
            await crud.update_departments([
                {"data": {"dept_name": "部门1"}, "filters": {"dept_id": "dept001"}},
                {"data": {"dept_name": "部门2"}, "where": {"dept_id": "dept002"}}
            ])

            # 按ID列表批量更新（统一条件格式）
            await crud.update_departments(
                updates={"is_active": False},
                conditions=[{"dept_id": "dept001"}, {"dept_id": "dept002"}]
            )
        """
        return await self._unified_update(
            table=DDTableNames.BIZ_DEPARTMENTS,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def delete_departments(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的部门删除方法 - 支持单条和批量操作

        Args:
            conditions: 删除条件，支持以下格式：
                - 单条删除: {"dept_id": "dept001"}
                - 批量删除: [{"dept_id": "dept001"}, {"dept_id": "dept002"}]
                - 复杂条件: [{"dept_type": "temporary"}, {"is_active": False}]
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            bool: 操作是否成功

        Examples:
            # 单条删除（按主键）
            await crud.delete_departments({"dept_id": "dept001"})

            # 单条删除（按其他条件）
            await crud.delete_departments({"dept_type": "temporary"})

            # 按ID列表批量删除
            await crud.delete_departments([
                {"dept_id": "dept001"},
                {"dept_id": "dept002"},
                {"dept_id": "dept003"}
            ])

            # 按条件批量删除
            await crud.delete_departments([
                {"dept_type": "temporary"},
                {"is_active": False}
            ])
        """
        return await self._unified_delete(
            table=DDTableNames.BIZ_DEPARTMENTS,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_departments(
        self,
        dept_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """查询部门列表"""
        where = {}
        if dept_type:
            where['dept_type'] = dept_type
        if is_active is not None:
            where['is_active'] = is_active

        return await self._aselect(
            table=DDTableNames.BIZ_DEPARTMENTS,
            where=where if where else None,
            order_by=['dept_name'],
            limit=limit,
            offset=offset
        )

    async def batch_query_departments(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询部门 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"dept_id": "DEPT001"},
                    {"dept_type": "normal", "is_active": True}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            conditions = [
                {"dept_id": "DEPT001"},
                {"dept_id": "DEPT002"}
            ]
            departments = await crud.batch_query_departments(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询部门: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.BIZ_DEPARTMENTS,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询部门完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询部门失败: {e}")
            raise DDError(f"批量查询失败: {e}")
    
    # ==================== 分发前管理 ====================

    async def create_pre_distribution(self, pre_data: Dict[str, Any]) -> int:
        """创建分发前记录"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            data=[pre_data]
        )

        if not result.success:
            raise DDError(f"创建分发前记录失败: {result.error_message}")

        # 查询刚插入的记录来获取ID
        if 'submission_id' in pre_data:
            inserted_records = await self._aselect(
                table=DDTableNames.BIZ_PRE_DISTRIBUTION,
                where={'submission_id': pre_data['submission_id']},
                limit=1
            )
            return inserted_records[0].get('id', 0) if inserted_records else 0
        else:
            return 0  # 如果没有唯一标识符，返回0

    async def batch_create_pre_distributions(
        self,
        pre_data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[int]:
        """
        批量创建分发前记录 - 高性能版本

        Args:
            pre_data_list: 分发前数据列表
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[int]: 创建的记录ID列表

        Examples:
            pre_list = [
                {"submission_id": "SUB001", "version": "v1.0", "dr01": "layer1"},
                {"submission_id": "SUB002", "version": "v1.0", "dr01": "layer2"}
            ]
            pre_ids = await crud.batch_create_pre_distributions(pre_list)
        """
        if not pre_data_list:
            return []

        # 使用批量插入（时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            data=pre_data_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        if not result.success:
            raise DDError(f"批量创建分发前记录失败: {result.error_message}")

        # 批量查询插入的记录来获取ID
        submission_ids = [data['submission_id'] for data in pre_data_list if 'submission_id' in data]
        if not submission_ids:
            return [0] * len(pre_data_list)

        # 使用批量查询获取ID
        queries = [
            {"data": ["id"], "filters": {"submission_id": submission_id}}
            for submission_id in submission_ids
        ]

        query_results = await self.rdb_client.abatch_query(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            queries=queries,
            batch_size=batch_size,
            max_concurrency=max_concurrency
        )

        # 提取ID
        ids = []
        for query_result in query_results:
            if query_result.data and len(query_result.data) > 0:
                ids.append(query_result.data[0].get('id', 0))
            else:
                ids.append(0)

        return ids

    async def get_pre_distribution(self, pre_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取分发前记录

        Args:
            pre_id: 主键ID（默认查询条件）
            **where_conditions: 其他查询条件，如 submission_id='xxx', version='v1.0'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if pre_id:
            where = {'id': pre_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise DDValidationError("必须提供 pre_id 或其他查询条件")

        results = await self._aselect(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_pre_distributions(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的分发前记录更新方法 - 支持单条和批量操作

        Args:
            updates: 更新数据，支持单条和批量格式
            conditions: 更新条件，支持单条和批量格式
            batch_size: 每批处理的记录数，默认100
            max_concurrency: 最大并发批次数，默认3
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Examples:
            # 单条更新（按主键）
            await crud.update_pre_distributions(
                updates={"status": "processed"},
                conditions={"id": 123}
            )

            # 按ID列表批量更新
            await crud.update_pre_distributions(
                updates={"status": "archived"},
                conditions=[{"id": 123}, {"id": 124}]
            )
        """
        return await self._unified_update(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def get_pre_distribution_by_unique_key(self, submission_id: str, version: str) -> Optional[Dict[str, Any]]:
        """根据唯一键获取分发前记录（便捷方法）"""
        return await self.get_pre_distribution(submission_id=submission_id, version=version)

    async def delete_pre_distributions(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的分发前记录删除方法 - 支持单条和批量操作

        Args:
            conditions: 删除条件，支持单条和批量格式
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Examples:
            # 单条删除（按主键）
            await crud.delete_pre_distributions({"id": 123})

            # 按ID列表批量删除
            await crud.delete_pre_distributions([{"id": 123}, {"id": 124}])
        """
        return await self._unified_delete(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_pre_distributions(
        self,
        version: Optional[str] = None,
        data_layer: Optional[str] = None,
        report_type: Optional[str] = None,
        submission_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        查询分发前记录列表

        Args:
            version: 版本号
            data_layer: 数据层（映射到dr01字段）
            report_type: 报表类型
            submission_id: 提交ID
            limit: 限制数量
            offset: 偏移量
            **kwargs: 其他查询条件，支持任意字段（如dr07等）

        Returns:
            分发前记录列表
        """
        where = {}
        if version:
            where['version'] = version
        if data_layer:
            where['dr01'] = data_layer  # 使用真实字段名
        if report_type:
            where['report_type'] = report_type
        if submission_id:
            where['submission_id'] = submission_id

        # 添加kwargs中的其他查询条件
        for key, value in kwargs.items():
            if value is not None:  # 只添加非None的值
                where[key] = value

        return await self._aselect(
            table=DDTableNames.BIZ_PRE_DISTRIBUTION,
            where=where if where else None,
            limit=limit,
            offset=offset
        )

    async def batch_query_pre_distributions(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询分发前记录 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"submission_id": "SUB001", "version": "v1.0"},
                    {"dr01": "layer1", "report_type": "monthly"}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            conditions = [
                {"submission_id": "SUB001", "version": "v1.0"},
                {"submission_id": "SUB002", "version": "v1.0"}
            ]
            pre_records = await crud.batch_query_pre_distributions(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询分发前记录: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.BIZ_PRE_DISTRIBUTION,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询分发前记录完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询分发前记录失败: {e}")
            raise DDError(f"批量查询失败: {e}")
    
    # ==================== 分发后管理 ====================

    async def create_post_distribution(self, post_data: Dict[str, Any]) -> int:
        """创建分发后记录"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            data=[post_data]
        )

        if not result.success:
            raise DDError(f"创建分发后记录失败: {result.error_message}")

        # 查询刚插入的记录来获取ID
        if 'pre_distribution_id' in post_data:
            inserted_records = await self._aselect(
                table=DDTableNames.BIZ_POST_DISTRIBUTION,
                where={'pre_distribution_id': post_data['pre_distribution_id']},
                limit=1
            )
            return inserted_records[0].get('id', 0) if inserted_records else 0
        else:
            return 0  # 如果没有唯一标识符，返回0

    async def batch_create_post_distributions(
        self,
        post_data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[int]:
        """
        批量创建分发后记录 - 高性能版本

        Args:
            post_data_list: 分发后数据列表
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[int]: 创建的记录ID列表

        Examples:
            post_list = [
                {"pre_distribution_id": 1, "dept_id": "DEPT001", "submission_id": "SUB001"},
                {"pre_distribution_id": 2, "dept_id": "DEPT002", "submission_id": "SUB002"}
            ]
            post_ids = await crud.batch_create_post_distributions(post_list)
        """
        if not post_data_list:
            return []

        # 使用批量插入（时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            data=post_data_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        if not result.success:
            raise DDError(f"批量创建分发后记录失败: {result.error_message}")

        # 批量查询插入的记录来获取ID
        pre_distribution_ids = [data['pre_distribution_id'] for data in post_data_list if 'pre_distribution_id' in data]
        if not pre_distribution_ids:
            return [0] * len(post_data_list)

        # 使用批量查询获取ID
        queries = [
            {"data": ["id"], "filters": {"pre_distribution_id": pre_id}}
            for pre_id in pre_distribution_ids
        ]

        query_results = await self.rdb_client.abatch_query(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            queries=queries,
            batch_size=batch_size,
            max_concurrency=max_concurrency
        )

        # 提取ID
        ids = []
        for query_result in query_results:
            if query_result.data and len(query_result.data) > 0:
                ids.append(query_result.data[0].get('id', 0))
            else:
                ids.append(0)

        return ids

    async def get_post_distribution(self, post_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取分发后记录

        Args:
            post_id: 主键ID（默认查询条件）
            **where_conditions: 其他查询条件，如 submission_id='xxx', version='v1.0', dept_id='xxx'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if post_id:
            where = {'id': post_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise DDValidationError("必须提供 post_id 或其他查询条件")

        results = await self._aselect(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_post_distributions(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的分发后记录更新方法 - 支持单条和批量操作

        Examples:
            # 单条更新（按主键）
            await crud.update_post_distributions(
                updates={"status": "completed"},
                conditions={"id": 123}
            )
        """
        return await self._unified_update(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def get_post_distribution_by_unique_key(self, submission_id: str, version: str, dept_id: str) -> Optional[Dict[str, Any]]:
        """根据唯一键获取分发后记录（便捷方法）"""
        return await self.get_post_distribution(submission_id=submission_id, version=version, dept_id=dept_id)

    async def delete_post_distributions(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的分发后记录删除方法 - 支持单条和批量操作

        Examples:
            # 单条删除（按主键）
            await crud.delete_post_distributions({"id": 123})

            # 按ID列表批量删除
            await crud.delete_post_distributions([{"id": 123}, {"id": 124}])
        """
        return await self._unified_delete(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    # 支持的额外查询字段白名单
    ALLOWED_POST_DISTRIBUTION_FIELDS = {
        'version', 'dr07', 'create_time', 'update_time',
        'status', 'priority', 'category', 'source_type',
        'business_type', 'data_source', 'process_status'
    }

    async def list_post_distributions(
        self,
        pre_distribution_id: Optional[int] = None,
        dept_id: Optional[str] = None,
        submission_id: Optional[str] = None,
        report_type: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        查询分发后记录列表

        Args:
            pre_distribution_id: 分发前记录ID
            dept_id: 部门ID
            submission_id: 提交ID
            report_type: 报表类型
            limit: 限制数量
            offset: 偏移量
            **kwargs: 其他查询条件，支持以下字段：
                - version: 版本号
                - dr07: 业务字段
                - create_time: 创建时间
                - update_time: 更新时间
                - status: 状态
                - priority: 优先级
                - category: 分类
                - source_type: 数据源类型
                - business_type: 业务类型
                - data_source: 数据来源
                - process_status: 处理状态

        Returns:
            分发后记录列表

        Raises:
            ValueError: 当使用不支持的查询字段时

        Example:
            # 基础查询
            records = await crud.list_post_distributions(dept_id="DEPT001")

            # 使用额外字段查询
            records = await crud.list_post_distributions(
                dept_id="DEPT001",
                version="v1.0",
                dr07="BUSINESS_TYPE"
            )
        """
        where = {}

        # 处理标准参数
        if pre_distribution_id:
            where['pre_distribution_id'] = pre_distribution_id
        if dept_id:
            where['dept_id'] = dept_id
        if submission_id:
            where['submission_id'] = submission_id
        if report_type:
            where['report_type'] = report_type

        # 验证并添加kwargs中的其他查询条件
        for key, value in kwargs.items():
            if value is not None:
                if key not in self.ALLOWED_POST_DISTRIBUTION_FIELDS:
                    raise ValueError(
                        f"不支持的查询字段: {key}. "
                        f"支持的字段: {', '.join(sorted(self.ALLOWED_POST_DISTRIBUTION_FIELDS))}"
                    )
                where[key] = value

        return await self._aselect(
            table=DDTableNames.BIZ_POST_DISTRIBUTION,
            where=where if where else None,
            limit=limit,
            offset=offset
        )

    async def batch_query_post_distributions(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询分发后记录 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"dept_id": "30239", "report_code": "S71_ADS_RELEASE_V0"},
                    {"dept_id": "30240", "report_code": "S71_ADS_RELEASE_V1"}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            # 批量查询不同条件的记录
            conditions = [
                {"dept_id": "30239", "report_code": "S71_ADS_RELEASE_V0"},
                {"dept_id": "30239", "report_code": "S71_ADS_RELEASE_V1"}
            ]
            records = await crud.batch_query_post_distributions(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                # 处理字段映射：report_code -> version
                mapped_conditions = {}
                for key, value in conditions.items():
                    if key == 'report_code':
                        # 关键修复：将report_code映射到数据库的version字段
                        mapped_conditions['version'] = value
                        logger.debug(f"字段映射: report_code='{value}' -> version='{value}'")
                    else:
                        mapped_conditions[key] = value

                # 验证条件中的字段
                for key in mapped_conditions.keys():
                    if key not in {'dept_id', 'version', 'submission_id', 'pre_distribution_id',
                                   'report_type'} and key not in self.ALLOWED_POST_DISTRIBUTION_FIELDS:
                        logger.warning(f"批量查询中包含不支持的字段: {key}")

                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": mapped_conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询分发后记录: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.BIZ_POST_DISTRIBUTION,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询分发后记录失败: {e}")
            raise DDError(f"批量查询失败: {e}")
    
    # ==================== 报表数据管理 ====================

    async def create_report_data(self, report_data: Dict[str, Any]) -> int:
        """创建报表数据"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.KB_REPORT_DATA,
            data=[report_data]
        )

        if not result.success:
            raise DDError(f"创建报表数据失败: {result.error_message}")

        # 获取插入的ID - 使用优化方法
        if hasattr(result, 'inserted_ids') and result.inserted_ids:
            return result.inserted_ids[0]
        else:
            # 使用优化的ID获取方法
            return await self._get_single_insert_id_optimized(
                table=DDTableNames.KB_REPORT_DATA,
                unique_conditions={
                    'knowledge_id': report_data['knowledge_id'],
                    'report_code': report_data['report_code']
                },
                fallback_order_by=['id DESC']
            )

    async def batch_create_report_data(
        self,
        report_data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[int]:
        """
        批量创建报表数据 - 高性能版本

        Args:
            report_data_list: 报表数据列表
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[int]: 创建的记录ID列表

        Examples:
            report_list = [
                {"knowledge_id": "KB001", "report_code": "RPT001", "report_name": "报表1"},
                {"knowledge_id": "KB002", "report_code": "RPT002", "report_name": "报表2"}
            ]
            report_ids = await crud.batch_create_report_data(report_list)
        """
        if not report_data_list:
            return []

        # 使用批量插入（时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.KB_REPORT_DATA,
            data=report_data_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        if not result.success:
            raise DDError(f"批量创建报表数据失败: {result.error_message}")

        # 尝试从结果中获取插入的ID
        if hasattr(result, 'inserted_ids') and result.inserted_ids:
            return result.inserted_ids
        else:
            # 批量查询插入的记录来获取ID
            queries = []
            for report_data in report_data_list:
                if 'knowledge_id' in report_data and 'report_code' in report_data:
                    queries.append({
                        "data": ["id"],
                        "filters": {
                            "knowledge_id": report_data['knowledge_id'],
                            "report_code": report_data['report_code']
                        }
                    })

            if not queries:
                return [0] * len(report_data_list)

            query_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_REPORT_DATA,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency
            )

            # 提取ID
            ids = []
            for query_result in query_results:
                if query_result.data and len(query_result.data) > 0:
                    ids.append(query_result.data[0].get('id', 0))
                else:
                    ids.append(0)

            return ids

    async def get_report_data(self, report_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取报表数据

        Args:
            report_id: 主键ID（默认查询条件）
            **where_conditions: 其他查询条件，如 knowledge_id='xxx', version='v1.0'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if report_id is not None and report_id > 0:
            where = {'id': report_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise DDValidationError("必须提供 report_id 或其他查询条件")

        results = await self._aselect(
            table=DDTableNames.KB_REPORT_DATA,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_report_data(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的报表数据更新方法 - 支持单条和批量操作

        Examples:
            # 单条更新（按主键）
            await crud.update_report_data(
                updates={"report_name": "新报表名称"},
                conditions={"id": 123}
            )
        """
        return await self._unified_update(
            table=DDTableNames.KB_REPORT_DATA,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def delete_report_data(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的报表数据删除方法 - 支持单条和批量操作

        Examples:
            # 单条删除（按主键）
            await crud.delete_report_data({"id": 123})

            # 按ID列表批量删除
            await crud.delete_report_data([{"id": 123}, {"id": 124}])
        """
        return await self._unified_delete(
            table=DDTableNames.KB_REPORT_DATA,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_report_data(
        self,
        knowledge_id: Optional[str] = None,
        version: Optional[str] = None,
        report_layer: Optional[str] = None,
        report_type: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """查询报表数据列表"""
        where = {}
        if knowledge_id:
            where['knowledge_id'] = knowledge_id
        if version:
            where['version'] = version
        if report_layer:
            where['report_layer'] = report_layer
        if report_type:
            where['report_type'] = report_type

        return await self._aselect(
            table=DDTableNames.KB_REPORT_DATA,
            where=where if where else None,
            limit=limit,
            offset=offset
        )

    async def batch_query_report_data(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询报表数据 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"knowledge_id": "KB001", "version": "v1.0"},
                    {"report_layer": "layer1", "report_type": "monthly"}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            conditions = [
                {"knowledge_id": "KB001", "version": "v1.0"},
                {"knowledge_id": "KB002", "version": "v1.0"}
            ]
            report_records = await crud.batch_query_report_data(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询报表数据: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_REPORT_DATA,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询报表数据完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询报表数据失败: {e}")
            raise DDError(f"批量查询失败: {e}")
    
    # ==================== 填报数据管理（包含向量操作）====================

    async def create_submission_data(self, submission_data: Union[Dict[str, Any], List[Dict[str, Any]]], knowledge_id: Optional[str] = None) -> Tuple[List[int], List[Any]]:
        """
        创建填报数据（包含向量化）- 支持单条和批量插入

        Args:
            submission_data: 单条记录字典或多条记录列表
            knowledge_id: 可选的knowledge_id，如果提供则直接使用，否则自动获取

        Returns:
            (submission_pks, vector_ids)  # submission_pks是dd_submission_data表的主键id列表
        """
        try:
            # 统一处理为列表格式
            if isinstance(submission_data, dict):
                submission_data_list = [submission_data]
                is_single = True
            else:
                submission_data_list = submission_data
                is_single = False

            # 批量创建填报数据
            result = await self.rdb_client.abatch_insert(
                table=DDTableNames.KB_SUBMISSION_DATA,
                data=submission_data_list
            )

            if not result.success:
                raise DDError(f"创建填报数据失败: {result.error_message}")

            # 🔧 修复：使用精确查询获取刚插入记录的主键ID，避免通过submission_id查询历史数据
            submission_pks = []

            # 使用多个字段组合精确查询刚插入的记录
            queries = []
            for i, data in enumerate(submission_data_list):
                # 构建精确查询条件：使用多个字段组合来精确匹配刚插入的记录
                query_filters = {
                    "submission_id": data['submission_id'],
                    "version": data.get('version', ''),
                    "dr01": data.get('dr01', ''),
                    "dr07": data.get('dr07', ''),
                    "dr09": data.get('dr09', '')
                }
                # 移除空值，保留有效的查询条件
                query_filters = {k: v for k, v in query_filters.items() if v is not None and v != ''}

                queries.append({
                    "data": ["id", "submission_id", "create_time"],
                    "filters": query_filters,
                    "limit": 1,
                    "order_by": ["id DESC"]  # 按ID降序，获取最新插入的记录
                })

            # 执行批量查询
            query_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries
            )

            # 提取主键ID
            for i, query_result in enumerate(query_results):
                if query_result.data and len(query_result.data) > 0:
                    pk_id = query_result.data[0].get('id', 0)
                    submission_pks.append(pk_id)
                    logger.debug(f"查询到插入记录的主键ID: {pk_id} (条件: {queries[i]['filters']})")
                else:
                    submission_pks.append(0)
                    logger.error(f"无法查询到插入记录的主键ID，查询条件: {queries[i]['filters']}")

            if not submission_pks or all(pk == 0 for pk in submission_pks):
                raise DDError("无法获取插入记录的主键ID，请检查数据插入是否成功")

            logger.info(f"成功获取到 {len([pk for pk in submission_pks if pk > 0])}/{len(submission_pks)} 个插入记录的主键ID")

            # 🔧 批量向量化处理（使用新的VectorRepository逻辑）
            all_vector_ids = []
            if self.vdb_client and self.embedding_client and DDConstants.VECTORIZED_FIELDS:
                logger.info(f"开始批量向量化处理: {len(submission_data_list)}条记录")

                # 🔧 在循环外部初始化，收集所有记录的向量化文本
                texts_to_embed = []
                text_metadata = []  # 存储文本对应的元数据
                processed_records = 0
                skipped_records = 0
                total_fields = 0

                # 构建批量向量数据
                for i, data in enumerate(submission_data_list):
                    if i >= len(submission_pks):
                        logger.warning(f"记录索引{i}超出submission_pks范围，跳过")
                        continue

                    vectorized_content = DDUtils.extract_vectorized_content(data)
                    if not vectorized_content:
                        skipped_records += 1
                        logger.debug(f"记录{i}无向量化内容，跳过")
                        continue

                    # 获取knowledge_id - 优先使用传入的参数，避免查询历史数据干扰
                    if knowledge_id:
                        actual_knowledge_id = knowledge_id  # 直接使用传入的knowledge_id
                        logger.debug(f"使用传入的knowledge_id: {knowledge_id}")
                    else:
                        actual_knowledge_id = await self._get_knowledge_id_for_submission(data, submission_pks[i])
                        logger.debug(f"通过查询获取knowledge_id: {actual_knowledge_id}")
                    if not actual_knowledge_id:
                        skipped_records += 1
                        logger.warning(f"无法获取knowledge_id，跳过向量创建: submission_pk={submission_pks[i]}")
                        continue

                    # 为当前记录的每个向量化字段收集文本
                    record_field_count = 0
                    for field_code, content in vectorized_content.items():
                        if content and content.strip():
                            texts_to_embed.append(content)
                            text_metadata.append({
                                "knowledge_id": actual_knowledge_id,
                                "data_row_id": submission_pks[i],
                                "field_id": field_code,
                                "data_layer": data.get("dr01", "")
                            })
                            record_field_count += 1
                            total_fields += 1

                    if record_field_count > 0:
                        processed_records += 1
                        logger.debug(f"记录{i}(submission_pk={submission_pks[i]})提取了{record_field_count}个向量化字段")

                logger.info(f"向量化数据收集完成: 处理{processed_records}条记录, 跳过{skipped_records}条记录, 总计{total_fields}个向量化字段")

                # 🔧 批量生成向量嵌入（如果有文本需要处理）
                embeddings_data = []
                if texts_to_embed:
                    try:
                        logger.info(f"开始生成向量嵌入: {len(texts_to_embed)}个文本")

                        # 🔧 设置更大的批次大小，充分利用GenericEmbedding的并发能力
                        # GenericEmbedding现在支持内部并发处理，可以处理更大的批次
                        embedding_batch_size = 160  # 5个并发批次 × 32个文本/批次 = 160个文本

                        # 估算文本大小，动态调整批次大小
                        total_text_size = sum(len(text.encode('utf-8')) for text in texts_to_embed)
                        avg_text_size = total_text_size / len(texts_to_embed) if texts_to_embed else 0

                        # 🔧 基于平均文本大小动态调整
                        if avg_text_size > 2000:  # 平均每个文本超过2KB
                            embedding_batch_size = 80   # 减半
                            logger.info(f"Large average text size ({avg_text_size:.0f} bytes), reducing batch size to {embedding_batch_size}")
                        elif avg_text_size > 5000:  # 平均每个文本超过5KB
                            embedding_batch_size = 40   # 再减半
                            logger.info(f"Very large average text size ({avg_text_size:.0f} bytes), reducing batch size to {embedding_batch_size}")

                        # 🔧 总大小检查（保持原有逻辑）
                        if total_text_size > 50 * 1024 * 1024:  # 50MB（提高阈值）
                            embedding_batch_size = max(20, embedding_batch_size // 2)  # 减半但不低于20
                            logger.warning(f"Large total text size ({total_text_size/1024/1024:.1f}MB), reducing batch size to {embedding_batch_size}")

                        all_embeddings = []
                        total_batches = (len(texts_to_embed) + embedding_batch_size - 1) // embedding_batch_size

                        # 分批处理文本
                        for i in range(0, len(texts_to_embed), embedding_batch_size):
                            batch_texts = texts_to_embed[i:i + embedding_batch_size]
                            batch_num = i // embedding_batch_size + 1

                            try:
                                logger.debug(f"处理向量生成批次{batch_num}/{total_batches}: {len(batch_texts)}个文本")

                                # 使用ainvoke批量生成向量，输入格式为[text1, text2, ...]
                                result = await self.embedding_client.ainvoke(texts=batch_texts)

                                if not result.embeddings or len(result.embeddings) != len(batch_texts):
                                    raise DDError(f"批次{batch_num}向量生成结果数量不匹配: 期望{len(batch_texts)}, 实际{len(result.embeddings) if result.embeddings else 0}")

                                all_embeddings.extend(result.embeddings)
                                logger.debug(f"批次{batch_num}向量生成成功: {len(batch_texts)}个文本")

                            except Exception as e:
                                logger.error(f"批次{batch_num}向量生成失败: {e}")
                                # 根据业务需求决定是否继续处理其他批次
                                # 这里选择抛出异常，停止整个处理过程
                                raise DDError(f"向量生成失败: 批次{batch_num}, 错误: {e}")

                        # 验证总结果数量
                        if len(all_embeddings) != len(texts_to_embed):
                            raise DDError(f"向量生成总数量不匹配: 期望{len(texts_to_embed)}, 实际{len(all_embeddings)}")

                        # 将向量与元数据组合
                        for embedding, metadata in zip(all_embeddings, text_metadata):
                            embeddings_data.append({
                                "embedding": embedding,
                                **metadata
                            })

                        logger.info(f"批量向量生成完成: 总计{len(all_embeddings)}个向量，分{total_batches}个批次处理")

                    except Exception as e:
                        logger.error(f"批量生成向量嵌入失败: {e}")
                        # 如果批量失败，可以考虑逐个重试或直接跳过
                        raise DDError(f"向量化处理失败: {e}")
                else:
                    logger.warning("没有收集到需要向量化的文本")

                # 🔧 使用VectorRepository的批量插入（版本控制模式）
                if embeddings_data:
                    logger.info(f"开始批量插入向量: {len(embeddings_data)}条向量记录")

                    from .vector.repository import VectorRepository
                    vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

                    all_vector_ids = await vector_repo.batch_insert_vectors(
                        embeddings_data=embeddings_data,
                        enable_versioning=True,  # 版本控制模式，保留历史
                        batch_size=100
                    )

                    logger.info(f"批量向量插入完成: 返回{len(all_vector_ids) if all_vector_ids else 0}个向量ID")
                else:
                    logger.warning("没有向量数据需要插入")

            # 记录最终结果
            logger.info(f"批量创建完成: 数据库记录{len(submission_pks)}条, 向量记录{len(all_vector_ids) if all_vector_ids else 0}条")

            # 如果是单条插入，返回单个ID；如果是批量插入，返回ID列表
            if is_single:
                return submission_pks[0] if submission_pks else 0, all_vector_ids
            else:
                return submission_pks, all_vector_ids

        except Exception as e:
            logger.error(f"创建填报数据失败: {e}")

            # 🔧 修改回滚策略：只在明确需要时才回滚数据库记录
            # 如果是向量化失败，用户可能希望保留数据库记录，单独修复向量问题
            if 'submission_pks' in locals() and submission_pks:
                logger.warning(f"数据库记录已创建{len(submission_pks)}条，但向量化失败。如需回滚数据库记录，请手动处理。")
                # 可以选择性地启用自动回滚
                # try:
                #     where_conditions = [{'id': pk} for pk in submission_pks]
                #     await self._abatch_delete_by_conditions(
                #         table=DDTableNames.KB_SUBMISSION_DATA,
                #         where_conditions=where_conditions
                #     )
                #     logger.info(f"已回滚{len(submission_pks)}条数据库记录")
                # except Exception as rollback_e:
                #     logger.error(f"回滚数据库记录失败: {rollback_e}")

            raise DDError(f"创建填报数据失败: {e}")
    
    async def get_submission_data(self, submission_pk: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取填报数据

        Args:
            submission_pk: dd_submission_data表的主键id（默认查询条件）
            **where_conditions: 其他查询条件，如 knowledge_id='xxx', submission_id='xxx'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if submission_pk:
            where = {'id': submission_pk}
        elif where_conditions:
            where = where_conditions
        else:
            raise DDValidationError("必须提供 submission_pk 或其他查询条件")

        results = await self._aselect(
            table=DDTableNames.KB_SUBMISSION_DATA,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def batch_get_submission_data(self, submission_pks: List[int]) -> List[Dict[str, Any]]:
        """
        批量获取填报数据

        Args:
            submission_pks: dd_submission_data表的主键id列表

        Returns:
            填报数据列表
        """
        if not submission_pks:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for pk in submission_pks:
                query = {
                    "data": ["*"],  # 选择所有字段
                    "filters": {"id": pk}  # 按主键过滤
                }
                queries.append(query)

            # 使用真正的abatch_query方法
            query_responses = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=100,
                max_concurrency=5
            )

            # 提取数据并按原始顺序排序
            result_map = {}
            for response in query_responses:
                if response.data:  # QueryResponse.data包含实际数据
                    for record in response.data:
                        result_map[record['id']] = record

            # 按原始顺序返回结果
            ordered_results = []
            for pk in submission_pks:
                if pk in result_map:
                    ordered_results.append(result_map[pk])

            return ordered_results

        except Exception as e:
            logger.error(f"批量获取填报数据失败: {e}")
            return []

    async def update_submission_data(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> Union[Tuple[bool, List[Any]], bool]:
        """
        统一的填报数据更新方法 - 支持单条和批量操作（包含向量更新）

        Args:
            updates: 更新数据，支持单条和批量格式
            conditions: 更新条件，支持单条和批量格式
            batch_size: 每批处理的记录数，默认100
            max_concurrency: 最大并发批次数，默认3
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            单条更新: (success, new_vector_ids)
            批量更新: bool (success)

        Examples:
            # 单条更新（按主键）
            success, vector_ids = await crud.update_submission_data(
                updates={"dr09": "新数据项名称"},
                conditions={"id": 123}
            )

            # 批量更新
            success = await crud.update_submission_data([
                {"data": {"dr09": "数据项1"}, "filters": {"id": 123}},
                {"data": {"dr09": "数据项2"}, "filters": {"id": 124}}
            ])
        """
        # 检查是否为单条更新且需要向量处理
        if (isinstance(updates, dict) and conditions is not None and
            isinstance(conditions, dict) and len(conditions) == 1):
            # 单条更新，保持原有的向量处理逻辑
            return await self._update_single_submission_with_vectors(
                submission_data=updates,
                conditions=conditions
            )
        else:
            # 批量更新，使用统一方法（暂不支持向量处理）
            success = await self._unified_update(
                table=DDTableNames.KB_SUBMISSION_DATA,
                updates=updates,
                conditions=conditions,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )
            return success

    async def _update_single_submission_with_vectors(
        self,
        submission_data: Dict[str, Any],
        conditions: Dict[str, Any]
    ) -> Tuple[bool, List[Any]]:
        """
        单条填报数据更新（包含向量更新）- 保持原有逻辑
        """
        try:
            # 1. 获取原数据（用于向量删除）
            old_data = None
            submission_pk = None

            if self.vdb_client and self.embedding_client and DDConstants.VECTORIZED_FIELDS:
                # 先查询获取完整数据和主键
                if 'id' in conditions:
                    submission_pk = conditions['id']
                    old_data = await self.get_submission_data(submission_pk)
                else:
                    # 如果没有主键，需要先查询获取主键
                    old_data = await self.get_submission_data(**conditions)
                    if old_data:
                        submission_pk = old_data['id']

                if not old_data:
                    raise DDNotFoundError(f"填报数据不存在: 条件={conditions}")

            # 2. 更新填报数据（时间戳由数据库自动处理）
            updates = [{"data": submission_data, "filters": conditions}]
            result = await self.rdb_client.abatch_update(
                table=DDTableNames.KB_SUBMISSION_DATA,
                updates=updates,
                batch_size=1,
                max_concurrency=1,
                timeout_per_batch=60.0
            )

            success = result.success and result.affected_rows > 0

            # 4. 智能向量更新（只在向量化字段变化时才执行）
            new_vector_ids = []
            if success and self.vdb_client and self.embedding_client and DDConstants.VECTORIZED_FIELDS and old_data:
                # 合并旧数据和新数据，确保比较时有完整的字段信息
                merged_data = {**old_data, **submission_data}

                # 检查向量化字段是否发生变化
                vectorized_fields_changed = self._check_vectorized_fields_changed(old_data, merged_data, submission_data)

                if vectorized_fields_changed:
                    logger.info(f"检测到向量化字段变化，更新向量: submission_pk={submission_pk}")

                    # 删除旧向量
                    await self._delete_vectors_for_submission(old_data, submission_pk)

                    # 创建新向量（使用合并后的完整数据）
                    vectorized_content = DDUtils.extract_vectorized_content(merged_data)
                    if vectorized_content:
                        new_vector_ids = await self._create_vectors_for_submission(
                            merged_data, submission_pk, vectorized_content
                        )
                else:
                    logger.debug(f"向量化字段未变化，跳过向量更新: submission_pk={submission_pk}")

            return success, new_vector_ids

        except Exception as e:
            logger.error(f"更新填报数据失败: {e}")
            raise DDError(f"更新填报数据失败: {e}")

    async def delete_submission_data(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的填报数据删除方法 - 支持单条和批量操作（包含向量删除）

        Args:
            conditions: 删除条件，支持单条和批量格式
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Examples:
            # 单条删除（按主键）
            await crud.delete_submission_data({"id": 123})

            # 按ID列表批量删除
            await crud.delete_submission_data([{"id": 123}, {"id": 124}])

        Note:
            现在单条和批量删除都支持向量删除
        """
        # 检查是否需要向量处理
        if self.vdb_client and self.embedding_client and DDConstants.VECTORIZED_FIELDS:
            # 单条或批量删除，都包含向量处理
            if isinstance(conditions, dict):
                # 单条删除
                return await self._delete_single_submission_with_vectors(conditions)
            else:
                # 批量删除，使用包含向量删除的方法
                return await self._delete_submission_with_vectors(
                    conditions=conditions,
                    batch_size=batch_size,
                    max_concurrency=max_concurrency,
                    timeout_per_batch=timeout_per_batch
                )
        else:
            # 没有向量客户端，只删除数据库记录
            return await self._unified_delete(
                table=DDTableNames.KB_SUBMISSION_DATA,
                conditions=conditions,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

    async def _delete_single_submission_with_vectors(self, conditions: Dict[str, Any]) -> bool:
        """
        单条填报数据删除（包含向量删除）

        Args:
            conditions: 删除条件，必须是字典格式

        Returns:
            bool: 删除是否成功
        """
        try:
            # 1. 先获取要删除的记录数据（用于向量删除）
            submission_data = None
            submission_pk = None

            if 'id' in conditions:
                submission_pk = conditions['id']
                submission_data = await self.get_submission_data(submission_pk)
            else:
                # 如果没有主键，需要先查询获取主键和数据
                submission_data = await self.get_submission_data(**conditions)
                if submission_data:
                    submission_pk = submission_data['id']

            if not submission_data:
                logger.warning(f"要删除的填报数据不存在: {conditions}")
                return False

            # 2. 删除向量数据
            if self.vdb_client and self.embedding_client and DDConstants.VECTORIZED_FIELDS:
                await self._delete_vectors_for_submission(submission_data, submission_pk)

            # 3. 删除数据库记录
            result = await self._unified_delete(
                table=DDTableNames.KB_SUBMISSION_DATA,
                conditions=conditions,
                batch_size=1,
                max_concurrency=1,
                timeout_per_batch=60.0
            )

            if result:
                logger.info(f"单条删除成功: submission_pk={submission_pk}")
            else:
                logger.warning(f"单条删除失败: submission_pk={submission_pk}")

            return result

        except Exception as e:
            logger.error(f"单条删除填报数据失败: {e}")
            raise DDError(f"单条删除填报数据失败: {e}")

    async def _delete_submission_with_vectors(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        填报数据删除（包含向量删除）- 支持单条和批量操作
        """
        try:
            # 🔧 第一步：先删除数据库记录，获取删除的主键列表
            result = await self._unified_delete(
                table=DDTableNames.KB_SUBMISSION_DATA,
                conditions=conditions,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            if not result:
                logger.info("数据库删除失败，跳过向量删除")
                return False

            # 🔧 第二步：根据删除条件获取被删除的主键列表
            # 注意：由于数据已被删除，我们需要从删除条件中提取主键
            deleted_ids = []
            if isinstance(conditions, dict):
                conditions_list = [conditions]
            else:
                conditions_list = conditions

            for condition in conditions_list:
                if 'id' in condition:
                    deleted_ids.append(condition['id'])
                else:
                    # 对于非主键条件，我们无法直接获取被删除的ID
                    # 这种情况下，向量删除可能不完整，记录警告
                    logger.warning(f"非主键删除条件，无法精确删除向量: {condition}")

            # 🔧 第三步：根据主键列表批量删除向量
            if deleted_ids:
                await self._batch_delete_vectors_by_ids(deleted_ids)
            else:
                logger.warning("无法获取删除的主键列表，跳过向量删除")

            return result

        except Exception as e:
            logger.error(f"删除填报数据失败: {e}")
            raise DDError(f"删除填报数据失败: {e}")

    async def list_submission_data(
        self,
        knowledge_id: Optional[str] = None,
        version: Optional[str] = None,
        data_layer: Optional[str] = None,
        submission_id: Optional[str] = None,
        report_data_id: Optional[int] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """查询填报数据列表"""
        where = {}
        if knowledge_id:
            where['knowledge_id'] = knowledge_id
        if version:
            where['version'] = version
        if data_layer:
            where['data_layer'] = data_layer
        if submission_id:
            where['submission_id'] = submission_id
        if report_data_id:
            where['report_data_id'] = report_data_id

        return await self._aselect(
            table=DDTableNames.KB_SUBMISSION_DATA,
            where=where if where else None,
            limit=limit,
            offset=offset
        )

    async def batch_query_submission_data(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询填报数据 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"knowledge_id": "KB001", "submission_id": "SUB001"},
                    {"data_layer": "layer1", "version": "v1.0"}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            conditions = [
                {"knowledge_id": "KB001", "submission_id": "SUB001"},
                {"knowledge_id": "KB002", "submission_id": "SUB002"}
            ]
            submission_records = await crud.batch_query_submission_data(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询填报数据: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询填报数据完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询填报数据失败: {e}")
            raise DDError(f"批量查询失败: {e}")
    
    # ==================== 私有方法：向量操作 ====================

    async def _create_vectors_for_submission(
        self,
        submission_data: Dict[str, Any],
        submission_pk: int,  # dd_submission_data表的主键id
        vectorized_content: Dict[str, str],
        knowledge_id: Optional[str] = None
    ) -> List[Any]:
        """为填报数据创建向量（使用VectorRepository）"""
        if not self.vdb_client or not self.embedding_client:
            logger.warning("向量客户端未配置，跳过向量创建")
            return []

        # 使用传入的knowledge_id，如果没有则尝试获取
        if not knowledge_id:
            knowledge_id = await self._get_knowledge_id_for_submission(submission_data, submission_pk)
            if not knowledge_id:
                logger.warning(f"无法获取knowledge_id，跳过向量创建: submission_pk={submission_pk}")
                return []

        # 使用VectorRepository
        from .vector.repository import VectorRepository
        vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

        vector_ids = []
        data_layer = submission_data.get("dr01", "")
        
        # 先删除可能存在的旧向量记录，避免唯一约束冲突
        try:
            await self._delete_existing_vectors(knowledge_id, data_layer, submission_pk)
        except Exception as e:
            logger.warning(f"删除旧向量记录失败: {e}")

        for field_code, content in vectorized_content.items():
            if not content or not content.strip():
                continue

            try:
                # 使用VectorRepository创建向量嵌入
                result = await vector_repo.create_embedding_for_field(
                    content=content,
                    field_code=field_code,
                    knowledge_id=knowledge_id,  # 使用正确的knowledge_id
                    data_layer=data_layer,
                    submission_pk=submission_pk
                )

                if result and result.get("vector_id"):
                    vector_ids.append(result["vector_id"])

            except Exception as e:
                logger.warning(f"创建向量失败: field={field_code}, error={e}")
                # 继续处理其他字段

        return vector_ids

    async def _batch_delete_existing_vectors(self, knowledge_id: str, submission_data_list: List[Dict[str, Any]], submission_pks: List[int]) -> None:
        """批量删除可能存在的旧向量记录，避免唯一约束冲突"""
        try:
            from .vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)
            
            # 构建批量删除条件
            for i, data in enumerate(submission_data_list):
                if i < len(submission_pks):
                    data_layer = data.get("dr01", "")
                    delete_filters = {
                        "is_latest": True,
                        "knowledge_id": knowledge_id,
                        "data_layer": data_layer,
                        "data_row_id": submission_pks[i]
                    }
                    
                    try:
                        await vector_repo._delete_vectors(delete_filters)
                        logger.debug(f"删除旧向量记录: knowledge_id={knowledge_id}, data_layer={data_layer}, data_row_id={submission_pks[i]}")
                    except Exception as e:
                        logger.warning(f"删除单个向量记录失败: {e}")
                        continue
            
        except Exception as e:
            logger.error(f"批量删除旧向量记录失败: {e}")
            # 不抛出异常，允许继续创建新向量

    async def _create_vectors_for_submission_without_delete(
        self,
        submission_data: Dict[str, Any],
        submission_pk: int,  # dd_submission_data表的主键id
        vectorized_content: Dict[str, str],
        knowledge_id: Optional[str] = None
    ) -> List[Any]:
        """为填报数据创建向量（不删除旧记录，适用于批量处理）"""
        if not self.vdb_client or not self.embedding_client:
            logger.warning("向量客户端未配置，跳过向量创建")
            return []

        # 使用传入的knowledge_id
        if not knowledge_id:
            knowledge_id = await self._get_knowledge_id_for_submission(submission_data, submission_pk)
            if not knowledge_id:
                logger.warning(f"无法获取knowledge_id，跳过向量创建: submission_pk={submission_pk}")
                return []

        # 使用VectorRepository
        from .vector.repository import VectorRepository
        vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

        vector_ids = []
        data_layer = submission_data.get("dr01", "")

        for field_code, content in vectorized_content.items():
            if not content or not content.strip():
                continue

            try:
                # 使用VectorRepository创建向量嵌入
                result = await vector_repo.create_embedding_for_field(
                    content=content,
                    field_code=field_code,
                    knowledge_id=knowledge_id,  # 使用正确的knowledge_id
                    data_layer=data_layer,
                    submission_pk=submission_pk
                )

                if result and result.get("vector_id"):
                    vector_ids.append(result["vector_id"])

            except Exception as e:
                logger.warning(f"创建向量失败: field={field_code}, error={e}")
                # 继续处理其他字段

        return vector_ids

    async def _delete_existing_vectors(self, knowledge_id: str, data_layer: str, submission_pk: int) -> None:
        """删除可能存在的旧向量记录，避免唯一约束冲突"""
        try:
            from .vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)
            
            # 构建删除条件，匹配唯一约束的所有字段
            delete_filters = {
                "is_latest": True,
                "knowledge_id": knowledge_id,
                "data_layer": data_layer,
                "data_row_id": submission_pk
            }
            
            # 删除匹配的向量记录
            await vector_repo._delete_vectors(delete_filters)
            logger.debug(f"删除旧向量记录: knowledge_id={knowledge_id}, data_layer={data_layer}, data_row_id={submission_pk}")
            
        except Exception as e:
            logger.error(f"删除旧向量记录失败: {e}")
            raise

    def _check_vectorized_fields_changed(
        self,
        old_data: Dict[str, Any],
        merged_data: Dict[str, Any],
        update_data: Dict[str, Any]
    ) -> bool:
        """
        检查向量化字段是否发生变化（智能检测版）

        Args:
            old_data: 原始完整数据
            merged_data: 合并后的完整数据
            update_data: 本次更新的字段数据

        Returns:
            bool: 如果向量化字段发生变化返回True，否则返回False
        """
        if not old_data or not DDConstants.VECTORIZED_FIELDS:
            return True  # 如果没有原始数据或没有向量化字段配置，默认需要更新

        # 只检查本次更新中包含的向量化字段
        vectorized_fields_in_update = [
            field for field in DDConstants.VECTORIZED_FIELDS
            if field in update_data
        ]

        if not vectorized_fields_in_update:
            # 本次更新不涉及任何向量化字段
            logger.debug(f"本次更新不涉及向量化字段，跳过向量更新")
            return False

        # 检查涉及的向量化字段是否真的发生了变化
        for field_code in vectorized_fields_in_update:
            old_value = old_data.get(field_code, "")
            new_value = merged_data.get(field_code, "")

            # 标准化比较（去除空白字符）
            old_value_clean = str(old_value).strip() if old_value is not None else ""
            new_value_clean = str(new_value).strip() if new_value is not None else ""

            if old_value_clean != new_value_clean:
                logger.info(f"向量化字段 {field_code} 发生变化: '{old_value_clean}' -> '{new_value_clean}'")
                return True

        logger.debug(f"向量化字段值未发生实际变化: {vectorized_fields_in_update}")
        return False

    async def _get_knowledge_id_for_submission(
        self,
        submission_data: Dict[str, Any],
        submission_pk: int
    ) -> Optional[str]:
        """获取填报数据对应的knowledge_id（优化版本，简化查询策略）"""
        try:
            # 方法1: 直接路径 - 如果submission_data中有report_data_id
            report_data_id = submission_data.get("report_data_id")
            if report_data_id:
                knowledge_id = await self._get_knowledge_id_by_report_id(report_data_id)
                if knowledge_id:
                    logger.debug(f"通过submission_data.report_data_id获取knowledge_id: {knowledge_id}")
                    return knowledge_id

            # 方法2: 智能查询策略 - 优先查询最可能的数据源
            return await self._get_knowledge_id_smart_query(submission_data, submission_pk)

        except Exception as e:
            logger.error(f"获取knowledge_id失败: {e}")
            return self._get_fallback_knowledge_id(submission_data)

    async def _get_knowledge_id_by_report_id(self, report_data_id: int) -> Optional[str]:
        """通过report_data_id获取knowledge_id（带缓存）"""
        # 检查缓存
        cache_key = f"report_id:{report_data_id}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result is not None:
            logger.debug(f"从缓存获取knowledge_id: {cached_result}")
            return cached_result

        try:
            report_results = await self._aselect(
                table=DDTableNames.KB_REPORT_DATA,
                where={'id': report_data_id},
                limit=1
            )
            if report_results:
                knowledge_id = report_results[0].get('knowledge_id')
                if knowledge_id:
                    # 缓存结果
                    self._set_cache(cache_key, knowledge_id)
                    return knowledge_id
            return None
        except Exception as e:
            logger.warning(f"通过report_data_id查询knowledge_id失败: {e}")
            return None

    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """从缓存获取值"""
        try:
            if cache_key in self._knowledge_id_cache:
                cached_item = self._knowledge_id_cache[cache_key]
                # 检查TTL
                if datetime.now().timestamp() - cached_item['timestamp'] < self._cache_ttl:
                    return cached_item['value']
                else:
                    # 过期，删除
                    del self._knowledge_id_cache[cache_key]
            return None
        except Exception:
            return None

    def _set_cache(self, cache_key: str, value: str):
        """设置缓存值"""
        try:
            # 如果缓存已满，清理最旧的条目
            if len(self._knowledge_id_cache) >= self._cache_max_size:
                # 简单的LRU：删除最旧的条目
                oldest_key = min(
                    self._knowledge_id_cache.keys(),
                    key=lambda k: self._knowledge_id_cache[k]['timestamp']
                )
                del self._knowledge_id_cache[oldest_key]

            # 添加新条目
            self._knowledge_id_cache[cache_key] = {
                'value': value,
                'timestamp': datetime.now().timestamp()
            }
        except Exception as e:
            logger.debug(f"设置缓存失败: {e}")  # 缓存失败不影响主要功能

    async def _get_knowledge_id_smart_query(
        self,
        submission_data: Dict[str, Any],
        submission_pk: int
    ) -> Optional[str]:
        """智能查询策略获取knowledge_id - 只查询当前记录，如果没有则抛出错误"""
        try:
            # 只查询当前记录的report_data_id
            query_result = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=[{
                    "data": ["report_data_id"],
                    "filters": {"id": submission_pk},
                    "limit": 1
                }],
                batch_size=1,
                max_concurrency=1
            )

            # 获取当前记录的report_data_id
            if query_result and query_result[0].data:
                record = query_result[0].data[0]
                report_data_id = record.get('report_data_id')

                if report_data_id:
                    # 通过report_data_id查询knowledge_id
                    knowledge_id = await self._get_knowledge_id_by_report_id(report_data_id)
                    if knowledge_id:
                        logger.debug(f"通过当前记录的report_data_id获取knowledge_id: {knowledge_id}")
                        return knowledge_id
                    else:
                        logger.error(f"当前记录的report_data_id({report_data_id})无法找到对应的knowledge_id")
                        return None
                else:
                    logger.error(f"当前记录(submission_pk={submission_pk})没有report_data_id")
                    return None
            else:
                logger.error(f"无法查询到当前记录(submission_pk={submission_pk})")
                return None

        except Exception as e:
            logger.error(f"查询当前记录的knowledge_id失败: {e}")
            return None

    async def _get_knowledge_id_simple_fallback(
        self,
        submission_data: Dict[str, Any],
        submission_pk: int
    ) -> Optional[str]:
        """简化的降级方案"""
        try:
            # 只尝试最基本的查询路径
            submission_result = await self._aselect(
                table=DDTableNames.KB_SUBMISSION_DATA,
                where={'id': submission_pk},
                limit=1
            )

            if submission_result and submission_result[0].get('report_data_id'):
                report_data_id = submission_result[0]['report_data_id']
                knowledge_id = await self._get_knowledge_id_by_report_id(report_data_id)
                if knowledge_id:
                    logger.debug(f"通过简化降级方案获取knowledge_id: {knowledge_id}")
                    return knowledge_id

            return self._get_fallback_knowledge_id(submission_data)

        except Exception as e:
            logger.error(f"简化降级方案失败: {e}")
            return self._get_fallback_knowledge_id(submission_data)

    def _get_fallback_knowledge_id(self, submission_data: Dict[str, Any]) -> str:
        """获取降级knowledge_id"""
        fallback_id = submission_data.get("submission_id", "default_kb")
        logger.warning(f"使用降级knowledge_id: {fallback_id}")
        return fallback_id



    async def _delete_vectors_for_submission(self, submission_data: Dict[str, Any], submission_pk: int) -> None:
        """删除填报数据的向量（使用VectorRepository）"""
        if not self.vdb_client:
            logger.warning("向量客户端未配置，跳过向量删除")
            return

        try:
            # 获取正确的knowledge_id
            knowledge_id = await self._get_knowledge_id_for_submission(submission_data, submission_pk)
            if not knowledge_id:
                logger.warning(f"无法获取knowledge_id，跳过向量删除: submission_pk={submission_pk}")
                return

            # 使用VectorRepository删除向量
            from .vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

            await vector_repo.delete_embeddings_by_submission(
                knowledge_id=knowledge_id,  # 使用正确的knowledge_id
                data_layer=submission_data.get("dr01", ""),
                submission_pk=submission_pk
            )
        except Exception as e:
            logger.warning(f"删除向量失败: submission_pk={submission_pk}, error={e}")

    async def _batch_delete_vectors_by_ids(self, deleted_ids: List[int]) -> None:
        """
        根据主键列表批量删除向量（严格删除，不保留版本）

        Args:
            deleted_ids: 被删除的dd_submission_data主键列表
        """
        if not self.vdb_client or not deleted_ids:
            logger.warning("向量客户端未配置或无删除ID，跳过向量删除")
            return

        try:
            from .vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

            # 🔧 直接根据data_row_id批量删除向量
            # data_row_id在dd_embeddings表中对应dd_submission_data的主键，全局唯一
            delete_exprs = []
            for data_row_id in deleted_ids:
                # 删除所有相关向量，不限制knowledge_id、data_layer等条件
                # 因为data_row_id是全局唯一的
                delete_expr = f"data_row_id = {data_row_id}"
                delete_exprs.append(delete_expr)

            if delete_exprs:
                # 🔧 使用PGVector客户端的批量删除方法
                delete_result = await vector_repo.vdb_client.abatch_delete(
                    collection_name=vector_repo.collection_name,
                    exprs=delete_exprs
                )

                deleted_count = delete_result.get('delete_count', 0) if isinstance(delete_result, dict) else 0
                logger.info(f"根据主键批量删除向量完成: 删除{deleted_count}条向量记录，主键数={len(deleted_ids)}")

        except Exception as e:
            logger.error(f"根据主键批量删除向量失败: {e}")
            # 不抛出异常，允许继续处理
    
    # ==================== 批量操作 ====================

    async def batch_create_departments(self, departments_data: List[Dict[str, Any]]) -> List[str]:
        """批量创建部门"""
        # 添加时间戳
        current_time = datetime.now()
        for dept_data in departments_data:
            dept_data.update({
                'create_time': current_time,
                'update_time': current_time
            })

        # 使用真正的批量插入
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_DEPARTMENTS,
            data=departments_data
        )

        if not result.success:
            raise DDError(f"批量创建部门失败: {result.error_message_message}")

        return [dept['dept_id'] for dept in departments_data]

    async def batch_create_submissions(self, submissions_data: List[Dict[str, Any]]) -> List[int]:
        """批量创建填报数据（不包含向量，纯数据库操作）- MySQL优化版本"""
        if not submissions_data:
            return []

        # 添加时间戳
        current_time = datetime.now()
        for submission_data in submissions_data:
            submission_data.update({
                'create_time': current_time,
                'update_time': current_time
            })

        # 检测数据库类型并选择最优策略
        try:
            db_type = self.rdb_client.get_database_type()
            logger.debug(f"检测到数据库类型: {db_type}")

            if str(db_type).lower() == 'mysql':
                return await self._batch_create_submissions_mysql_optimized(submissions_data)
            else:
                return await self._batch_create_submissions_universal(submissions_data)

        except Exception as e:
            logger.warning(f"数据库类型检测失败，使用通用方案: {e}")
            return await self._batch_create_submissions_universal(submissions_data)

    async def _batch_create_submissions_mysql_optimized(self, submissions_data: List[Dict[str, Any]]) -> List[int]:
        """MySQL优化的批量创建方案 - 使用LAST_INSERT_ID()"""
        try:
            # 分批处理，避免单次插入过多数据
            batch_size = 500  # MySQL优化的批次大小
            all_ids = []

            for i in range(0, len(submissions_data), batch_size):
                batch_data = submissions_data[i:i + batch_size]
                batch_ids = await self._mysql_batch_insert_with_ids(batch_data)
                all_ids.extend(batch_ids)

            logger.info(f"MySQL优化批量创建完成: 插入{len(submissions_data)}条, 获取{len(all_ids)}个ID")
            return all_ids

        except Exception as e:
            logger.warning(f"MySQL优化方案失败，降级到通用方案: {e}")
            return await self._batch_create_submissions_universal(submissions_data)

    async def _mysql_batch_insert_with_ids(self, batch_data: List[Dict[str, Any]]) -> List[int]:
        """MySQL特定的批量插入并获取ID"""
        try:
            # 执行批量插入
            result = await self.rdb_client.abatch_insert(
                table=DDTableNames.KB_SUBMISSION_DATA,
                data=batch_data
            )

            if not result.success:
                raise DDError(f"MySQL批量插入失败: {result.error_message}")

            # MySQL特定：使用LAST_INSERT_ID()获取起始ID
            try:
                # 方案1: 尝试获取LAST_INSERT_ID()
                last_id_result = await self._execute_mysql_last_insert_id()

                if last_id_result:
                    last_id = last_id_result
                    # 计算所有插入的ID（MySQL自增ID连续）
                    batch_ids = list(range(last_id - len(batch_data) + 1, last_id + 1))
                    logger.debug(f"MySQL LAST_INSERT_ID()成功: 起始ID={last_id - len(batch_data) + 1}, 数量={len(batch_data)}")
                    return batch_ids
                else:
                    raise Exception("LAST_INSERT_ID()返回空结果")

            except Exception as e:
                logger.warning(f"MySQL LAST_INSERT_ID()方案失败: {e}")
                # 降级到查询方案
                return await self._query_inserted_ids_fallback(batch_data)

        except Exception as e:
            logger.error(f"MySQL批量插入失败: {e}")
            raise DDError(f"MySQL批量插入失败: {e}")

    async def _execute_mysql_last_insert_id(self) -> Optional[int]:
        """执行MySQL LAST_INSERT_ID()查询"""
        try:
            # 尝试多种方式执行LAST_INSERT_ID()
            sql_variants = [
                "SELECT LAST_INSERT_ID() as last_id",
                "SELECT LAST_INSERT_ID()",
            ]

            for sql in sql_variants:
                try:
                    if hasattr(self.rdb_client, 'aexecute'):
                        result = await self.rdb_client.aexecute(sql)
                        if hasattr(result, 'data') and result.data:
                            last_id = result.data[0].get('last_id') or result.data[0].get('LAST_INSERT_ID()')
                            if last_id and last_id > 0:
                                return int(last_id)

                    # 尝试使用原生查询
                    elif hasattr(self.rdb_client, 'execute_raw_sql'):
                        result = await self.rdb_client.execute_raw_sql(sql)
                        if result and len(result) > 0:
                            last_id = result[0].get('last_id') or result[0].get('LAST_INSERT_ID()')
                            if last_id and last_id > 0:
                                return int(last_id)

                except Exception as e:
                    logger.debug(f"SQL变体 '{sql}' 失败: {e}")
                    continue

            return None

        except Exception as e:
            logger.warning(f"执行LAST_INSERT_ID()失败: {e}")
            return None

    async def _batch_create_submissions_universal(self, submissions_data: List[Dict[str, Any]]) -> List[int]:
        """通用的批量创建方案（原有逻辑优化版）"""
        try:
            # 使用批量插入
            result = await self.rdb_client.abatch_insert(
                table=DDTableNames.KB_SUBMISSION_DATA,
                data=submissions_data
            )

            if not result.success:
                raise DDError(f"批量创建填报数据失败: {result.error_message}")

            # 使用优化的查询方案获取ID
            return await self._query_inserted_ids_fallback(submissions_data)

        except Exception as e:
            logger.error(f"通用批量创建失败: {e}")
            raise DDError(f"批量创建填报数据失败: {e}")

    async def _query_inserted_ids_fallback(self, submissions_data: List[Dict[str, Any]]) -> List[int]:
        """查询插入ID的降级方案（优化版）"""
        try:
            # 提取唯一标识符
            submission_ids = [data['submission_id'] for data in submissions_data]

            # 使用abatch_query批量查询
            queries = [
                {"data": ["id", "submission_id"], "filters": {"submission_id": submission_id}, "limit": 1}
                for submission_id in submission_ids
            ]

            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=100,
                max_concurrency=5
            )

            # 提取ID，保持与submission_ids的顺序一致
            inserted_ids = []
            for i, query_response in enumerate(batch_results):
                if query_response.data and len(query_response.data) > 0:
                    inserted_ids.append(query_response.data[0].get('id', 0))
                else:
                    logger.warning(f"未找到刚插入的记录: submission_id={submission_ids[i]}")
                    inserted_ids.append(0)

            logger.debug(f"批量查询插入记录完成: 查询{len(submission_ids)}个, 成功{sum(1 for id in inserted_ids if id > 0)}个")
            return inserted_ids

        except Exception as e:
            logger.warning(f"批量查询失败，降级到逐个查询: {e}")
            # 最后的降级方案：逐个查询
            return await self._query_inserted_ids_individual_fallback(submissions_data)

    async def _query_inserted_ids_individual_fallback(self, submissions_data: List[Dict[str, Any]]) -> List[int]:
        """逐个查询ID的最终降级方案"""
        inserted_records = []
        submission_ids = [data['submission_id'] for data in submissions_data]

        for submission_id in submission_ids:
            try:
                records = await self._aselect(
                    table=DDTableNames.KB_SUBMISSION_DATA,
                    where={'submission_id': submission_id},
                    limit=1
                )
                if records:
                    inserted_records.extend(records)
                else:
                    inserted_records.append({'id': 0})
            except Exception as e:
                logger.warning(f"查询单个记录失败: submission_id={submission_id}, error={e}")
                inserted_records.append({'id': 0})

        return [record.get('id', 0) for record in inserted_records]

    async def _get_single_insert_id_optimized(
        self,
        table: str,
        unique_conditions: Dict[str, Any],
        fallback_order_by: Optional[List[str]] = None
    ) -> int:
        """
        获取单条插入记录ID的优化方法

        Args:
            table: 表名
            unique_conditions: 唯一标识条件
            fallback_order_by: 降级查询的排序条件

        Returns:
            插入记录的ID，失败返回0
        """
        try:
            # 检测数据库类型
            db_type = self.rdb_client.get_database_type()

            if str(db_type).lower() == 'mysql':
                # MySQL优化：尝试使用LAST_INSERT_ID()
                last_id = await self._execute_mysql_last_insert_id()
                if last_id and last_id > 0:
                    logger.debug(f"MySQL LAST_INSERT_ID()获取单条插入ID成功: {last_id}")
                    return last_id

            # 降级到查询方案
            return await self._get_single_insert_id_by_query(table, unique_conditions, fallback_order_by)

        except Exception as e:
            logger.warning(f"优化获取插入ID失败，使用查询方案: {e}")
            return await self._get_single_insert_id_by_query(table, unique_conditions, fallback_order_by)

    async def _get_single_insert_id_by_query(
        self,
        table: str,
        unique_conditions: Dict[str, Any],
        fallback_order_by: Optional[List[str]] = None
    ) -> int:
        """通过查询获取单条插入记录ID"""
        try:
            inserted_records = await self._aselect(
                table=table,
                where=unique_conditions,
                order_by=fallback_order_by or ['id DESC'],
                limit=1
            )

            if inserted_records:
                return inserted_records[0].get('id', 0)
            else:
                logger.warning(f"未找到插入记录: table={table}, conditions={unique_conditions}")
                return 0

        except Exception as e:
            logger.error(f"查询插入记录ID失败: table={table}, error={e}")
            return 0



    # ==================== 部门关联关系管理 ====================

    async def create_department_relation(self, relation_data: Dict[str, Any]) -> int:
        """创建部门关联关系"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            data=[relation_data]
        )

        if not result.success:
            raise DDError(f"创建部门关联关系失败: {result.error_message}")

        # 获取插入的ID
        if hasattr(result, 'inserted_ids') and result.inserted_ids:
            return result.inserted_ids[0]
        else:
            # 通过查询获取最新插入的记录
            latest = await self._aselect(
                table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
                where={'dept_id': relation_data['dept_id'], 'table_id': relation_data['table_id']},
                order_by=['id DESC'],
                limit=1
            )
            return latest[0]['id'] if latest else 0

    async def batch_create_department_relations(
        self,
        relation_data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[int]:
        """
        批量创建部门关联关系 - 高性能版本

        Args:
            relation_data_list: 部门关联关系数据列表
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[int]: 创建的记录ID列表

        Examples:
            relation_list = [
                {"dept_id": "DEPT001", "table_id": "TABLE001", "relation_type": "parent"},
                {"dept_id": "DEPT002", "table_id": "TABLE002", "relation_type": "child"}
            ]
            relation_ids = await crud.batch_create_department_relations(relation_list)
        """
        if not relation_data_list:
            return []

        # 使用批量插入（时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            data=relation_data_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        if not result.success:
            raise DDError(f"批量创建部门关联关系失败: {result.error_message}")

        # 尝试从结果中获取插入的ID
        if hasattr(result, 'inserted_ids') and result.inserted_ids:
            return result.inserted_ids
        else:
            # 批量查询插入的记录来获取ID
            queries = []
            for relation_data in relation_data_list:
                if 'dept_id' in relation_data and 'table_id' in relation_data:
                    queries.append({
                        "data": ["id"],
                        "filters": {
                            "dept_id": relation_data['dept_id'],
                            "table_id": relation_data['table_id']
                        }
                    })

            if not queries:
                return [0] * len(relation_data_list)

            query_results = await self.rdb_client.abatch_query(
                table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency
            )

            # 提取ID
            ids = []
            for query_result in query_results:
                if query_result.data and len(query_result.data) > 0:
                    ids.append(query_result.data[0].get('id', 0))
                else:
                    ids.append(0)

            return ids

    async def get_department_relation(self, relation_id: int) -> Optional[Dict[str, Any]]:
        """获取部门关联关系"""
        results = await self._aselect(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            where={'id': relation_id},
            limit=1
        )
        return results[0] if results else None

    async def update_department_relations(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的部门关联关系更新方法 - 支持单条和批量操作

        Examples:
            # 单条更新（按主键）
            await crud.update_department_relations(
                updates={"relation_type": "new_type"},
                conditions={"id": 123}
            )
        """
        return await self._unified_update(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def delete_department_relations(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的部门关联关系删除方法 - 支持单条和批量操作

        Examples:
            # 单条删除（按主键）
            await crud.delete_department_relations({"id": 123})

            # 按ID列表批量删除
            await crud.delete_department_relations([{"id": 123}, {"id": 124}])
        """
        return await self._unified_delete(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_department_relations_by_dept(self, dept_id: str) -> List[Dict[str, Any]]:
        """根据部门ID查询关联关系列表"""
        return await self._aselect(
            table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
            where={'dept_id': dept_id}
        )

    async def batch_query_department_relations(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """
        批量查询部门关联关系 - 高性能版本

        Args:
            conditions_list: 查询条件列表，每个条件是一个字典
                例如: [
                    {"dept_id": "DEPT001", "table_id": "TABLE001"},
                    {"relation_type": "parent", "is_active": True}
                ]
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            所有查询结果的合并列表

        Examples:
            conditions = [
                {"dept_id": "DEPT001"},
                {"dept_id": "DEPT002", "relation_type": "parent"}
            ]
            relations = await crud.batch_query_department_relations(conditions)
        """
        if not conditions_list:
            return []

        try:
            # 构建批量查询请求
            queries = []
            for conditions in conditions_list:
                query = {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询部门关联关系: 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.BIZ_DEPARTMENTS_RELATION,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    elif hasattr(query_response, 'success') and not query_response.success:
                        logger.warning(f"查询 {i+1} 失败: {getattr(query_response, 'error_message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 时出错: {e}")

            logger.info(f"批量查询部门关联关系完成: 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"批量查询部门关联关系失败: {e}")
            raise DDError(f"批量查询失败: {e}")

    # ==================== 字段元数据管理 ====================

    async def create_fields_metadata(self, metadata_data: Dict[str, Any]) -> int:
        """创建字段元数据"""
        # 使用批量插入（单条，时间戳由数据库自动处理）
        result = await self.rdb_client.abatch_insert(
            table=DDTableNames.KB_FIELDS_METADATA,
            data=[metadata_data]
        )

        if not result.success:
            raise DDError(f"创建字段元数据失败: {result.error_message}")

        # 查询刚插入的记录来获取ID（使用field_code作为唯一标识符）
        inserted_records = await self._aselect(
            table=DDTableNames.KB_FIELDS_METADATA,
            where={'field_code': metadata_data['field_code']},
            limit=1
        )

        if not inserted_records:
            raise DDError("无法获取插入记录的ID")

        return inserted_records[0].get('field_id', 0)

    async def get_fields_metadata(self, field_id: int) -> Optional[Dict[str, Any]]:
        """获取字段元数据"""
        results = await self._aselect(
            table=DDTableNames.KB_FIELDS_METADATA,
            where={'field_id': field_id},
            limit=1
        )
        return results[0] if results else None

    async def get_fields_metadata_by_code(self, field_code: str) -> Optional[Dict[str, Any]]:
        """根据字段编码获取字段元数据"""
        results = await self._aselect(
            table=DDTableNames.KB_FIELDS_METADATA,
            where={'field_code': field_code},
            limit=1
        )
        return results[0] if results else None

    async def update_fields_metadata(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的字段元数据更新方法 - 支持单条和批量操作

        Examples:
            # 单条更新（按主键）
            await crud.update_fields_metadata(
                updates={"field_name": "新字段名"},
                conditions={"field_id": 123}
            )
        """
        return await self._unified_update(
            table=DDTableNames.KB_FIELDS_METADATA,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def delete_fields_metadata(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的字段元数据删除方法 - 支持单条和批量操作

        Examples:
            # 单条删除（按主键）
            await crud.delete_fields_metadata({"field_id": 123})

            # 按ID列表批量删除
            await crud.delete_fields_metadata([{"field_id": 123}, {"field_id": 124}])
        """
        return await self._unified_delete(
            table=DDTableNames.KB_FIELDS_METADATA,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_fields_metadata(
        self,
        field_category: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_vectorized: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """查询字段元数据列表"""
        where = {}
        if field_category:
            where['field_category'] = field_category
        if is_active is not None:
            where['is_active'] = is_active
        if is_vectorized is not None:
            where['is_vectorized'] = is_vectorized

        return await self._aselect(
            table=DDTableNames.KB_FIELDS_METADATA,
            where=where if where else None,
            order_by=['field_order', 'field_id'],  # 按字段顺序和ID排序
            limit=limit,
            offset=offset
        )

    # ==================== 统一的CRUD辅助方法 ====================

    async def _unified_update(
        self,
        table: str,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的更新方法 - 支持单条和批量操作

        Args:
            table: 表名
            updates: 更新数据
            conditions: 更新条件
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数
            timeout_per_batch: 每批次超时时间

        Returns:
            bool: 操作是否成功
        """
        # 标准化输入参数
        if isinstance(updates, dict) and conditions is not None:
            # 单条更新模式
            current_time = datetime.now()
            updates['update_time'] = current_time

            # 处理条件
            if isinstance(conditions, dict):
                # 单个条件
                update_list = [{"data": updates, "filters": conditions}]
            elif isinstance(conditions, list):
                # 多个条件，相同的更新数据
                update_list = [{"data": updates, "filters": cond} for cond in conditions]
            else:
                raise DDValidationError("conditions 必须是字典或字典列表")

            # 单条或少量更新使用较小的并发参数
            if len(update_list) == 1:
                batch_size = 1
                max_concurrency = 1
                timeout_per_batch = 60.0

        elif isinstance(updates, list):
            # 批量更新模式
            current_time = datetime.now()
            update_list = []

            for update in updates:
                if isinstance(update, dict) and ('data' in update or 'filters' in update or 'where' in update):
                    # 已经是标准格式
                    if 'data' in update:
                        update['data']['update_time'] = current_time
                    update_list.append(update)
                else:
                    raise DDValidationError("批量更新格式错误，应为 [{'data': {...}, 'filters': {...}}, ...]")
        else:
            raise DDValidationError("updates 参数格式错误")

        # 执行批量更新
        result = await self.rdb_client.abatch_update(
            table=table,
            updates=update_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

        return result.success and result.affected_rows > 0

    async def _unified_delete(
        self,
        table: str,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """
        统一的删除方法 - 支持单条和批量操作

        Args:
            table: 表名
            conditions: 删除条件
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数
            timeout_per_batch: 每批次超时时间

        Returns:
            bool: 操作是否成功
        """
        # 标准化输入参数
        if isinstance(conditions, dict):
            # 单条删除
            condition_list = [conditions]
            # 单条删除使用较小的并发参数
            batch_size = 1
            timeout_per_batch = 60.0
        elif isinstance(conditions, list):
            # 批量删除
            condition_list = conditions
        else:
            raise DDValidationError("conditions 必须是字典或字典列表")

        if not condition_list:
            raise DDValidationError("删除条件不能为空")

        # 执行批量删除
        result = await self._abatch_delete_by_conditions(
            table=table,
            where_conditions=condition_list,
            batch_size=batch_size,
            timeout_per_batch=timeout_per_batch
        )
        return result.success and result.affected_rows > 0

    # ==================== 私有辅助方法 ====================

    async def _aselect(self, table: str, where: Optional[Dict[str, Any]] = None,
                      order_by: Optional[List[str]] = None, limit: Optional[int] = None,
                      offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        兼容性方法：将aselect调用转换为aquery调用

        Args:
            table: 表名
            where: WHERE条件
            order_by: 排序字段
            limit: 限制数量
            offset: 偏移量

        Returns:
            查询结果列表
        """
        query_request = {
            "table": table
        }

        # 只有在有条件时才添加filters
        if where:
            query_request["filters"] = where

        # 只有在有值时才添加limit和offset
        if limit is not None:
            query_request["limit"] = limit
        if offset is not None:
            query_request["offset"] = offset

        if order_by:
            # 转换order_by格式
            sorts = []
            for order_field in order_by:
                if ' DESC' in order_field.upper():
                    field = order_field.replace(' DESC', '').replace(' desc', '').strip()
                    sorts.append({"field": field, "order": "desc"})
                elif ' ASC' in order_field.upper():
                    field = order_field.replace(' ASC', '').replace(' asc', '').strip()
                    sorts.append({"field": field, "order": "asc"})
                else:
                    sorts.append({"field": order_field.strip(), "order": "asc"})
            query_request["sorts"] = sorts

        result = await self.rdb_client.aquery(query_request)
        return result.data if result else []

    async def _abatch_delete_by_conditions(
        self,
        table: str,
        where_conditions: List[Dict[str, Any]],
        batch_size: int = 1000,
        timeout_per_batch: float = 300.0
    ) -> Any:
        """
        通过条件批量删除 - 使用基类的abatch_delete方法

        Args:
            table: 表名
            where_conditions: 删除条件列表，支持以下格式：
                - 简单条件: [{"id": 1}, {"status": "inactive"}]
                - 复杂条件: [{"age": {"$lt": 18}}, {"name": {"$like": "%test%"}}]
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            操作结果 (OperationResponse)
        """
        try:
            logger.debug(f"开始批量删除: table={table}, 条件数量={len(where_conditions)}")

            # 使用基类的异步批量删除方法
            result = await self.rdb_client.abatch_delete(
                table=table,
                conditions=where_conditions,
                batch_size=batch_size,
                timeout_per_batch=timeout_per_batch
            )

            logger.info(f"批量删除完成: table={table}, 成功={result.success}, 影响行数={result.affected_rows}")
            return result

        except Exception as e:
            logger.error(f"批量删除失败: table={table}, error={e}")
            # 返回失败结果，保持接口兼容性
            from base.db.base.rdb.core.models import OperationResponse
            return OperationResponse(
                success=False,
                affected_rows=0,
                error_message=str(e)
            )

    def _prepare_vectorized_data(self, data: Dict[str, Any]) -> Dict[str, str]:
        """准备需要向量化的数据"""
        vectorized_data = {}
        for field in DDConstants.VECTORIZED_FIELDS:
            if field in data and data[field]:
                vectorized_data[field] = str(data[field])
        return vectorized_data
