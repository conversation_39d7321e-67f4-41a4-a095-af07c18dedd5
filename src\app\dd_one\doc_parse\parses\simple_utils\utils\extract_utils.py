import pandas as pd
import re


def is_valid_x(name: str):
    pattern = r'^(?=.*\.)(?=.*[\u4e00-\u9fa5])(?=.*\d).+$'
    return bool(re.match(pattern, str(name)))


def get_x_series(df: pd.DataFrame):
    pattern = r'^(?=.*\.)(?=.*[\u4e00-\u9fa5])(?=.*\d).+$'
    last_valid_col = None
    last_first_match_idx = None
    last_last_match_idx = None

    for col_idx in range(df.shape[1]):
        col_values = df.iloc[:, col_idx].astype(str)
        matches = col_values.str.contains(pattern, regex=True, na=False) | col_values.str.lower().str.startswith(
            'general selection')
        match_count = matches.sum()
        match_ratio = match_count / len(col_values) if len(col_values) > 0 else 0
        if match_count > 0 and match_ratio >= 0.2:
            last_valid_col = col_idx
            last_first_match_idx = matches.idxmax()
            match_indices = matches[matches].index.tolist()
            last_last_match_idx = match_indices[-1]

    if last_valid_col is not None:
        return last_valid_col, last_first_match_idx, last_last_match_idx
    print("未找到满足条件的列")
    return None, None, None


def get_detail_series(df: pd.DataFrame, row_idx: int, first_col_idx: int) -> tuple[int, int]:
    """
    从指定行开始，在指定列中查找第一个空值，返回起始行和结束行的索引。

    参数:
        df (pd.DataFrame): 输入的DataFrame
        row_idx (int): 起始行索引
        first_col_idx (int): 要查找的列索引

    返回:
        tuple[int, int]: (起始行索引, 结束行索引)，结束行是第一个空值前的行，
                        如果没有空值则返回最后一行
    """
    # 输入验证
    if not (0 <= row_idx < len(df)):
        raise ValueError("行索引超出范围")
    if not (0 <= first_col_idx+1 < len(df.columns)):
        raise ValueError("列索引超出范围")

    # 获取指定列
    column = df.iloc[:, first_col_idx+1]

    # 从row_idx开始查找第一个空值（NaN或None）
    for idx in range(row_idx, len(df)):
        if pd.isna(column.iloc[idx]):
            return row_idx, idx - 1 if idx > row_idx else row_idx

    # 如果没有找到空值，返回最后一行索引
    return row_idx, len(df) - 1


def get_id(df: pd.DataFrame, agg: tuple, axis='x'):
    fix, first, last = agg
    res = ''
    split_note = '<split>'
    # print(f"axis={axis}, fix={fix}, first={first}, last={last}")
    if axis == 'x':
        target_series = df.iloc[fix, first:last]
        for i in target_series:
            if i and not pd.isna(i):
                val = str(i)
                res += val + split_note
        if res:
            res = res[:-len(split_note)]
    else:
        target_series = df.iloc[first:last, fix]
        for i in target_series:
            if i and not pd.isna(i):
                val = str(i)
                if '. ' in val or '.' in val:
                    if val and val[0].isdigit() and val.startswith(val[0] + '.'):
                        res += val + split_note
                    continue
                else:
                    res += val + split_note
        if res:
            res = res[:-len(split_note)]
        # print('target_series',target_series)

    return res
