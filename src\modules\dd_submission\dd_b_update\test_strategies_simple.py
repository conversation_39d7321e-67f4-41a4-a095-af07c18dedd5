#!/usr/bin/env python3
"""
DD-B更新策略测试 - 使用统一入口

⚠️  警告: 此测试会实际修改数据库！请在测试环境运行！

使用DDBUpdateProcessor.process_update_request统一入口测试5种更新策略：
1. SIMPLE_UPDATE - 简单字段更新
2. BDR10_UPDATE - BDR10中文名更新
3. COLUMN_SELECTION - BDR09修改，从字段选择开始的Pipeline
4. SQL_GENERATION - BDR11修改，从SQL生成开始的Pipeline
5. BDR16_ANALYSIS - BDR16修改，分析表范围变化

TODO填写清单 (Ctrl+F搜索 "TODO"):
1. TODO_REAL_ENTRY_ID_1, TODO_REAL_ENTRY_ID_2 - 真实的submission_id
2. TODO_REAL_DEPT_ID - 真实的dept_id

获取数据SQL:
SELECT submission_id, dept_id FROM post_distribution WHERE version='S71_ADS_RELEASE_V0' LIMIT 5;
"""

import asyncio
import json
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

import os
import sys
project_root = os.getcwd()
sys.path.insert(0, project_root)
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="dd_b_update_test",
    level="DEBUG"
)

async def get_database_clients():
    """获取数据库客户端"""
    from service import get_client
    
    rdb_client = await get_client("database.rdbs.mysql")
    vdb_client = None
    embedding_client = None
    
    try:
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
    except Exception as e:
        logger.warning(f"可选客户端连接失败: {e}")
    
    return {'rdb_client': rdb_client, 'vdb_client': vdb_client, 'embedding_client': embedding_client}


async def test_all_strategies():
    """测试所有5种更新策略 - 使用统一入口"""
    print("DD-B更新策略测试")
    print("=" * 60)

    try:
        # 获取数据库客户端
        clients = await get_database_clients()

        from modules.dd_submission.dd_b_update.dd_b_update_processor import DDBUpdateProcessor
        from modules.dd_submission.dd_b_update.models.update_models import UpdateRequest

        # 使用统一的更新处理器入口
        processor = DDBUpdateProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client'],
            max_workers=2,
            enable_cache=True
        )
        
        # 测试参数
        report_code = "S71_ADS_RELEASE_V0"
        dept_id = "30239"  # 请填写真实的dept_id，如30239
        
        # 测试用例 - 使用统一入口测试不同策略
        test_cases = [
            # {
            #     "name": "策略1: SIMPLE_UPDATE - 简单字段更新",
            #     "data": [{"entry_id": "1005", "BDR01": "新值1", "BDR02": "新值2"}],
            #     "expected_strategy": "simple_update"
            # },
            # {
            #     "name": "策略2: BDR10_UPDATE - BDR10中文名更新",
            #     "data": [{"entry_id": "1005", "BDR10": "新的中文表名"}],
            #     "expected_strategy": "bdr10_update"
            # },
            # {
            #     "name": "策略3: COLUMN_SELECTION - BDR09表选择更新",
            #     "data": [{"entry_id": "1005", "BDR09": '["adm_lon_accumulative_amt", "bdm_acc_payment_sched","adm_mrk_cus_interbank"]'}],
            #     "expected_strategy": "column_selection"
            # },
            {
                "name": "策略4: SQL_GENERATION - BDR11字段选择更新",
                "data": [{"entry_id": "1005", "BDR11": '{"adm_lon_accumulative_amt": ["busi_no", "loan_amt_interes_income_cny", "issue_dt", "settle_dt"], "bdm_evt_loan_trans": ["trans_dt", "lending_ref", "balance"], "bdm_acc_loan_info": ["cust_no", "prin_od_dt", "data_dt"]}'}],
                "expected_strategy": "sql_generation"
            },
            # {
            #     "name": "策略5a: BDR16表范围变化 -> COLUMN_SELECTION",
            #     "data": [{"entry_id": "1005", "BDR16": """表范围：["adm_lon_accumulative_amt", "bdm_acc_payment_sched"]\\n条件：新的业务逻辑"""}],
            #     "expected_strategy": "column_selection"
            # },
            # {
            #     "name": "策略5b: BDR16表范围未变化 -> SIMPLE_UPDATE",
            #     "data": [{"entry_id": "1005", "BDR16": """表范围：["adm_lon_accumulative_amt", "bdm_acc_payment_sched"]\\n条件：新的业务逻辑"""}],
            #     "expected_strategy": "simple_update",
            #     "note": "需要手动调整BDR16内容，保持表范围与原数据相同"
            # },
            # {
            #     "name": "策略优先级: BDR11 + BDR09 -> SQL_GENERATION",
            #     "data": [{"entry_id": "1005", "BDR11": '{"adm_lon_accumulative_amt": ["busi_no", "loan_amt_interes_income_cny", "issue_dt", "settle_dt"], "bdm_evt_loan_trans": ["trans_dt", "lending_ref", "balance"], "bdm_acc_loan_info": ["cust_no", "prin_od_dt", "data_dt"]}', "BDR09": '["adm_lon_accumulative_amt", "bdm_acc_loan_info","bdm_evt_loan_trans"]'}],
            #     "expected_strategy": "sql_generation"
            # },
            {
                "name": "策略优先级: BDR11 + BDR09 + BDR08",
                "data": [{"entry_id": "1005", "BDR11": '{"adm_lon_accumulative_amt": ["busi_no", "loan_amt_interes_income_cny", "issue_dt", "settle_dt"], "bdm_evt_loan_trans": ["trans_dt", "lending_ref", "balance"], "bdm_acc_loan_info": ["cust_no", "prin_od_dt", "data_dt"]}', "BDR09": '["adm_lon_accumulative_amt", "bdm_acc_loan_info","bdm_evt_loan_trans"]',"BDR16": """表范围：["adm_lon_accumulative_amt", "bdm_acc_payment_sched"]\\n条件：新的业务逻辑"""}],
                "expected_strategy": "sql_generation"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print("-" * 50)

            try:
                # 创建更新请求
                update_request = UpdateRequest(
                    report_code=report_code,
                    dept_id=dept_id,
                    data=test_case["data"]
                )

                # 使用统一入口处理更新请求
                batch_result = await processor.process_update_request(update_request)

                print(f"   📊 处理结果:")
                print(f"      总数: {batch_result.total_count}")
                print(f"      成功: {batch_result.success_count}")
                print(f"      失败: {batch_result.failed_count}")
                print(f"      耗时: {batch_result.execution_time:.2f}s")

                # 检查策略
                if batch_result.results:
                    result = batch_result.results[0]
                    print(f"   � 策略分析结果:")
                    print(f"      entry_id: {result.entry_id}")
                    print(f"      策略: {result.strategy.value if result.strategy else 'unknown'}")
                    print(f"      成功: {result.success}")
                    if result.updated_fields:
                        print(f"      更新字段: {list(result.updated_fields.keys())}")
                    if result.error_message:
                        print(f"      错误: {result.error_message}")

                    # 检查策略是否正确
                    if result.strategy:
                        success = result.strategy.value == test_case["expected_strategy"]
                        status = "✅ 策略正确" if success else f"❌ 策略错误，期望: {test_case['expected_strategy']}"
                        print(f"   {status}")
                    else:
                        success = False
                        print(f"   ❌ 策略未识别")
                else:
                    success = False
                    print(f"   ❌ 没有处理结果")

                if test_case.get("note"):
                    print(f"   💡 注意: {test_case['note']}")

                results.append(success)

            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
                results.append(False)
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("🎉 测试结果汇总")
        print("-" * 30)
        
        passed = sum(results)
        total = len(results)
        
        for i, (test_case, success) in enumerate(zip(test_cases, results), 1):
            status = "✅" if success else "❌"
            print(f"  {i}. {status} {test_case['name'].split(':')[0]}")
        
        print(f"\n📊 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有策略测试通过！更新策略判断逻辑正确")
        else:
            print("⚠️ 部分策略测试失败，请检查相关逻辑")
        
        return passed == total
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    asyncio.run(test_all_strategies())
