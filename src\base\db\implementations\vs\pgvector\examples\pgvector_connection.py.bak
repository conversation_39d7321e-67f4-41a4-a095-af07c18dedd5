import asyncio
import time
import logging
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any
import psycopg2
from psycopg2.pool import SimpleConnectionPool, ThreadedConnectionPool
import asyncpg

from .config import PGVectorConnectionConfig
from .exceptions import ConnectionError, PoolError, handle_pgvector_error

logger = logging.getLogger(__name__)


class PGVectorConnectionManager:
    """
    PGVector连接管理器

    基于原生连接池的简洁实现，提供：
    1. 同步连接池管理（psycopg2）
    2. 异步连接池管理（asyncpg）
    3. 基本的健康检查
    4. 连接生命周期管理
    """

    def __init__(self, config: PGVectorConnectionConfig, **kwargs):
        """
        初始化PGVector连接管理器

        Args:
            config: 数据库连接配置
            **kwargs: 额外配置参数
        """
        self.config = config
        self.kwargs = kwargs

        # 连接池
        self.sync_pool: Optional[SimpleConnectionPool | ThreadedConnectionPool] = None
        self.async_pool: Optional[asyncpg.Pool] = None

        # 连接状态管理
        self._is_sync_connected = False
        self._is_async_connected = False
        self._connection_lock = asyncio.Lock()

        # 基本统计信息
        self._connection_stats = {
            'sync_operations': 0,
            'async_operations': 0,
            'last_connect_time': None,
            'last_disconnect_time': None
        }

        logger.info(f"初始化PGVector连接管理器: {config.host}:{config.port}")
    
    # ==================== 连接状态属性 ====================
    
    @property
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self._is_sync_connected and self.sync_pool is not None
    
    @property
    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self._is_async_connected and self.async_pool is not None
    
    @property
    def is_any_connected(self) -> bool:
        """检查是否有任何连接已建立（同步或异步）"""
        return self.is_connected or self.is_aconnected
    
    # ==================== 同步连接管理 ====================
    
    @handle_pgvector_error
    def connect(self, **kwargs) -> None:
        """建立同步连接池"""
        if self.sync_pool is not None:
            logger.warning("同步连接池已存在")
            return
        
        try:
            start_time = time.time()
            logger.info(f"开始建立同步连接池: {self.config.host}:{self.config.port}")
            
            # 保守的连接池配置 - 避免PostgreSQL资源耗尽
            min_conn = getattr(self.config, 'min_connections', 1)  # 降低最小连接数
            max_conn = getattr(self.config, 'max_connections', 10)  # 降低最大连接数
            
            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                'password': self.config.password,
                'database': self.config.database
            }
            
            # 创建连接池
            if max_conn > 1:
                self.sync_pool = ThreadedConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            else:
                self.sync_pool = SimpleConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            
            # 测试连接
            self._test_sync_connection()
            
            # 更新状态和统计
            self._is_sync_connected = True
            self._connection_stats['total_connections'] += 1
            self._connection_stats['last_connect_time'] = time.time()
            
            connect_time = time.time() - start_time
            logger.info(f"同步连接池建立成功，连接数: {min_conn}-{max_conn}，耗时: {connect_time:.3f}秒")
            
        except Exception as e:
            self._connection_stats['failed_connections'] += 1
            logger.error(f"建立同步连接池失败: {e}")
            raise ConnectionError(f"Failed to create sync connection pool: {e}") from e
    
    def _test_sync_connection(self):
        """测试同步连接"""
        test_conn = None
        try:
            test_conn = self.sync_pool.getconn()
            if test_conn is None:
                raise ConnectionError("无法从连接池获取测试连接")
            
            with test_conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                if result[0] != 1:
                    raise ConnectionError("连接测试失败")
        finally:
            if test_conn:
                self.sync_pool.putconn(test_conn)
    
    @contextmanager
    def get_sync_connection(self):
        """获取同步连接的上下文管理器"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            start_time = time.time()
            conn = self.sync_pool.getconn()
            if conn is None:
                raise PoolError("无法从连接池获取连接")

            self._connection_stats['sync_operations'] += 1
            yield conn

        except Exception as e:
            logger.error(f"同步连接操作失败: {e}")
            raise
        finally:
            if conn:
                try:
                    self.sync_pool.putconn(conn)
                    operation_time = time.time() - start_time
                    self._connection_stats['total_operation_time'] += operation_time
                except Exception as e:
                    logger.warning(f"归还同步连接失败: {e}")

    @contextmanager
    def transaction(self):
        """同步事务上下文管理器"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            start_time = time.time()
            conn = self.sync_pool.getconn()
            if conn is None:
                raise PoolError("无法从连接池获取连接")

            # 开始事务
            conn.autocommit = False
            self._connection_stats['sync_operations'] += 1

            yield conn

            # 提交事务
            conn.commit()
            logger.debug("同步事务提交成功")

        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                    logger.debug("同步事务回滚成功")
                except Exception as rollback_error:
                    logger.error(f"同步事务回滚失败: {rollback_error}")
            logger.error(f"同步事务失败: {e}")
            raise
        finally:
            if conn:
                try:
                    conn.autocommit = True  # 恢复自动提交
                    self.sync_pool.putconn(conn)
                    operation_time = time.time() - start_time
                    self._connection_stats['total_operation_time'] += operation_time
                except Exception as e:
                    logger.warning(f"归还同步连接失败: {e}")
    
    # ==================== 异步连接管理 ====================
    
    @handle_pgvector_error
    async def aconnect(self, **kwargs) -> None:
        """建立异步连接池"""
        async with self._connection_lock:
            if self.async_pool is not None:
                logger.warning("异步连接池已存在")
                return
            
            try:
                start_time = time.time()
                logger.info(f"开始建立异步连接池: {self.config.host}:{self.config.port}")
                
                # 保守的连接池配置 - 避免PostgreSQL资源耗尽
                min_conn = getattr(self.config, 'min_connections', 1)  # 降低最小连接数
                max_conn = getattr(self.config, 'max_connections', 10)  # 降低最大连接数
                
                connection_params = {
                    'host': self.config.host,
                    'port': self.config.port,
                    'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                    'password': self.config.password,
                    'database': self.config.database,
                    'min_size': min_conn,
                    'max_size': max_conn,
                    'command_timeout': kwargs.get('command_timeout', 60),
                    'max_inactive_connection_lifetime': kwargs.get('max_inactive_connection_lifetime', 300)
                }
                
                # 创建异步连接池
                self.async_pool = await asyncpg.create_pool(**connection_params)
                
                # 测试连接
                await self._test_async_connection()
                
                # 更新状态和统计
                self._is_async_connected = True
                self._connection_stats['total_connections'] += 1
                self._connection_stats['last_connect_time'] = time.time()
                
                connect_time = time.time() - start_time
                logger.info(f"异步连接池建立成功，连接数: {min_conn}-{max_conn}，耗时: {connect_time:.3f}秒")

                # 记录连接池建立后的状态
                self.log_connection_pool_status("连接池建立完成")
                
            except Exception as e:
                self._connection_stats['failed_connections'] += 1
                logger.error(f"建立异步连接池失败: {e}")
                raise ConnectionError(f"Failed to create async connection pool: {e}") from e
    
    async def _test_async_connection(self):
        """测试异步连接"""
        async with self.async_pool.acquire() as conn:
            result = await conn.fetchval("SELECT 1")
            if result != 1:
                raise ConnectionError("异步连接测试失败")

    async def awarm_up_connections(self, target_connections: int = 3) -> None:
        """
        预热连接池 - 在批量操作前预先建立连接

        Args:
            target_connections: 目标连接数，不超过max_connections
        """
        if not self.is_aconnected:
            logger.warning("连接池未建立，无法预热连接")
            return

        try:
            # 限制预热连接数不超过最大连接数
            max_conn = getattr(self.config, 'max_connections', 10)
            target_connections = min(target_connections, max_conn)

            logger.info(f"开始预热连接池，目标连接数: {target_connections}")

            # 并发获取多个连接来预热连接池
            connections = []
            try:
                for i in range(target_connections):
                    conn = await self.async_pool.acquire()
                    connections.append(conn)
                    # 执行简单查询确保连接可用
                    await conn.fetchval("SELECT 1")

                logger.info(f"连接池预热成功，已建立 {len(connections)} 个连接")

                # 记录预热后的连接池状态
                self.log_connection_pool_status("连接池预热完成")

            finally:
                # 释放所有预热连接回连接池
                for conn in connections:
                    try:
                        await self.async_pool.release(conn)
                    except Exception as e:
                        logger.warning(f"释放预热连接失败: {e}")

        except Exception as e:
            logger.warning(f"连接池预热失败: {e}")
            # 预热失败不应该影响正常操作，只记录警告
    
    @asynccontextmanager
    async def get_async_connection(self):
        """获取异步连接的上下文管理器 - 带全局限流保护和详细诊断"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        start_time = time.time()
        operation_id = f"op_{int(start_time * 1000) % 100000}"  # 操作ID用于追踪

        # 记录连接获取开始
        logger.debug(f"[{operation_id}] 开始获取异步连接")

        # 获取全局限流器状态
        global_limiter_available = self._global_connection_limiter._value
        logger.debug(f"[{operation_id}] 全局限流器可用许可: {global_limiter_available}")

        # 获取连接池状态
        if hasattr(self.async_pool, '_holders'):
            pool_size = len(self.async_pool._holders)
            pool_free = len([h for h in self.async_pool._holders if h._con is not None])
            logger.debug(f"[{operation_id}] 连接池状态: 总大小={pool_size}, 可用连接={pool_free}")

        try:
            # 等待全局连接限流器
            limiter_wait_start = time.time()
            logger.debug(f"[{operation_id}] 等待全局连接限流器...")

            async with self._global_connection_limiter:
                limiter_wait_time = time.time() - limiter_wait_start
                logger.debug(f"[{operation_id}] 获得全局连接许可，等待时间: {limiter_wait_time:.3f}s")

                # 等待连接池连接（带超时）
                pool_wait_start = time.time()
                logger.debug(f"[{operation_id}] 等待连接池连接...")

                try:
                    # 正确的超时处理方式：先获取连接，再使用上下文管理器
                    conn = await asyncio.wait_for(
                        self.async_pool.acquire(),
                        timeout=30.0  # 30秒超时
                    )

                    try:
                        pool_wait_time = time.time() - pool_wait_start
                        total_wait_time = time.time() - start_time

                        self._connection_stats['async_operations'] += 1

                        logger.debug(f"[{operation_id}] 连接获取成功! "
                                   f"连接池等待: {pool_wait_time:.3f}s, "
                                   f"总等待时间: {total_wait_time:.3f}s, "
                                   f"累计操作数: {self._connection_stats['async_operations']}")

                        yield conn

                        logger.debug(f"[{operation_id}] 连接使用完毕，准备释放")

                    finally:
                        # 确保连接被正确释放回连接池
                        try:
                            await self.async_pool.release(conn)
                            logger.debug(f"[{operation_id}] 连接已释放回连接池")
                        except Exception as release_error:
                            logger.error(f"[{operation_id}] 释放连接失败: {release_error}")

                except asyncio.TimeoutError:
                    pool_wait_time = time.time() - pool_wait_start
                    logger.error(f"[{operation_id}] 连接池获取超时! 等待时间: {pool_wait_time:.3f}s")
                    raise ConnectionError(f"连接池获取超时，等待时间: {pool_wait_time:.3f}s")

        except Exception as e:
            operation_time = time.time() - start_time
            error_details = {
                'operation_id': operation_id,
                'error': str(e),
                'operation_time': f"{operation_time:.3f}s",
                'global_limiter_available': global_limiter_available,
                'connection_stats': self._connection_stats.copy()
            }

            logger.error(f"[{operation_id}] 异步连接操作失败: {e}")
            logger.error(f"[{operation_id}] 错误详情: {error_details}")

            # 检查是否是PostgreSQL资源耗尽错误
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in [
                'out of shared memory', 'max_locks_per_transaction',
                'could not serialize access', 'deadlock detected'
            ]):
                logger.error(f"[{operation_id}] 检测到PostgreSQL资源耗尽错误!")

                # 获取更详细的连接池状态
                if hasattr(self.async_pool, '_holders'):
                    holders_info = []
                    for i, holder in enumerate(self.async_pool._holders):
                        holder_info = {
                            'index': i,
                            'has_connection': holder._con is not None,
                            'in_use': getattr(holder, '_in_use', 'unknown')
                        }
                        holders_info.append(holder_info)
                    logger.error(f"[{operation_id}] 连接池详细状态: {holders_info}")

            raise
        finally:
            operation_time = time.time() - start_time
            self._connection_stats['total_operation_time'] += operation_time
            logger.debug(f"[{operation_id}] 连接操作完成，总耗时: {operation_time:.3f}s")

    def get_detailed_connection_stats(self) -> Dict[str, Any]:
        """获取详细的连接统计信息"""
        stats = self._connection_stats.copy()

        # 添加全局限流器状态
        stats['global_limiter_available'] = self._global_connection_limiter._value
        stats['global_limiter_locked'] = self._global_connection_limiter.locked()

        # 添加连接池状态
        if self.async_pool and hasattr(self.async_pool, '_holders'):
            holders = self.async_pool._holders
            stats['pool_total_size'] = len(holders)
            stats['pool_free_connections'] = len([h for h in holders if h._con is not None])
            stats['pool_busy_connections'] = stats['pool_total_size'] - stats['pool_free_connections']

            # 连接池配置信息
            stats['pool_min_size'] = getattr(self.async_pool, '_minsize', 'unknown')
            stats['pool_max_size'] = getattr(self.async_pool, '_maxsize', 'unknown')

        # 计算平均操作时间
        if stats['async_operations'] > 0:
            stats['avg_operation_time'] = stats['total_operation_time'] / stats['async_operations']
        else:
            stats['avg_operation_time'] = 0

        return stats

    def log_connection_pool_status(self, context: str = ""):
        """记录连接池状态日志"""
        stats = self.get_detailed_connection_stats()
        logger.info(f"连接池状态{f' ({context})' if context else ''}: {stats}")

        # 如果操作数异常高，发出警告
        if stats['async_operations'] > 100:
            logger.warning(f"异步操作数异常高: {stats['async_operations']}，可能存在连接泄漏或异常重试")

            # 如果操作数超过200，强制记录详细状态
            if stats['async_operations'] > 200:
                logger.error(f"连接池可能出现严重问题! 操作数: {stats['async_operations']}")
                logger.error(f"详细状态: {stats}")

                # 建议立即采取措施
                logger.error("建议立即采取措施:")
                logger.error("1. 检查是否存在连接泄漏")
                logger.error("2. 增加连接池大小")
                logger.error("3. 降低并发请求数")
                logger.error("4. 重启连接池")

    @asynccontextmanager
    async def atransaction(self):
        """异步事务上下文管理器"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        start_time = time.time()
        try:
            async with self.async_pool.acquire() as conn:
                # 开始事务
                async with conn.transaction():
                    self._connection_stats['async_operations'] += 1
                    yield conn
                    # asyncpg的transaction()上下文管理器会自动提交
                    logger.debug("异步事务提交成功")
        except Exception as e:
            # asyncpg的transaction()上下文管理器会自动回滚
            logger.error(f"异步事务失败: {e}")
            logger.debug("异步事务回滚成功")
            raise
        finally:
            operation_time = time.time() - start_time
            self._connection_stats['total_operation_time'] += operation_time

    # ==================== 断开连接管理 ====================

    def disconnect(self) -> None:
        """断开同步连接池"""
        if self.sync_pool is not None:
            try:
                self.sync_pool.closeall()
                self.sync_pool = None
                self._is_sync_connected = False
                self._connection_stats['last_disconnect_time'] = time.time()
                logger.info("同步连接池已断开")
            except Exception as e:
                logger.warning(f"断开同步连接池时出错: {e}")

    async def adisconnect(self) -> None:
        """断开异步连接池"""
        async with self._connection_lock:
            if self.async_pool is not None:
                try:
                    await self.async_pool.close()
                    self.async_pool = None
                    self._is_async_connected = False
                    self._connection_stats['last_disconnect_time'] = time.time()
                    logger.info("异步连接池已断开")
                except Exception as e:
                    logger.warning(f"断开异步连接池时出错: {e}")

    def disconnect_all(self) -> None:
        """断开所有连接"""
        self.disconnect()
        # 异步断开需要在异步上下文中调用
        if self.async_pool is not None:
            logger.warning("异步连接池需要在异步上下文中断开")

    # ==================== 健康检查和监控 ====================

    def health_check(self) -> Dict[str, Any]:
        """执行健康检查 - 智能检查已建立的连接类型"""
        current_time = time.time()

        # 检查是否需要执行健康检查
        if current_time - self._last_health_check < self._health_check_interval:
            return self._get_cached_health_status()

        # 智能健康检查：优先检查已建立的连接类型
        if self.is_aconnected:
            # 如果异步连接已建立，返回异步连接状态（不强制建立同步连接）
            health_status = {
                'timestamp': current_time,
                'connection_type': 'async',
                'async_connected': True,
                'sync_connected': self.is_connected,
                'overall_status': 'healthy',
                'healthy': True,
                'response_time': 0.001,  # 异步连接已建立，响应时间很快
                'note': 'Using async connection status'
            }
        elif self.is_connected:
            # 如果只有同步连接，检查同步连接状态
            sync_health = self._check_sync_health()
            health_status = {
                'timestamp': current_time,
                'connection_type': 'sync',
                'sync_connection': sync_health,
                'overall_status': 'healthy' if sync_health['status'] == 'healthy' else 'unhealthy',
                'healthy': sync_health['status'] == 'healthy',
                'response_time': sync_health.get('response_time', 0)
            }
        else:
            # 没有任何连接
            health_status = {
                'timestamp': current_time,
                'connection_type': 'none',
                'overall_status': 'disconnected',
                'healthy': False,
                'response_time': 0,
                'error': 'No connections established'
            }

        self._last_health_check = current_time
        self._cached_health_status = health_status
        return health_status

    def _get_cached_health_status(self) -> Dict[str, Any]:
        """获取缓存的健康状态"""
        if self._cached_health_status:
            # 返回缓存状态，但更新时间戳
            cached_status = self._cached_health_status.copy()
            cached_status['timestamp'] = time.time()
            cached_status['note'] = f"Cached status from {self._cached_health_status.get('timestamp', 0):.0f}"
            return cached_status

        # 如果没有缓存，返回基本状态
        return {
            'timestamp': time.time(),
            'overall_status': 'unknown',
            'healthy': False,
            'response_time': 0,
            'note': 'No cached health status available'
        }

    async def ahealth_check(self) -> Dict[str, Any]:
        """执行异步健康检查"""
        # 检查异步连接状态
        async_health = await self._check_async_health()

        health_status = {
            'timestamp': time.time(),
            'async_connection': async_health,
            'overall_status': 'healthy' if async_health['status'] == 'healthy' else 'unhealthy',
            'healthy': async_health['status'] == 'healthy',  # 为测试兼容性添加
            'response_time': async_health.get('response_time', 0)
        }

        return health_status

    def _check_sync_health(self) -> Dict[str, Any]:
        """检查同步连接健康状态"""
        if not self.is_connected:
            return {'status': 'disconnected', 'error': 'No sync connection pool'}

        try:
            with self.get_sync_connection() as conn:
                with conn.cursor() as cur:
                    start_time = time.time()
                    cur.execute("SELECT 1")
                    result = cur.fetchone()
                    response_time = time.time() - start_time

                    if result[0] == 1:
                        return {
                            'status': 'healthy',
                            'response_time': response_time,
                            'pool_info': self._get_sync_pool_info()
                        }
                    else:
                        return {'status': 'error', 'error': 'Health check query failed'}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    async def _check_async_health(self) -> Dict[str, Any]:
        """检查异步连接健康状态"""
        if not self.is_aconnected:
            return {'status': 'disconnected', 'error': 'No async connection pool'}

        try:
            async with self.get_async_connection() as conn:
                start_time = time.time()
                result = await conn.fetchval("SELECT 1")
                response_time = time.time() - start_time

                if result == 1:
                    return {
                        'status': 'healthy',
                        'response_time': response_time,
                        'pool_info': self._get_async_pool_info()
                    }
                else:
                    return {'status': 'error', 'error': 'Health check query failed'}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def _get_cached_health_status(self) -> Dict[str, Any]:
        """获取缓存的健康状态"""
        return {
            'timestamp': self._last_health_check,
            'sync_connection': {'status': 'healthy' if self.is_connected else 'disconnected'},
            'async_connection': {'status': 'healthy' if self.is_aconnected else 'disconnected'},
            'overall_status': 'cached',
            'note': 'Cached status, use force_health_check for real-time status'
        }

    def _get_sync_pool_info(self) -> Dict[str, Any]:
        """获取同步连接池信息"""
        if not self.sync_pool:
            return {}

        try:
            return {
                'minconn': getattr(self.sync_pool, 'minconn', 'unknown'),
                'maxconn': getattr(self.sync_pool, 'maxconn', 'unknown'),
                'pool_type': type(self.sync_pool).__name__
            }
        except Exception:
            return {'error': 'Unable to get pool info'}

    def _get_async_pool_info(self) -> Dict[str, Any]:
        """获取异步连接池信息"""
        if not self.async_pool:
            return {}

        try:
            return {
                'min_size': getattr(self.async_pool, '_min_size', 'unknown'),
                'max_size': getattr(self.async_pool, '_max_size', 'unknown'),
                'current_size': len(getattr(self.async_pool, '_holders', [])),
                'pool_type': type(self.async_pool).__name__
            }
        except Exception:
            return {'error': 'Unable to get pool info'}

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        stats = self._connection_stats.copy()
        stats.update({
            'sync_connected': self.is_connected,
            'async_connected': self.is_aconnected,
            'health_check_interval': self._health_check_interval,
            'last_health_check': self._last_health_check,
            'avg_operation_time': (
                stats['total_operation_time'] / max(1, stats['sync_operations'] + stats['async_operations'])
            )
        })
        return stats

    # ==================== 向后兼容接口 ====================

    def _ensure_connected(self) -> None:
        """确保连接已建立（向后兼容）"""
        if not self.is_any_connected:
            self.connect()

    async def _aensure_connected(self) -> None:
        """确保异步连接已建立（向后兼容）"""
        if not self.is_aconnected:
            await self.aconnect()

    def sync_health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """同步健康检查（向后兼容）"""
        # 确保连接已建立
        self._ensure_connected()
        return self._check_sync_health()

    async def async_health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """异步健康检查（向后兼容）"""
        # 确保异步连接已建立
        await self._aensure_connected()
        return await self._check_async_health()

    @contextmanager
    def get_cursor(self):
        """获取同步游标（向后兼容）"""
        with self.get_sync_connection() as conn:
            with conn.cursor() as cursor:
                yield cursor

    @asynccontextmanager
    async def get_async_cursor(self):
        """获取异步游标（向后兼容）"""
        async with self.get_async_connection() as conn:
            yield conn  # asyncpg连接对象本身就是cursor

    # ==================== 清理和资源管理 ====================

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.disconnect_all()
        except Exception as e:
            logger.warning(f"清理连接资源时出错: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.aconnect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.adisconnect()
