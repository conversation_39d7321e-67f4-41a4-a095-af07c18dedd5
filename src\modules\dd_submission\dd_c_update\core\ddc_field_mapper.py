"""
DD-C字段映射器 - 完全独立实现

支持复杂的字段覆盖、组合策略和Pipeline结果映射
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field

from .ddc_field_analyzer import DDCUpdateStrategy
from .ddc_update_crud import DDCUpdateCrud

logger = logging.getLogger(__name__)


@dataclass
class DDCFieldMappingResult:
    """DD-C字段映射结果"""
    record_id: int
    mapping_success: bool = False
    mapping_notes: List[str] = field(default_factory=list)
    
    # SDR字段（原始格式）
    sdr05_raw: Optional[List[str]] = None      # 表英文名列表
    sdr06_raw: Optional[List[str]] = None      # 表中文名列表
    sdr08_raw: Optional[Dict[str, List[str]]] = None  # 字段映射
    sdr09_raw: Optional[Dict[str, str]] = None # 字段中文名映射
    sdr10_raw: Optional[str] = None            # SQL语句
    sdr12_raw: Optional[str] = None            # JOIN条件
    
    # BDR字段（同步更新）
    bdr09_raw: Optional[List[str]] = None      # = sdr05
    bdr10_raw: Optional[List[str]] = None      # = sdr06
    bdr11_raw: Optional[Dict[str, List[str]]] = None  # = sdr08
    bdr16_raw: Optional[str] = None            # 业务逻辑描述
    
    def to_string_format(self, reserved_fields: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        转换为字符串格式（用于数据库存储）

        Args:
            reserved_fields: 保留字段，优先级高于Pipeline结果

        Returns:
            最终的更新字段字典
        """
        result = {}

        # SDR字段转换（支持字符串格式和原始格式）
        if self.sdr05_raw is not None:
            if isinstance(self.sdr05_raw, str):
                result['sdr05'] = self.sdr05_raw  # 已经是字符串格式
            else:
                result['sdr05'] = json.dumps(self.sdr05_raw, ensure_ascii=False)
        if self.sdr06_raw is not None:
            if isinstance(self.sdr06_raw, str):
                result['sdr06'] = self.sdr06_raw
            else:
                result['sdr06'] = json.dumps(self.sdr06_raw, ensure_ascii=False)
        if self.sdr08_raw is not None:
            if isinstance(self.sdr08_raw, str):
                result['sdr08'] = self.sdr08_raw
            else:
                result['sdr08'] = json.dumps(self.sdr08_raw, ensure_ascii=False)
        if self.sdr09_raw is not None:
            if isinstance(self.sdr09_raw, str):
                result['sdr09'] = self.sdr09_raw
            else:
                result['sdr09'] = json.dumps(self.sdr09_raw, ensure_ascii=False)
        if self.sdr10_raw is not None:
            result['sdr10'] = str(self.sdr10_raw)
        if self.sdr12_raw is not None:
            result['sdr12'] = str(self.sdr12_raw)

        # BDR字段转换（同步SDR字段）
        if self.bdr09_raw is not None:
            if isinstance(self.bdr09_raw, str):
                result['bdr09'] = self.bdr09_raw
            else:
                result['bdr09'] = json.dumps(self.bdr09_raw, ensure_ascii=False)
        if self.bdr10_raw is not None:
            if isinstance(self.bdr10_raw, str):
                result['bdr10'] = self.bdr10_raw
            else:
                result['bdr10'] = json.dumps(self.bdr10_raw, ensure_ascii=False)
        if self.bdr11_raw is not None:
            if isinstance(self.bdr11_raw, str):
                result['bdr11'] = self.bdr11_raw
            else:
                result['bdr11'] = json.dumps(self.bdr11_raw, ensure_ascii=False)
        if self.bdr16_raw is not None:
            result['bdr16'] = str(self.bdr16_raw)  # BDR16始终是字符串

        # 应用保留字段覆盖（优先级最高）
        # 注意：BDR16不在覆盖列表中，确保使用Pipeline生成的值
        if reserved_fields:
            for field_name, field_value in reserved_fields.items():
                field_lower = field_name.lower()
                if field_lower in ['sdr05', 'sdr06', 'sdr08', 'sdr09', 'sdr10', 'sdr12',
                                  'bdr09', 'bdr10', 'bdr11']:  # 移除bdr16
                    # 对于JSON字段，确保正确格式化
                    if field_lower in ['sdr05', 'sdr06', 'sdr08', 'sdr09', 'bdr09', 'bdr10', 'bdr11']:
                        if isinstance(field_value, (list, dict)):
                            result[field_lower] = json.dumps(field_value, ensure_ascii=False)
                        else:
                            result[field_lower] = str(field_value)
                    else:
                        # 字符串字段直接赋值
                        result[field_lower] = str(field_value)

        return result


class DDCFieldMapper:
    """DD-C字段映射器 - 完全独立实现"""
    
    def __init__(self, update_crud: DDCUpdateCrud):
        """
        初始化DD-C字段映射器
        
        Args:
            update_crud: DD-C数据库操作组件
        """
        self.update_crud = update_crud
        logger.debug("DD-C字段映射器初始化完成")
    
    async def map_pipeline_result(
        self,
        pipeline_result: Dict[str, Any],
        original_data: Dict[str, Any],
        strategy: DDCUpdateStrategy,
        override_fields: Optional[Dict[str, Any]] = None
    ) -> DDCFieldMappingResult:
        """
        映射Pipeline结果到DD-C字段

        使用DD-B的PipelineFieldMapper进行通用Pipeline结果处理，
        然后根据DD-C策略选择需要的字段，最后应用保留字段覆盖。

        Args:
            pipeline_result: Pipeline执行结果
            original_data: 原始记录数据
            strategy: 更新策略
            override_fields: 覆盖字段（优先级更高）

        Returns:
            字段映射结果
        """
        try:
            record_id = original_data.get('id', 0)
            logger.debug(f"开始DD-C字段映射: record_id={record_id}, strategy={strategy.value}")

            # 1. 使用DD-B的PipelineFieldMapper进行通用处理
            from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
            from modules.knowledge.metadata.crud import MetadataCrud

            metadata_crud = MetadataCrud(self.update_crud.rdb_client)
            pipeline_mapper = PipelineFieldMapper(metadata_crud)

            # 2. 创建记录对象（兼容DD-B的接口）
            original_record = self._create_record_object(original_data)

            # 3. 执行通用Pipeline结果映射
            pipeline_mapping_result = await pipeline_mapper.map_single_record(
                pipeline_result, original_record, keep_raw_format=False
            )

            if not pipeline_mapping_result.mapping_success:
                logger.warning(f"Pipeline字段映射失败: record_id={record_id}")
                result = DDCFieldMappingResult(record_id=record_id)
                result.mapping_notes = pipeline_mapping_result.mapping_notes
                return result

            # 4. 转换为字符串格式（DD-B的完整字段集合）
            string_results = pipeline_mapper.convert_to_string_format([pipeline_mapping_result])
            if not string_results:
                logger.warning(f"字符串格式转换失败: record_id={record_id}")
                result = DDCFieldMappingResult(record_id=record_id)
                result.mapping_notes = ["字符串格式转换失败"]
                return result

            string_result = string_results[0]

            # 5. 合并Pipeline结果和override_fields（真正的字典合并）
            updated_fields = self._merge_pipeline_and_override_fields(string_result, override_fields)

            # 7. 转换为DD-C的结果格式
            result = self._convert_to_ddc_result(updated_fields, record_id, pipeline_mapping_result.mapping_notes)

            logger.debug(f"DD-C字段映射完成: record_id={record_id}, 成功={result.mapping_success}")
            logger.debug(f"  更新字段: {list(updated_fields.keys())}")

            return result

        except Exception as e:
            logger.error(f"DD-C字段映射失败: record_id={original_data.get('id', 0)}, error={e}")
            result = DDCFieldMappingResult(record_id=original_data.get('id', 0))
            result.mapping_notes = [f"字段映射异常: {e}"]
            return result

    def _create_record_object(self, original_data: Dict[str, Any]):
        """创建兼容DD-B接口的记录对象"""
        from dataclasses import dataclass

        @dataclass
        class DDCRecord:
            id: int
            dept_id: str = ""
            report_code: str = ""
            dr01: str = ""
            bdr09: str = ""
            bdr10: str = ""
            bdr11: str = ""
            bdr16: str = ""
            bdr17: str = ""

        return DDCRecord(
            id=int(original_data.get('id', 0)),
            dept_id=original_data.get('dept_id', ''),
            report_code=original_data.get('version', ''),
            dr01=original_data.get('dr01', ''),
            bdr09=original_data.get('bdr09', ''),
            bdr10=original_data.get('bdr10', ''),
            bdr11=original_data.get('bdr11', ''),
            bdr16=original_data.get('bdr16', ''),
            bdr17=original_data.get('bdr17', '')
        )

    def _merge_pipeline_and_override_fields(
        self,
        pipeline_result: Dict[str, str],
        override_fields: Optional[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        合并Pipeline结果和override_fields，实现真正的字典合并

        合并策略：
        1. 以Pipeline结果为基础
        2. override_fields可以覆盖Pipeline结果中的现有字段
        3. override_fields可以添加Pipeline结果中不存在的新字段
        4. 过滤掉空值字段

        Args:
            pipeline_result: DD-B Pipeline映射器的字符串格式结果
            override_fields: 策略特定的覆盖字段

        Returns:
            合并后的字段字典
        """
        # 1. 从Pipeline结果开始（过滤空值）
        merged_fields = {k: v for k, v in pipeline_result.items() if v}
        logger.debug(f"Pipeline结果字段: {list(merged_fields.keys())}")

        # 2. 合并override_fields（覆盖现有字段，添加新字段）
        if override_fields:
            for field_name, field_value in override_fields.items():
                field_lower = field_name.lower()
                if field_value:  # 只添加非空值
                    if field_lower in merged_fields:
                        logger.debug(f"覆盖字段: {field_lower} = {field_value}")
                    else:
                        logger.debug(f"新增字段: {field_lower} = {field_value}")
                    merged_fields[field_lower] = field_value

        logger.debug(f"合并后字段: {list(merged_fields.keys())}")
        return merged_fields



    def _convert_to_ddc_result(self, updated_fields: Dict[str, str], record_id: int, mapping_notes: List[str]) -> DDCFieldMappingResult:
        """转换为DD-C的结果格式"""
        result = DDCFieldMappingResult(record_id=record_id)
        result.mapping_success = True
        result.mapping_notes = mapping_notes + ["使用DD-B PipelineFieldMapper处理"]

        # 直接存储字符串格式的字段，to_string_format会直接返回这些值
        # 这样避免了不必要的JSON解析和重新序列化
        try:
            # SDR字段（存储为字符串格式，to_string_format直接使用）
            if 'sdr05' in updated_fields and updated_fields['sdr05']:
                result.sdr05_raw = updated_fields['sdr05']  # 直接存储字符串
            if 'sdr06' in updated_fields and updated_fields['sdr06']:
                result.sdr06_raw = updated_fields['sdr06']
            if 'sdr08' in updated_fields and updated_fields['sdr08']:
                result.sdr08_raw = updated_fields['sdr08']
            if 'sdr09' in updated_fields and updated_fields['sdr09']:
                result.sdr09_raw = updated_fields['sdr09']
            if 'sdr10' in updated_fields and updated_fields['sdr10']:
                result.sdr10_raw = updated_fields['sdr10']
            if 'sdr12' in updated_fields and updated_fields['sdr12']:
                result.sdr12_raw = updated_fields['sdr12']

            # BDR字段
            if 'bdr09' in updated_fields and updated_fields['bdr09']:
                result.bdr09_raw = updated_fields['bdr09']
            if 'bdr10' in updated_fields and updated_fields['bdr10']:
                result.bdr10_raw = updated_fields['bdr10']
            if 'bdr11' in updated_fields and updated_fields['bdr11']:
                result.bdr11_raw = updated_fields['bdr11']
            if 'bdr16' in updated_fields and updated_fields['bdr16']:
                result.bdr16_raw = updated_fields['bdr16']  # 关键：Pipeline生成的BDR16

        except Exception as e:
            logger.warning(f"字段转换失败: {e}")
            result.mapping_notes.append(f"字段转换失败: {e}")

        return result

    def _is_pipeline_result_valid(self, pipeline_result: Dict[str, Any]) -> bool:
        """检查Pipeline结果是否有效"""
        if not pipeline_result:
            return False
        
        # 检查关键字段
        meaningful_keys = ['parser_info', 'sql_candidates', 'business_logic', 'candidate_columns']
        has_meaningful_data = any(
            key in pipeline_result and pipeline_result[key] 
            for key in meaningful_keys
        )
        
        return has_meaningful_data
    
    async def _map_sdr05_strategy(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        mapping_notes: List[str]
    ):
        """映射SDR05策略（表英文名修改）"""
        logger.debug("执行SDR05策略映射")
        
        # 从Pipeline结果提取表信息
        parser_info = pipeline_result.get('parser_info', {})
        tables = parser_info.get('tables', [])
        
        if tables:
            result.sdr05_raw = tables
            mapping_notes.append(f"SDR05映射成功: {len(tables)}个表")
            
            # 生成表中文名
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(tables)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in tables]
            mapping_notes.append(f"SDR06映射成功: {len(result.sdr06_raw)}个表中文名")
        else:
            mapping_notes.append("SDR05映射失败: 未找到表信息")
    
    async def _map_sdr08_strategy(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        mapping_notes: List[str]
    ):
        """映射SDR08策略（字段映射修改）"""
        logger.debug("执行SDR08策略映射")
        
        # 从Pipeline结果提取字段映射
        candidate_columns = pipeline_result.get('candidate_columns', {})
        
        if candidate_columns:
            result.sdr08_raw = candidate_columns
            mapping_notes.append(f"SDR08映射成功: {len(candidate_columns)}个表的字段映射")
            
            # 提取表列表
            tables = list(candidate_columns.keys())
            result.sdr05_raw = tables
            mapping_notes.append(f"SDR05映射成功: {len(tables)}个表")
            
            # 生成表中文名
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(tables)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in tables]
            mapping_notes.append(f"SDR06映射成功: {len(result.sdr06_raw)}个表中文名")
            
            # 生成字段中文名
            column_cn_mapping = await self.update_crud.generate_column_chinese_names(candidate_columns)
            result.sdr09_raw = column_cn_mapping
            mapping_notes.append(f"SDR09映射成功: {len(column_cn_mapping)}个字段中文名")
            
            # 提取SQL语句
            sql_candidates = pipeline_result.get('sql_candidates', [])
            if sql_candidates:
                result.sdr10_raw = sql_candidates[0] if isinstance(sql_candidates, list) else str(sql_candidates)
                mapping_notes.append("SDR10映射成功: SQL语句")
        else:
            mapping_notes.append("SDR08映射失败: 未找到字段映射")
    
    async def _map_sdr10_strategy(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        mapping_notes: List[str]
    ):
        """映射SDR10策略（SQL语句修改）"""
        logger.debug("执行SDR10策略映射")
        
        # 从Pipeline结果提取表信息（从SQL解析得到）
        parser_info = pipeline_result.get('parser_info', {})
        tables = parser_info.get('tables', [])
        
        if tables:
            result.sdr05_raw = tables
            mapping_notes.append(f"SDR05映射成功: {len(tables)}个表")
            
            # 生成表中文名
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(tables)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in tables]
            mapping_notes.append(f"SDR06映射成功: {len(result.sdr06_raw)}个表中文名")
            
            # 提取JOIN条件
            join_info = parser_info.get('join', '')
            if join_info:
                result.sdr12_raw = join_info
                mapping_notes.append("SDR12映射成功: JOIN条件")
        else:
            mapping_notes.append("SDR10策略映射失败: 未找到表信息")
    
    async def _map_sdr05_sdr08_composite(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        override_fields: Optional[Dict[str, Any]],
        mapping_notes: List[str]
    ):
        """映射SDR05+SDR08组合策略"""
        logger.debug("执行SDR05+SDR08组合策略映射")
        
        # 使用覆盖字段中的固定值
        if override_fields:
            if 'SDR05' in override_fields:
                try:
                    sdr05_value = override_fields['SDR05']
                    if isinstance(sdr05_value, str):
                        result.sdr05_raw = json.loads(sdr05_value)
                    else:
                        result.sdr05_raw = sdr05_value
                    mapping_notes.append("SDR05使用固定值")
                except Exception as e:
                    logger.warning(f"解析固定SDR05失败: {e}")
            
            if 'SDR08' in override_fields:
                try:
                    sdr08_value = override_fields['SDR08']
                    if isinstance(sdr08_value, str):
                        result.sdr08_raw = json.loads(sdr08_value)
                    else:
                        result.sdr08_raw = sdr08_value
                    mapping_notes.append("SDR08使用固定值")
                except Exception as e:
                    logger.warning(f"解析固定SDR08失败: {e}")
        
        # 从固定的SDR05和SDR08生成其他字段
        if result.sdr05_raw:
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(result.sdr05_raw)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in result.sdr05_raw]
            mapping_notes.append(f"SDR06从固定SDR05生成: {len(result.sdr06_raw)}个表中文名")
        
        if result.sdr08_raw:
            column_cn_mapping = await self.update_crud.generate_column_chinese_names(result.sdr08_raw)
            result.sdr09_raw = column_cn_mapping
            mapping_notes.append(f"SDR09从固定SDR08生成: {len(column_cn_mapping)}个字段中文名")
        
        # Pipeline结果用于其他字段
        sql_candidates = pipeline_result.get('sql_candidates', [])
        if sql_candidates:
            result.sdr10_raw = sql_candidates[0] if isinstance(sql_candidates, list) else str(sql_candidates)
            mapping_notes.append("SDR10从Pipeline结果获取")
    
    async def _map_sdr08_sdr10_composite(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        override_fields: Optional[Dict[str, Any]],
        mapping_notes: List[str]
    ):
        """映射SDR08+SDR10组合策略"""
        logger.debug("执行SDR08+SDR10组合策略映射")
        
        # 使用覆盖字段中的固定值
        if override_fields:
            if 'SDR08' in override_fields:
                try:
                    sdr08_value = override_fields['SDR08']
                    if isinstance(sdr08_value, str):
                        result.sdr08_raw = json.loads(sdr08_value)
                    else:
                        result.sdr08_raw = sdr08_value
                    mapping_notes.append("SDR08使用固定值")
                    
                    # 从SDR08生成SDR05
                    result.sdr05_raw = list(result.sdr08_raw.keys())
                    mapping_notes.append(f"SDR05从固定SDR08生成: {len(result.sdr05_raw)}个表")
                except Exception as e:
                    logger.warning(f"解析固定SDR08失败: {e}")
            
            if 'SDR10' in override_fields:
                result.sdr10_raw = override_fields['SDR10']
                mapping_notes.append("SDR10使用固定值")
        
        # 从固定的SDR08生成其他字段
        if result.sdr08_raw:
            # 生成表中文名
            tables = list(result.sdr08_raw.keys())
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(tables)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in tables]
            mapping_notes.append(f"SDR06从固定SDR08生成: {len(result.sdr06_raw)}个表中文名")
            
            # 生成字段中文名
            column_cn_mapping = await self.update_crud.generate_column_chinese_names(result.sdr08_raw)
            result.sdr09_raw = column_cn_mapping
            mapping_notes.append(f"SDR09从固定SDR08生成: {len(column_cn_mapping)}个字段中文名")
        
        # Pipeline结果用于其他字段（如SDR12）
        parser_info = pipeline_result.get('parser_info', {})
        join_info = parser_info.get('join', '')
        if join_info:
            result.sdr12_raw = join_info
            mapping_notes.append("SDR12从Pipeline结果获取")
    
    async def _map_sdr05_sdr10_composite(
        self, 
        result: DDCFieldMappingResult, 
        pipeline_result: Dict[str, Any], 
        original_data: Dict[str, Any],
        override_fields: Optional[Dict[str, Any]],
        mapping_notes: List[str]
    ):
        """映射SDR05+SDR10组合策略"""
        logger.debug("执行SDR05+SDR10组合策略映射")
        
        # 使用覆盖字段中的固定值
        if override_fields:
            if 'SDR05' in override_fields:
                try:
                    sdr05_value = override_fields['SDR05']
                    if isinstance(sdr05_value, str):
                        result.sdr05_raw = json.loads(sdr05_value)
                    else:
                        result.sdr05_raw = sdr05_value
                    mapping_notes.append("SDR05使用固定值")
                except Exception as e:
                    logger.warning(f"解析固定SDR05失败: {e}")
            
            if 'SDR10' in override_fields:
                result.sdr10_raw = override_fields['SDR10']
                mapping_notes.append("SDR10使用固定值")
        
        # 从固定的SDR05生成其他字段
        if result.sdr05_raw:
            table_cn_mapping = await self.update_crud.generate_table_chinese_names(result.sdr05_raw)
            result.sdr06_raw = [table_cn_mapping.get(table, table) for table in result.sdr05_raw]
            mapping_notes.append(f"SDR06从固定SDR05生成: {len(result.sdr06_raw)}个表中文名")
        
        # Pipeline结果用于其他字段（如SDR12）
        parser_info = pipeline_result.get('parser_info', {})
        join_info = parser_info.get('join', '')
        if join_info:
            result.sdr12_raw = join_info
            mapping_notes.append("SDR12从Pipeline结果获取")
    
    def _sync_bdr_fields(self, result: DDCFieldMappingResult, mapping_notes: List[str]):
        """同步BDR字段（与SDR字段保持一致）"""
        # BDR09 = SDR05
        if result.sdr05_raw is not None:
            result.bdr09_raw = result.sdr05_raw
            mapping_notes.append("BDR09同步SDR05")
        
        # BDR10 = SDR06
        if result.sdr06_raw is not None:
            result.bdr10_raw = result.sdr06_raw
            mapping_notes.append("BDR10同步SDR06")
        
        # BDR11 = SDR08
        if result.sdr08_raw is not None:
            result.bdr11_raw = result.sdr08_raw
            mapping_notes.append("BDR11同步SDR08")
        
        # BDR16 = 业务逻辑描述（简化处理）
        if result.sdr05_raw or result.sdr08_raw or result.sdr10_raw:
            description_parts = []
            if result.sdr05_raw:
                description_parts.append(f"涉及表: {', '.join(result.sdr05_raw)}")
            if result.sdr08_raw:
                field_count = sum(len(fields) for fields in result.sdr08_raw.values())
                description_parts.append(f"涉及字段: {field_count}个")
            if result.sdr10_raw:
                description_parts.append("包含SQL语句")
            
            result.bdr16_raw = "; ".join(description_parts)
            mapping_notes.append("BDR16生成业务逻辑描述")
