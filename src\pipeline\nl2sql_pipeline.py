"""
完整的NL2SQL Pipeline执行器
实现从schema_generation_params到business_logic的端到端处理
"""

import logging
from typing import Dict, Any, Optional, List
from pipeline.core.context import PipelineContext
from pipeline.core.manager import PipelineBuilder
from pipeline.steps import (
    SchemaGeneratorStep,
    TableSelectorStep,
    ColumnSelectorStep,
    ColumnDeciderStep,
    KeyAssociatorStep,
    SQLGeneratorStep,
    SQLParserStep,
    BusinessLogicGeneratorStep
)

logger = logging.getLogger(__name__)


# ==================== 公共辅助函数 ====================

def _validate_common_params(user_question: str, hint: str = "") -> None:
    """验证通用参数"""
    if not user_question or not user_question.strip():
        raise ValueError("user_question不能为空")


def _create_base_context(user_question: str, hint: str = "", db_type: str = "mysql") -> PipelineContext:
    """创建基础Pipeline上下文"""
    return PipelineContext(
        user_question=user_question.strip(),
        hint=hint.strip() if hint else ""
    )


async def _execute_pipeline_with_error_handling(
    pipeline,
    context: PipelineContext,
    pipeline_name: str
) -> PipelineContext:
    """统一的Pipeline执行和错误处理"""
    try:
        logger.info(f"开始执行{pipeline_name}Pipeline")
        result_context = await pipeline.execute(context)

        # Pipeline.execute()直接返回context，不是字典
        # 从context中获取执行摘要来判断状态
        execution_summary = result_context.get("execution_summary", {})
        pipeline_success = execution_summary.get("success", True)

        if not pipeline_success:
            failed_steps = execution_summary.get("failed_steps", [])
            logger.warning(f"Pipeline执行状态: failed")
            logger.error(f"Pipeline失败步骤: {failed_steps}")
        else:
            successful_steps = execution_summary.get("successful_steps", [])
            total_steps = execution_summary.get("total_steps", 0)
            logger.info(f"Pipeline执行状态: success")
            logger.info(f"步骤执行状态: {len(successful_steps)}/{total_steps} 成功")

        # 记录执行结果
        _log_pipeline_results(result_context, pipeline_name)

        logger.info(f"{pipeline_name}Pipeline执行完成")
        return result_context

    except Exception as e:
        logger.error(f"{pipeline_name}Pipeline执行异常: {e}")
        logger.error(f"异常类型: {type(e).__name__}")

        # 尝试从context中获取部分结果
        try:
            _log_pipeline_results(context, pipeline_name)
        except:
            logger.warning("无法记录部分执行结果")

        # 重新抛出异常，但提供更多上下文信息
        raise RuntimeError(f"{pipeline_name}Pipeline执行失败: {e}") from e


# ==================== 完整Pipeline ====================

async def execute_nl2sql_pipeline(
    schema_generation_params: Dict[str, Any],
    user_question: str,
    hint: str = "",
    db_type: str = "mysql"
) -> PipelineContext:
    """
    执行完整的NL2SQL Pipeline流程
    
    Args:
        schema_generation_params: Schema生成参数，必须包含table_ids
        user_question: 用户问题
        hint: 提示信息（可选）
        db_type: 数据库类型（默认mysql）
    
    Returns:
        包含所有中间结果和最终结果的PipelineContext
    
    Pipeline流程：
    1. schema_generator (table_ids → db_schema)
    2. table_selector (db_schema → candidate_tables + schema_generation_params)
    3. schema_generator (table_names → db_schema)
    4. column_selector (db_schema → candidate_columns + schema_generation_params)
    5. schema_generator (candidate_columns → db_schema)
    6. column_decider (db_schema → refined candidate_columns + schema_generation_params)
    7. schema_generator (candidate_columns → db_schema + mappings)
    8. key_associator (candidate_column_ids → db_schema with associate_info)
    9. sql_generator (db_schema → sql_candidates)
    10. sql_parser (sql_candidates + candidate_tables → parser_info)
    11. business_logic_generator (parser_info + sql_candidates → business_logic)
    """
    
    logger.info("开始执行完整NL2SQL Pipeline")

    # 验证输入参数
    if not schema_generation_params:
        raise ValueError("schema_generation_params不能为空")

    if "table_ids" not in schema_generation_params:
        raise ValueError("schema_generation_params必须包含table_ids")

    _validate_common_params(user_question, hint)

    # 创建Pipeline上下文
    context = _create_base_context(user_question, hint, db_type)

    # 设置初始参数
    context.set("schema_generation_params", schema_generation_params)
    context.set("db_type", db_type)
    
    # 构建完整Pipeline
    builder = PipelineBuilder("complete_nl2sql")
    
    # 步骤1: 初始Schema生成（基于table_ids）
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_1",
        is_final=False
    ))
    
    # 步骤2: 表选择
    builder.add_step(TableSelectorStep())
    
    # 步骤3: 基于选择表的Schema重生成
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_2", 
        is_final=False
    ))
    
    # 步骤4: 列选择
    builder.add_step(ColumnSelectorStep())
    
    # 步骤5: 基于选择列的Schema重生成
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_3",
        is_final=False
    ))
    
    # 步骤6: 列决策（精化）
    builder.add_step(ColumnDeciderStep())
    
    # 步骤7: 最终Schema生成（输出映射信息）
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_final",
        is_final=True
    ))
    
    # 步骤8: 关联键处理
    builder.add_step(KeyAssociatorStep())
    
    # 步骤9: SQL生成
    builder.add_step(SQLGeneratorStep())
    
    # 步骤10: SQL解析
    builder.add_step(SQLParserStep())
    
    # 步骤11: 业务逻辑生成
    builder.add_step(BusinessLogicGeneratorStep())
    
    # 构建并执行Pipeline
    pipeline = builder.build()

    return await _execute_pipeline_with_error_handling(pipeline, context, "完整NL2SQL")


# ==================== 字段选择Pipeline ====================

async def execute_column_selection_pipeline(
    candidate_tables: Dict[str, List[str]],
    schema_generation_params: Dict[str, Any],
    user_question: str,
    hint: str = "",
    db_type: str = "mysql"
) -> PipelineContext:
    """
    执行字段选择Pipeline流程

    Args:
        candidate_tables: 候选表，格式：{"db_name": ["table1", "table2"]}
        schema_generation_params: Schema生成参数，必须包含table_names
        user_question: 用户问题
        hint: 提示信息（可选）
        db_type: 数据库类型（默认mysql）

    Returns:
        包含所有中间结果和最终结果的PipelineContext

    Pipeline流程（9个步骤）：
    1. schema_generator_2 (table_names → db_schema)
    2. column_selector (db_schema → candidate_columns + schema_generation_params)
    3. schema_generator_3 (candidate_columns → db_schema)
    4. column_decider (db_schema → refined candidate_columns + schema_generation_params)
    5. schema_generator_final (candidate_columns → db_schema + mappings)
    6. key_associator (candidate_column_ids → db_schema with associate_info)
    7. sql_generator (db_schema → sql_candidates)
    8. sql_parser (sql_candidates + candidate_tables → parser_info)
    9. business_logic_generator (parser_info + sql_candidates → business_logic)
    """

    logger.info("开始执行字段选择Pipeline")

    # 验证输入参数
    if not candidate_tables:
        raise ValueError("candidate_tables不能为空")

    if not schema_generation_params:
        raise ValueError("schema_generation_params不能为空")

    if "table_names" not in schema_generation_params:
        raise ValueError("schema_generation_params必须包含table_names")

    _validate_common_params(user_question, hint)

    # 创建Pipeline上下文
    context = _create_base_context(user_question, hint, db_type)

    # 设置初始参数
    context.set("candidate_tables", candidate_tables)
    context.set("schema_generation_params", schema_generation_params)
    context.set("db_type", db_type)

    # 构建字段选择Pipeline
    builder = PipelineBuilder("column_selection")

    # 步骤1: 基于选择表的Schema生成
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_2",
        is_final=False
    ))

    # 步骤2: 列选择
    builder.add_step(ColumnSelectorStep())

    # 步骤3: 基于选择列的Schema重生成
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_3",
        is_final=False
    ))

    # 步骤4: 列决策（精化）
    builder.add_step(ColumnDeciderStep())

    # 步骤5: 最终Schema生成（输出映射信息）
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_final",
        is_final=True
    ))

    # 步骤6: 关联键处理
    builder.add_step(KeyAssociatorStep())

    # 步骤7: SQL生成
    builder.add_step(SQLGeneratorStep())

    # 步骤8: SQL解析
    builder.add_step(SQLParserStep())

    # 步骤9: 业务逻辑生成
    builder.add_step(BusinessLogicGeneratorStep())

    # 构建并执行Pipeline
    pipeline = builder.build()

    return await _execute_pipeline_with_error_handling(pipeline, context, "字段选择")


# ==================== SQL生成Pipeline ====================

async def execute_sql_generation_pipeline(
    candidate_columns: Dict[str, List[str]],
    schema_generation_params: Dict[str, Any],
    user_question: str,
    hint: str = "",
    db_type: str = "mysql"
) -> PipelineContext:
    """
    执行SQL生成Pipeline流程

    Args:
        candidate_columns: 候选列，格式：{"table1": ["col1", "col2"]}
        schema_generation_params: Schema生成参数，必须包含candidate_columns且is_final=True
        user_question: 用户问题
        hint: 提示信息（可选）
        db_type: 数据库类型（默认mysql）

    Returns:
        包含所有中间结果和最终结果的PipelineContext

    Pipeline流程（5个步骤）：
    1. schema_generator_final (candidate_columns → db_schema + mappings)
    2. key_associator (candidate_column_ids → db_schema with associate_info)
    3. sql_generator (db_schema → sql_candidates)
    4. sql_parser (sql_candidates + candidate_tables → parser_info)
    5. business_logic_generator (parser_info + sql_candidates → business_logic)
    """

    logger.info("开始执行SQL生成Pipeline")

    # 验证输入参数
    if not candidate_columns:
        raise ValueError("candidate_columns不能为空")

    if not schema_generation_params:
        raise ValueError("schema_generation_params不能为空")

    if "candidate_columns" not in schema_generation_params:
        raise ValueError("schema_generation_params必须包含candidate_columns")

    if not schema_generation_params.get("is_final", False):
        raise ValueError("schema_generation_params的is_final必须为True")

    _validate_common_params(user_question, hint)

    # 创建Pipeline上下文
    context = _create_base_context(user_question, hint, db_type)

    # 设置初始参数
    context.set("candidate_columns", candidate_columns)
    context.set("schema_generation_params", schema_generation_params)
    context.set("db_type", db_type)

    # 构建SQL生成Pipeline
    builder = PipelineBuilder("sql_generation")

    # 步骤1: 最终Schema生成（输出映射信息）
    builder.add_step(SchemaGeneratorStep(
        step_name="schema_generator_final",
        is_final=True
    ))

    # 步骤2: 关联键处理
    builder.add_step(KeyAssociatorStep())

    # 步骤3: SQL生成
    builder.add_step(SQLGeneratorStep())

    # 步骤4: SQL解析
    builder.add_step(SQLParserStep())

    # 步骤5: 业务逻辑生成
    builder.add_step(BusinessLogicGeneratorStep())

    # 构建并执行Pipeline
    pipeline = builder.build()

    return await _execute_pipeline_with_error_handling(pipeline, context, "SQL生成")


# ==================== SQL解析Pipeline ====================

async def execute_sql_parsing_pipeline(
    sql_candidates: List[str],
    candidate_tables: Optional[Dict[str, List[str]]]={},
    user_question: str = "",
    hint: str = "",
    db_type: str = "mysql"
) -> PipelineContext:
    """
    执行SQL解析Pipeline流程

    Args:
        sql_candidates: SQL候选列表，格式：["SELECT ...", "SELECT ..."]
        candidate_tables: 候选表，格式：{"db_name": ["table1", "table2"]}
        user_question: 用户问题
        hint: 提示信息（可选）
        db_type: 数据库类型（默认mysql）

    Returns:
        包含所有中间结果和最终结果的PipelineContext

    Pipeline流程（2个步骤）：
    1. sql_parser (sql_candidates + candidate_tables → parser_info)
    2. business_logic_generator (parser_info + sql_candidates → business_logic)
    """

    logger.info("开始执行SQL解析Pipeline")

    # 验证输入参数
    if not sql_candidates:
        raise ValueError("sql_candidates不能为空")

    if not isinstance(sql_candidates, list):
        raise ValueError("sql_candidates必须是列表格式")

    # if not candidate_tables:
    #     raise ValueError("candidate_tables不能为空")

    # if not isinstance(candidate_tables, dict):
    #     raise ValueError("candidate_tables必须是字典格式")

    _validate_common_params(user_question, hint)

    # 创建Pipeline上下文
    context = _create_base_context(user_question, hint, db_type)

    # 设置初始参数
    context.set("sql_candidates", sql_candidates)
    context.set("candidate_tables", candidate_tables)
    context.set("db_type", db_type)

    # 构建SQL解析Pipeline
    builder = PipelineBuilder("sql_parsing")

    # 步骤1: SQL解析
    builder.add_step(SQLParserStep())

    # 步骤2: 业务逻辑生成
    builder.add_step(BusinessLogicGeneratorStep())

    # 构建并执行Pipeline
    pipeline = builder.build()

    return await _execute_pipeline_with_error_handling(pipeline, context, "SQL解析")


def _log_pipeline_results(context: PipelineContext, pipeline_name: str = "Pipeline") -> None:
    """记录Pipeline执行结果的摘要信息"""
    try:
        try:
            context = context.get('context', context)
            logger.debug("在pipeline里获取真实的context")
        except:
            pass
        # 记录主要输出
        candidate_tables = context.get("candidate_tables", {})
        candidate_columns = context.get("candidate_columns", {})
        sql_candidates = context.get("sql_candidates", [])
        parser_info = context.get("parser_info", {})
        business_logic = context.get("business_logic", {})
        candidate_column_ids = context.get("candidate_column_ids", {})
        candidate_table_ids = context.get("candidate_table_ids", {})
        logger.info(f"=== {pipeline_name}执行结果摘要 ===")
        logger.info(f"候选表: {len(candidate_tables)}个数据库，"
                   f"共{sum(len(tables) for tables in candidate_tables.values())}个表")
        logger.info(f"候选列: {len(candidate_columns)}个表，"
                   f"共{sum(len(cols) for cols in candidate_columns.values())}个列")
        logger.info(f"SQL候选: {len(sql_candidates)}个")
        logger.info(f"SQL解析: {'成功' if parser_info.get('parsing_status') == 'success' else '失败'}")
        logger.info(f"业务逻辑: {'已生成' if business_logic else '未生成'}")

        # 记录详细信息（调试级别）
        if candidate_tables:
            logger.debug(f"候选表详情: {candidate_tables}")
        if sql_candidates:
            logger.debug(f"SQL候选详情: {sql_candidates[0][:200] if sql_candidates[0] else 'Empty'}...")

        # 记录步骤执行状态 - 直接使用execution_summary中的信息
        execution_summary = context.get("execution_summary", {})

        if execution_summary:
            # 使用execution_summary中的准确信息
            total_steps = execution_summary.get("total_steps", 0)
            successful_steps = execution_summary.get("successful_steps", [])
            failed_steps = execution_summary.get("failed_steps", [])
            pipeline_name = execution_summary.get("pipeline_name", "未知Pipeline")

            logger.info(f"步骤执行状态: {len(successful_steps)}/{total_steps} 成功 ({pipeline_name})")

            if failed_steps:
                logger.warning(f"失败的步骤: {', '.join(failed_steps)}")
        else:
            # 降级方案：使用context的方法
            successful_steps = context.get_successful_steps()
            failed_steps = context.get_failed_steps()
            total_steps = len(successful_steps) + len(failed_steps)

            logger.info(f"步骤执行状态: {len(successful_steps)}/{total_steps} 成功")

            if failed_steps:
                logger.warning(f"失败的步骤: {', '.join(failed_steps)}")

    except Exception as e:
        logger.warning(f"记录Pipeline结果失败: {e}")


class CompletePipelineExecutor:
    """
    完整Pipeline执行器类
    提供更高级的接口和配置选项，支持所有Pipeline变体
    """

    def __init__(self, default_db_type: str = "mysql"):
        """
        初始化执行器

        Args:
            default_db_type: 默认数据库类型
        """
        self.default_db_type = default_db_type
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def _extract_main_results(self, context: PipelineContext) -> Dict[str, Any]:
        """提取主要结果的通用方法"""

        if context.get('context', None):
            context = context.get('context')
            logger.debug(f"在pipeline里获取真实的context:{context}")
            
        return {
            "candidate_tables": context.get("candidate_tables", {}),
            "candidate_columns": context.get("candidate_columns", {}),
            "candidate_table_ids": context.get("candidate_table_ids", {}),
            "candidate_column_ids": context.get("candidate_column_ids", {}),
            "db_schema": context.get("db_schema", ""),
            "sql_candidates": context.get("sql_candidates", []),
            "parser_info": context.get("parser_info", {}),
            "business_logic": context.get("business_logic", {}),
            "associate_keys": context.get("associate_keys", {}),
            "associate_info": context.get("associate_info", "")
        }

    # ==================== 完整Pipeline ====================

    async def execute(
        self,
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行完整Pipeline并返回主要结果

        Args:
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            包含主要结果的字典
        """
        # 使用默认数据库类型
        if db_type is None:
            db_type = self.default_db_type

        # 执行Pipeline
        context = await execute_nl2sql_pipeline(
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

        return self._extract_main_results(context)

    async def execute_with_context(
        self,
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> PipelineContext:
        """
        执行完整Pipeline并返回完整上下文

        Args:
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            完整的PipelineContext
        """
        # 使用默认数据库类型
        if db_type is None:
            db_type = self.default_db_type

        return await execute_nl2sql_pipeline(
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

    # ==================== 字段选择Pipeline ====================

    async def execute_column_selection(
        self,
        candidate_tables: Dict[str, List[str]],
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行字段选择Pipeline并返回主要结果

        Args:
            candidate_tables: 候选表
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            包含主要结果的字典
        """
        if db_type is None:
            db_type = self.default_db_type

        context = await execute_column_selection_pipeline(
            candidate_tables=candidate_tables,
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

        return self._extract_main_results(context)

    async def execute_column_selection_with_context(
        self,
        candidate_tables: Dict[str, List[str]],
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> PipelineContext:
        """
        执行字段选择Pipeline并返回完整上下文

        Args:
            candidate_tables: 候选表
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            完整的PipelineContext
        """
        if db_type is None:
            db_type = self.default_db_type

        return await execute_column_selection_pipeline(
            candidate_tables=candidate_tables,
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

    # ==================== SQL生成Pipeline ====================

    async def execute_sql_generation(
        self,
        candidate_columns: Dict[str, List[str]],
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行SQL生成Pipeline并返回主要结果

        Args:
            candidate_columns: 候选列
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            包含主要结果的字典
        """
        if db_type is None:
            db_type = self.default_db_type

        context = await execute_sql_generation_pipeline(
            candidate_columns=candidate_columns,
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

        return self._extract_main_results(context)

    async def execute_sql_generation_with_context(
        self,
        candidate_columns: Dict[str, List[str]],
        schema_generation_params: Dict[str, Any],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> PipelineContext:
        """
        执行SQL生成Pipeline并返回完整上下文

        Args:
            candidate_columns: 候选列
            schema_generation_params: Schema生成参数
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            完整的PipelineContext
        """
        if db_type is None:
            db_type = self.default_db_type

        return await execute_sql_generation_pipeline(
            candidate_columns=candidate_columns,
            schema_generation_params=schema_generation_params,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

    # ==================== SQL解析Pipeline ====================

    async def execute_sql_parsing(
        self,
        sql_candidates: List[str],
        candidate_tables: Optional[Dict[str, List[str]]]={},
        user_question: str = "",
        hint: str = "",
        db_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行SQL解析Pipeline并返回主要结果

        Args:
            sql_candidates: SQL候选列表
            candidate_tables: 候选表
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            包含主要结果的字典
        """
        if db_type is None:
            db_type = self.default_db_type

        context = await execute_sql_parsing_pipeline(
            sql_candidates=sql_candidates,
            candidate_tables=candidate_tables,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )

        return self._extract_main_results(context)

    async def execute_sql_parsing_with_context(
        self,
        sql_candidates: List[str],
        candidate_tables: Dict[str, List[str]],
        user_question: str,
        hint: str = "",
        db_type: Optional[str] = None
    ) -> PipelineContext:
        """
        执行SQL解析Pipeline并返回完整上下文

        Args:
            sql_candidates: SQL候选列表
            candidate_tables: 候选表
            user_question: 用户问题
            hint: 提示信息
            db_type: 数据库类型（可选，使用默认值）

        Returns:
            完整的PipelineContext
        """
        if db_type is None:
            db_type = self.default_db_type

        return await execute_sql_parsing_pipeline(
            sql_candidates=sql_candidates,
            candidate_tables=candidate_tables,
            user_question=user_question,
            hint=hint,
            db_type=db_type
        )
