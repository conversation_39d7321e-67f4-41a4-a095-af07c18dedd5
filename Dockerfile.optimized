# BaseImage
FROM python:3.12-slim

# Environment variables
ENV SERVER_PORT=30351 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv for faster package management
RUN pip install uv

# Set working directory
WORKDIR /app

# Use China mirror for pip (if needed)
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# Copy only dependency files first (for better caching)
COPY pyproject.toml uv.lock ./

# Install dependencies using uv (much faster than pip)
RUN uv pip install --system -e .

# Copy the rest of the project files
COPY . .

# Expose port
EXPOSE ${SERVER_PORT}

# Use shell form for better environment variable handling
SHELL ["/bin/bash", "-c"]

# Entry point with environment variable support
ENTRYPOINT ["python", "-u", "src/api/main.py"]
