-- ==========================================
-- DD数据需求管理系统 - 完整版（列存储架构 + 业务表系统）
-- ==========================================
--
-- 说明：
-- 1. 采用列存储架构，每个字段编码作为表的列名
-- 2. 保留dd_fields_metadata表作为字段定义参考
-- 3. dd_submission_data表：主数据表，每行代表一条DD数据记录
-- 4. 业务表系统：支持分发前后的业务流程管理
-- 5. 字段命名采用小写编码：dr01, dr02, bdr01等
-- 6. 高性能设计，避免键值对存储的性能问题
--
-- 表结构：
-- - dd_fields_metadata: 字段元数据（数据字典）
-- - dd_submission_data: 主数据表（列存储）
-- - dd_departments: 部门职责表
-- - biz_dd_pre_distribution: 分发前表（增加report_type、set字段）
-- - biz_dd_post_distribution: 分发后表（增加report_type、set字段）
-- - dd_report_data: 报表数据表（具体字段名，增加report_type、set字段）
--
-- 创建时间：2025-07-14
-- ==========================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. DD字段元数据表 (DD Fields Metadata) - 保留作为字段定义参考
-- 描述: 定义DD表格的所有字段元信息，作为数据字典使用
-- ==========================================
DROP TABLE IF EXISTS `dd_fields_metadata`;
CREATE TABLE `dd_fields_metadata` (
  `field_id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `field_code` varchar(50) NOT NULL COMMENT '字段编码，如DR01、BDR01等',
  `field_name` varchar(255) NOT NULL COMMENT '字段名称（英文）',
  `field_name_cn` varchar(255) NOT NULL COMMENT '字段名称（中文）',
  `field_desc` text COMMENT '字段详细描述',
  `category_level` char(1) NOT NULL COMMENT '分类层级：A-结果数据需求, B-业务解读, C-IT解读, D-指标解读',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `sub_category` varchar(100) DEFAULT NULL COMMENT '子分类，如A1管理信息、A2表字段等',
  `process_type` varchar(50) DEFAULT NULL COMMENT '流程类型：义务报送-DR22/BDR04相关，其他为A/B/C/D分类',
  `data_type` varchar(50) NOT NULL DEFAULT 'TEXT' COMMENT '数据类型：TEXT, NUMBER, DATE, ENUM等',
  `max_length` int DEFAULT NULL COMMENT '最大长度限制',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `validation_rules` json DEFAULT NULL COMMENT '校验规则，JSON格式',
  `enum_values` json DEFAULT NULL COMMENT '枚举值列表，JSON格式（当data_type为ENUM时使用）',
  `field_order` int NOT NULL DEFAULT '0' COMMENT '字段排序，用于模板生成时的列顺序',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要向量化：1-是，0-否',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `uk_field_code` (`field_code`),
  KEY `idx_category_level` (`category_level`),
  KEY `idx_sub_category` (`sub_category`),
  KEY `idx_process_type` (`process_type`),
  KEY `idx_field_order` (`field_order`),
  KEY `idx_is_vectorized` (`is_vectorized`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD字段元数据表 (字段定义参考，数据字典)';

-- ==========================================
-- 2. DD填报数据表 (DD Submission Data) - 列存储架构的主数据表
-- 描述: 每行代表一条DD数据记录，每列对应一个字段编码
-- ==========================================
DROP TABLE IF EXISTS `dd_submission_data`;
CREATE TABLE `dd_submission_data` (
  -- 基础管理字段
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据记录ID，主键自增',
  `submission_id` varchar(50) NOT NULL COMMENT '填报项ID',
  `report_data_id` bigint DEFAULT NULL COMMENT '关联dd_report_data表的主键ID',
  `version` varchar(100) NOT NULL COMMENT '版本标签，如2025-07-14',
  `type` varchar(50) NOT NULL COMMENT '数据类型：RANGE-范围（整表口径），SUBMISSION-填报项',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- A类字段：结果数据需求 (DR01-DR22)
  `dr01` varchar(50) DEFAULT NULL COMMENT 'A-dr01-产出结果数据所在的数据层',
  `dr02` varchar(200) DEFAULT NULL COMMENT 'A-dr02-需求文件名称',
  `dr03` varchar(150) DEFAULT NULL COMMENT 'A-dr03-需求主题域',
  `dr04` varchar(100) DEFAULT NULL COMMENT 'A-dr04-需求主题域ID',
  `dr05` varchar(200) DEFAULT NULL COMMENT 'A-dr05-系统文件表',
  `dr06` varchar(200) DEFAULT NULL COMMENT 'A-dr06-表名',
  `dr07` varchar(100) DEFAULT NULL COMMENT 'A-dr07-表名ID',
  `dr08` varchar(50) DEFAULT '不适用' COMMENT 'A-dr08-报送模式',
  `dr09` varchar(200) DEFAULT '不适用' COMMENT 'A-dr09-数据项名称',
  `dr10` varchar(100) DEFAULT '不适用' COMMENT 'A-dr10-数据项名称ID',
  `dr11` varchar(200) DEFAULT NULL COMMENT 'A-dr11-系统文件表字段',
  `dr12` varchar(50) DEFAULT NULL COMMENT 'A-dr12-系统文件表字段主键/外键标识位',
  `dr13` varchar(150) DEFAULT NULL COMMENT 'A-dr13-数据元',
  `dr14` varchar(100) DEFAULT NULL COMMENT 'A-dr14-数据元代码',
  `dr15` text DEFAULT NULL COMMENT 'A-dr15-数据元说明',
  `dr16` text DEFAULT NULL COMMENT 'A-dr16-格式要求',
  `dr17` text DEFAULT NULL COMMENT 'A-dr17-需求口径/填写规则',
  `dr18` text DEFAULT NULL COMMENT 'A-dr18-码表',
  `dr19` varchar(50) DEFAULT '不适用' COMMENT 'A-dr19-报送频率',
  `dr20` text DEFAULT NULL COMMENT 'A-dr20-数据质量检核规则',
  `dr21` text DEFAULT NULL COMMENT 'A-dr21-数据脱敏要求',
  `dr22` varchar(200) DEFAULT NULL COMMENT 'A-dr22-RRMS数据提供部门',

  -- B类字段：业务解读数据需求 (BDR01-BDR18)
  `bdr01` varchar(200) DEFAULT NULL COMMENT 'B-bdr01-数据生产/处理部门',
  `bdr02` varchar(100) DEFAULT NULL COMMENT 'B-bdr02-数据生成/处理部门代表',
  `bdr03` varchar(200) DEFAULT NULL COMMENT 'B-bdr03-数据管理部门',
  `bdr04` varchar(100) DEFAULT NULL COMMENT 'B-bdr04-数据管理部门代表',
  `bdr05` text DEFAULT NULL COMMENT 'B-bdr05-业务流程',
  `bdr06` varchar(150) DEFAULT NULL COMMENT 'B-bdr06-数据主题',
  `bdr07` varchar(150) DEFAULT '1' COMMENT 'B-bdr07-产品/服务',
  `bdr08` varchar(50) DEFAULT '不适用' COMMENT 'B-bdr08-数据来源类型',
  `bdr09` varchar(200) DEFAULT NULL COMMENT 'B-bdr09-源系统名称',
  `bdr10` text DEFAULT NULL COMMENT 'B-bdr10-源系统对应功能界面',
  `bdr11` text DEFAULT NULL COMMENT 'B-bdr11-源系统对应功能界面描述',
  `bdr12` varchar(200) DEFAULT NULL COMMENT 'B-bdr12-源系统对应字段',
  `bdr13` varchar(200) DEFAULT NULL COMMENT 'B-bdr13-手工上传数据维护部门',
  `bdr14` varchar(200) DEFAULT NULL COMMENT 'B-bdr14-手工上传表名称',
  `bdr15` varchar(200) DEFAULT NULL COMMENT 'B-bdr15-外部数据来源名称',
  `bdr16` varchar(200) DEFAULT NULL COMMENT 'B-bdr16-外部数据项',
  `bdr17` text DEFAULT NULL COMMENT 'B-bdr17-业务数据匹配逻辑',
  `bdr18` text DEFAULT NULL COMMENT 'B-bdr18-码表映射信息',

  -- C类字段：IT解读业务数据需求 (SDR01-SDR15)
  `sdr01` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr01-源头数据所在的数据层',
  -- `sdr02` varchar(100) DEFAULT NULL COMMENT 'C-sdr02-源系统唯一标识码', -- 暂时跳过实现
  `sdr03` varchar(200) DEFAULT NULL COMMENT 'C-sdr03-源系统名称',
  `sdr03_5` varchar(100) DEFAULT NULL COMMENT 'C-sdr03_5-源系统名称ID',
  `sdr04` varchar(100) DEFAULT NULL COMMENT 'C-sdr04-源系统数据需求分析代表',
  `sdr05` varchar(200) DEFAULT NULL COMMENT 'C-sdr05-源系统文件名',
  `sdr06` text DEFAULT NULL COMMENT 'C-sdr06-源系统文件名描述',
  `sdr07` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr07-表更新模式',
  `sdr08` varchar(200) DEFAULT NULL COMMENT 'C-sdr08-源系统对应字段',
  `sdr08_5` varchar(100) DEFAULT NULL COMMENT 'C-sdr08_5-源系统对应字段ID',
  `sdr09` text DEFAULT NULL COMMENT 'C-sdr09-源系统对应字段描述',
  `sdr10` text DEFAULT NULL COMMENT 'C-sdr10-数据加工逻辑',
  `sdr11` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr11-可信数据源标识',
  `sdr12` text DEFAULT NULL COMMENT 'C-sdr12-用于生成此数据的其他关联数据项',
  `sdr13` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr13-数据类型',
  `sdr14` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr14-数据接入频率',
  `sdr15` text DEFAULT NULL COMMENT 'C-sdr15-备注',

  -- D类字段：指标解读技术口径 (IDR01-IDR05)
  `idr01` varchar(150) DEFAULT NULL COMMENT 'D-idr01-数据模型',
  `idr02` varchar(100) DEFAULT NULL COMMENT 'D-idr02-数据模型ID',
  `idr03` text DEFAULT NULL COMMENT 'D-idr03-原子指标逻辑',
  `idr04` text DEFAULT NULL COMMENT 'D-idr04-计算指标逻辑',
  `idr05` text DEFAULT NULL COMMENT 'D-idr05-备注',

  PRIMARY KEY (`id`),
  KEY `idx_submission_id` (`submission_id`),
  KEY `idx_report_data_id` (`report_data_id`),
  KEY `idx_version` (`version`),
  KEY `idx_type` (`type`),
  KEY `idx_version_type` (`version`, `type`),
  -- 重点索引：最常查询的字段
  KEY `idx_dr09` (`dr09`),  -- 数据项名称，最常查询（varchar(200)无需前缀）
  KEY `idx_dr17` (`dr17`(255)),  -- 需求口径/填写规则，最常查询（text需要前缀）
  -- 枚举类型字段索引（仅保留dr01为枚举）
  KEY `idx_dr01` (`dr01`),      -- 产出结果数据所在的数据层（枚举）
  -- 时间索引
  KEY `idx_create_time` (`create_time`),

  CONSTRAINT `fk_submission_data_report` FOREIGN KEY (`report_data_id`) REFERENCES `dd_report_data` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD填报数据表 (列存储架构，高性能主数据表)';

-- ==========================================
-- 3. DD业务表系统 - 分发前后管理
-- ==========================================

-- ==========================================
-- 3.1 部门职责表 (Department Responsibilities)
-- 描述: 管理各部门的职责和类型，支持按部门分工的填报流程
-- ==========================================
DROP TABLE IF EXISTS `dd_departments`;
CREATE TABLE `dd_departments` (
  `dept_id` varchar(50) NOT NULL COMMENT '部门ID，业务唯一标识',
  `dept_name` varchar(200) NOT NULL COMMENT '部门名称',
  `dept_desc` text DEFAULT NULL COMMENT '部门职责描述',
  `dept_type` varchar(50) NOT NULL DEFAULT 'normal' COMMENT '部门类型：normal-普通部门，mandatory-义务报送部门，management-管理部门',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`dept_id`),
  KEY `idx_dept_name` (`dept_name`),
  KEY `idx_dept_type` (`dept_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门职责表 (支持按部门分工的业务流程)';

-- ==========================================
-- 3.2 部门关联关系表 (Department Relations)
-- 描述: 管理部门与数据表的关联关系，一对多关系
-- ==========================================
DROP TABLE IF EXISTS `dd_departments_relation`;
CREATE TABLE `dd_departments_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `dept_id` varchar(50) NOT NULL COMMENT '部门ID，关联dd_departments.dept_id',
  `table_id` bigint NOT NULL COMMENT '表ID，关联相关数据表',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_table` (`dept_id`, `table_id`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_table_id` (`table_id`),
  CONSTRAINT `fk_dd_dept_relation_dept` FOREIGN KEY (`dept_id`) REFERENCES `dd_departments` (`dept_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门关联关系表';



-- ==========================================
-- 3.3 DD分发前表 (DD Pre-Distribution)
-- 描述: 存储分发前的DD-A阶段数据（DR01-DR21），作为分发基础
-- ==========================================
DROP TABLE IF EXISTS `biz_dd_pre_distribution`;
CREATE TABLE `biz_dd_pre_distribution` (
  -- 主键
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',

  -- 业务关联字段
  `submission_id` varchar(50) NOT NULL COMMENT '填报项ID，业务唯一标识，关联业务流程',
  `submission_type` varchar(50) NOT NULL COMMENT '填报类型：RANGE-范围（整表口径），SUBMISSION-填报项',
  `report_type` varchar(50) NOT NULL COMMENT '报表类型：detail-明细，index-指标',
  `set` varchar(50) DEFAULT NULL COMMENT '套系信息',
  `version` varchar(100) NOT NULL COMMENT '版本标签',

  -- 业务管理字段
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- DD-A类字段：结果数据需求 (DR01-DR21，不包括DR22)
  `dr01` varchar(50) DEFAULT NULL COMMENT 'A-dr01-产出结果数据所在的数据层',
  `dr02` varchar(200) DEFAULT NULL COMMENT 'A-dr02-需求文件名称',
  `dr03` varchar(150) DEFAULT NULL COMMENT 'A-dr03-需求主题域',
  `dr04` varchar(100) DEFAULT NULL COMMENT 'A-dr04-需求主题域ID',
  `dr05` varchar(200) DEFAULT NULL COMMENT 'A-dr05-系统文件表',
  `dr06` varchar(200) DEFAULT NULL COMMENT 'A-dr06-表名',
  `dr07` varchar(100) DEFAULT NULL COMMENT 'A-dr07-表名ID',
  `dr08` varchar(50) DEFAULT '不适用' COMMENT 'A-dr08-报送模式',
  `dr09` varchar(200) DEFAULT '不适用' COMMENT 'A-dr09-数据项名称',
  `dr10` varchar(100) DEFAULT '不适用' COMMENT 'A-dr10-数据项名称ID',
  `dr11` varchar(200) DEFAULT NULL COMMENT 'A-dr11-系统文件表字段',
  `dr12` varchar(50) DEFAULT NULL COMMENT 'A-dr12-系统文件表字段主键/外键标识位',
  `dr13` varchar(150) DEFAULT NULL COMMENT 'A-dr13-数据元',
  `dr14` varchar(100) DEFAULT NULL COMMENT 'A-dr14-数据元代码',
  `dr15` text DEFAULT NULL COMMENT 'A-dr15-数据元说明',
  `dr16` text DEFAULT NULL COMMENT 'A-dr16-格式要求',
  `dr17` text DEFAULT NULL COMMENT 'A-dr17-需求口径/填写规则',
  `dr18` text DEFAULT NULL COMMENT 'A-dr18-码表',
  `dr19` varchar(50) DEFAULT '不适用' COMMENT 'A-dr19-报送频率',
  `dr20` text DEFAULT NULL COMMENT 'A-dr20-数据质量检核规则',
  `dr21` text DEFAULT NULL COMMENT 'A-dr21-数据脱敏要求',

  PRIMARY KEY (`id`),
  KEY `idx_submission_id` (`submission_id`),
  UNIQUE KEY `uk_pre_distribution_submission_version` (`submission_id`, `version`),
  KEY `idx_submission_type` (`submission_type`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_set` (`set`),
  KEY `idx_version` (`version`),
  KEY `idx_dr01` (`dr01`),
  KEY `idx_dr07` (`dr07`),
  KEY `idx_dr09` (`dr09`),
  KEY `idx_dr17` (`dr17`(255)),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD分发前表 (DR01-DR21，分发基础数据)';

-- ==========================================
-- 3.4 DD分发后表 (DD Post-Distribution)
-- 描述: 存储分发后各部门的DD-B/C/D阶段数据（DR22及后续），按部门分工管理
-- ==========================================
DROP TABLE IF EXISTS `biz_dd_post_distribution`;
CREATE TABLE `biz_dd_post_distribution` (
  -- 主键
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',

  -- 业务关联字段
  `pre_distribution_id` bigint NOT NULL COMMENT '关联分发前表的主键ID',
  `submission_id` varchar(50) NOT NULL COMMENT '填报项ID，业务唯一标识，关联业务流程',
  `submission_type` varchar(50) NOT NULL COMMENT '填报类型：RANGE-范围（整表口径），SUBMISSION-填报项',
  `report_type` varchar(50) NOT NULL COMMENT '报表类型：detail-明细，index-指标',
  `set` varchar(50) DEFAULT NULL COMMENT '套系信息',
  `version` varchar(100) NOT NULL COMMENT '版本标签',

  -- 部门管理
  `dept_id` varchar(50) NOT NULL COMMENT '负责部门ID，关联部门职责表',

  -- 业务管理字段
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 数据层
  `dr01` varchar(50) DEFAULT NULL COMMENT 'A-dr01-产出结果数据所在的数据层',
  -- DR07字段（报送项ID）
  `dr07` varchar(100) DEFAULT NULL COMMENT 'A-dr07-表名ID（报送项ID）',

  -- DD-A类字段：DR22（义务报送相关）
  `dr22` varchar(200) DEFAULT NULL COMMENT 'A-dr22-RRMS数据提供部门',

  -- DD-B类字段：业务解读数据需求 (BDR01-BDR18)
  `bdr01` varchar(200) DEFAULT NULL COMMENT 'B-bdr01-数据生产/处理部门',
  `bdr02` varchar(100) DEFAULT NULL COMMENT 'B-bdr02-数据生成/处理部门代表',
  `bdr03` varchar(200) DEFAULT NULL COMMENT 'B-bdr03-数据管理部门',
  `bdr04` varchar(100) DEFAULT NULL COMMENT 'B-bdr04-数据管理部门代表',
  `bdr05` text DEFAULT NULL COMMENT 'B-bdr05-业务流程',
  `bdr06` varchar(150) DEFAULT NULL COMMENT 'B-bdr06-数据主题',
  `bdr07` varchar(150) DEFAULT '1' COMMENT 'B-bdr07-产品/服务',
  `bdr08` varchar(50) DEFAULT '不适用' COMMENT 'B-bdr08-数据来源类型',
  `bdr09` varchar(200) DEFAULT NULL COMMENT 'B-bdr09-源系统名称',
  `bdr10` text DEFAULT NULL COMMENT 'B-bdr10-源系统对应功能界面',
  `bdr11` text DEFAULT NULL COMMENT 'B-bdr11-源系统对应功能界面描述',
  `bdr12` varchar(200) DEFAULT NULL COMMENT 'B-bdr12-源系统对应字段',
  `bdr13` varchar(200) DEFAULT NULL COMMENT 'B-bdr13-手工上传数据维护部门',
  `bdr14` varchar(200) DEFAULT NULL COMMENT 'B-bdr14-手工上传表名称',
  `bdr15` varchar(200) DEFAULT NULL COMMENT 'B-bdr15-外部数据来源名称',
  `bdr16` varchar(200) DEFAULT NULL COMMENT 'B-bdr16-外部数据项',
  `bdr17` text DEFAULT NULL COMMENT 'B-bdr17-业务数据匹配逻辑',
  `bdr18` text DEFAULT NULL COMMENT 'B-bdr18-码表映射信息',

  -- DD-C类字段：IT解读业务数据需求 (SDR01-SDR15)
  `sdr01` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr01-源头数据所在的数据层',
  -- `sdr02` varchar(100) DEFAULT NULL COMMENT 'C-sdr02-源系统唯一标识码', -- 暂时跳过实现
  `sdr03` varchar(200) DEFAULT NULL COMMENT 'C-sdr03-源系统名称',
  `sdr03_5` varchar(100) DEFAULT NULL COMMENT 'C-sdr03_5-源系统名称ID',
  `sdr04` varchar(100) DEFAULT NULL COMMENT 'C-sdr04-源系统数据需求分析代表',
  `sdr05` varchar(200) DEFAULT NULL COMMENT 'C-sdr05-源系统文件名',
  `sdr06` text DEFAULT NULL COMMENT 'C-sdr06-源系统文件名描述',
  `sdr07` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr07-表更新模式',
  `sdr08` varchar(200) DEFAULT NULL COMMENT 'C-sdr08-源系统对应字段',
  `sdr08_5` varchar(100) DEFAULT NULL COMMENT 'C-sdr08_5-源系统对应字段ID',
  `sdr09` text DEFAULT NULL COMMENT 'C-sdr09-源系统对应字段描述',
  `sdr10` text DEFAULT NULL COMMENT 'C-sdr10-数据加工逻辑',
  `sdr11` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr11-可信数据源标识',
  `sdr12` text DEFAULT NULL COMMENT 'C-sdr12-用于生成此数据的其他关联数据项',
  `sdr13` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr13-数据类型',
  `sdr14` varchar(50) DEFAULT '不适用' COMMENT 'C-sdr14-数据接入频率',
  `sdr15` text DEFAULT NULL COMMENT 'C-sdr15-备注',

  -- DD-D类字段：指标解读技术口径 (IDR01-IDR05)
  `idr01` varchar(150) DEFAULT NULL COMMENT 'D-idr01-数据模型',
  `idr02` varchar(100) DEFAULT NULL COMMENT 'D-idr02-数据模型ID',
  `idr03` text DEFAULT NULL COMMENT 'D-idr03-原子指标逻辑',
  `idr04` text DEFAULT NULL COMMENT 'D-idr04-计算指标逻辑',
  `idr05` text DEFAULT NULL COMMENT 'D-idr05-备注',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_distribution_submission_version_dept` (`submission_id`, `version`, `dept_id`),
  KEY `idx_pre_distribution_id` (`pre_distribution_id`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_submission_id` (`submission_id`),
  KEY `idx_submission_type` (`submission_type`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_set` (`set`),
  KEY `idx_version` (`version`),
  KEY `idx_dr01` (`dr01`),
  KEY `idx_dr07` (`dr07`),
  KEY `idx_create_time` (`create_time`),

  CONSTRAINT `fk_post_distribution_pre` FOREIGN KEY (`pre_distribution_id`) REFERENCES `biz_dd_pre_distribution` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_post_distribution_dept` FOREIGN KEY (`dept_id`) REFERENCES `dd_departments` (`dept_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD分发后表 (DR22及后续，按部门分工)';

-- ==========================================
-- 4. DD报表数据表 (DD Report Data) - 上层表
-- 描述: 存储报表相关信息，使用具体字段名，dd_submission_data关联到此表
-- ==========================================
DROP TABLE IF EXISTS `dd_report_data`;
CREATE TABLE `dd_report_data` (
  -- 主键
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',

  -- 知识库关联
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID，用于隔离不同知识库的数据',

  -- 版本管理
  `version` varchar(100) NOT NULL COMMENT '版本标签',

  -- 报表核心信息（具体字段名）
  `report_name` varchar(255) NOT NULL COMMENT '报表名称',
  `report_code` varchar(100) NOT NULL COMMENT '报表代码',
  `report_layer` varchar(50) NOT NULL COMMENT '报表数据层',
  `report_department` varchar(200) DEFAULT NULL COMMENT '报表负责部门',
  `report_type` enum('detail','index') NOT NULL COMMENT '报表类型：detail-明细，index-指标',
  `set` varchar(50) DEFAULT NULL COMMENT '套系信息',
  `is_manual` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否手工报送: 1-是, 0-否',

  -- 业务管理字段
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_report_code_version` (`knowledge_id`(100), `report_code`, `version`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_version` (`version`),
  KEY `idx_report_code` (`report_code`),
  KEY `idx_report_layer` (`report_layer`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_set` (`set`),
  KEY `idx_is_manual` (`is_manual`),
  KEY `idx_create_time` (`create_time`),

  CONSTRAINT `fk_dd_report_data_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD报表数据表 (上层表，具体字段名)';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ==========================================
-- 第二部分：数据初始化
-- ==========================================

-- 清空现有数据（如果需要重新初始化）
-- DELETE FROM dd_fields_metadata;
-- DELETE FROM dd_departments;

-- ==========================================
-- 1. 部门职责表初始化数据
-- ==========================================
-- INSERT INTO dd_departments (dept_id, dept_name, dept_desc, dept_type) VALUES
-- ('DEPT_RRMS', 'RRMS数据提供部门', '负责监管报送数据的提供和管理', 'mandatory'),
-- ('DEPT_BUSINESS', '业务部门', '负责业务数据的生产和处理', 'normal'),
-- ('DEPT_IT', 'IT部门', '负责系统数据的技术实现和维护', 'normal'),
-- ('DEPT_DATA_MGT', '数据管理部门', '负责数据治理和指标管理', 'management'),
-- ('DEPT_COMPLIANCE', '合规部门', '负责义务报送的合规管理', 'mandatory');

-- ==========================================
-- 2. 部门关联表初始化数据
-- ==========================================

-- -- 2.1 部门关联关系初始化
-- -- 注意：这里使用数字ID，实际部署时需要根据相关数据表的实际数据进行调整
-- INSERT INTO dd_departments_relation (dept_id, table_id) VALUES
-- -- RRMS部门关联的数据表
-- ('DEPT_RRMS', 1),
-- ('DEPT_RRMS', 2),
-- ('DEPT_RRMS', 3),

-- -- 业务部门关联的数据表
-- ('DEPT_BUSINESS', 4),
-- ('DEPT_BUSINESS', 5),
-- ('DEPT_BUSINESS', 6),

-- -- IT部门关联的数据表
-- ('DEPT_IT', 7),
-- ('DEPT_IT', 8),

-- -- 数据管理部门关联的数据表
-- ('DEPT_DATA_MGT', 9),
-- ('DEPT_DATA_MGT', 10),

-- -- 合规部门关联的数据表
-- ('DEPT_COMPLIANCE', 11),
-- ('DEPT_COMPLIANCE', 12);



-- -- ==========================================
-- -- 2. DD字段元数据初始化 - 作为数据字典使用
-- -- ==========================================

-- -- A类字段：结果数据需求 (DR01-DR22)
-- INSERT INTO dd_fields_metadata (
--     field_code, field_name, field_name_cn, field_desc,
--     category_level, category_name, sub_category, process_type,
--     data_type, is_vectorized, field_order
-- ) VALUES
-- -- A1.管理信息
-- ('DR01', 'Target Data Layer', '产出结果数据所在的数据层', 'ADS/BDM/IDM/ADM等', 'A', '结果数据需求', 'A1.管理信息', 'A', 'ENUM', 0, 1),

-- -- A2.表-字段
-- ('DR02', 'Requirement Document Name', '需求文件名称', '需求文档的名称', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 2),
-- ('DR03', 'Requirement Domain', '需求主题域', '需求所属的主题域', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 3),
-- ('DR04', 'Requirement Domain ID', '需求主题域ID', '需求主题域的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 4),
-- ('DR05', 'System Table Name', '系统文件表', '系统中的表名', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 5),
-- ('DR06', 'Requirement Table Name', '表名', '需求中的表名', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 6),
-- ('DR07', 'Requirement Table Name ID', '表名ID', '表名的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 7),
-- ('DR08', 'Reporting Model', '报送模式', '全量/增量/变量', 'A', '结果数据需求', 'A2.表-字段', 'A', 'ENUM', 0, 8),
-- ('DR09', 'Requirement Data Field in Chinese', '数据项名称', '数据字段的中文名称', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 1, 9),
-- ('DR10', 'Requirement Data Field ID', '数据项名称ID', '数据项的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 10),
-- ('DR11', 'System Table Field Name', '系统文件表字段', '系统表中的字段名', 'A', '结果数据需求', 'A2.表-字段', 'A', 'TEXT', 0, 11),
-- ('DR12', 'System Table Field is Primary Key or Foreign Key Indicator', '系统文件表字段主键/外键标识位', '标识字段是否为主键或外键', 'A', '结果数据需求', 'A2.表-字段', 'A', 'ENUM', 0, 12),

-- -- A3.领域数据标准
-- ('DR13', 'Data Element', '数据元', '数据元定义', 'A', '结果数据需求', 'A3.领域数据标准', 'A', 'TEXT', 0, 13),
-- ('DR14', 'Data Element Serial No.', '数据元代码', '数据元的编码', 'A', '结果数据需求', 'A3.领域数据标准', 'A', 'TEXT', 0, 14),
-- ('DR15', 'Data Element Description', '数据元说明', '数据元的详细说明', 'A', '结果数据需求', 'A3.领域数据标准', 'A', 'TEXT', 0, 15),

-- -- A4.表-字段
-- ('DR16', 'Data Format', '格式要求', '需求要求的数据格式', 'A', '结果数据需求', 'A4.表-字段', 'A', 'TEXT', 0, 16),
-- ('DR17', 'Requirement caliber/filling rules', '需求口径/填写规则', '数据填写的规则和口径', 'A', '结果数据需求', 'A4.表-字段', 'A', 'TEXT', 1, 17),
-- ('DR18', 'Reference Code', '码表', '含版本号的码表信息', 'A', '结果数据需求', 'A4.表-字段', 'A', 'TEXT', 0, 18),
-- ('DR19', 'Reporting Frequency', '报送频率', '日报/周报/月报/季报/半年报/年报/Adhoc', 'A', '结果数据需求', 'A4.表-字段', 'A', 'ENUM', 0, 19),
-- ('DR20', 'Data Quality Rules', '数据质量检核规则', '需求要求的数据质量规则', 'A', '结果数据需求', 'A4.表-字段', 'A', 'TEXT', 0, 20),
-- ('DR21', 'Data desensitization requirements', '数据脱敏要求', '数据脱敏的具体要求', 'A', '结果数据需求', 'A4.表-字段', 'A', 'TEXT', 0, 21),
-- ('DR22', 'IRR TOM/RRMS Data Producer', 'RRMS数据提供部门', '数据提供的部门信息，义务报送相关', 'A', '结果数据需求', 'A4.表-字段', '义务报送', 'TEXT', 0, 22);

-- -- B类字段：业务解读数据需求 (BDR01-BDR18)
-- INSERT INTO dd_fields_metadata (field_code, field_name, field_name_cn, field_desc, category_level, category_name, sub_category, process_type, data_type, is_vectorized, field_order) VALUES
-- ('BDR01', 'Data Producing/Processing Department', '数据生产/处理部门', '负责数据生产和处理的部门', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 23),
-- ('BDR02', 'Data Producing/Processing Department Representative', '数据生成/处理部门代表', '部门代表人员信息', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 24),
-- ('BDR03', 'Data Management Department', '数据管理部门', '负责数据管理的部门', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 25),
-- ('BDR04', 'Data Management Department Representative', '数据管理部门代表', '数据管理部门代表人员，义务报送相关', 'B', '业务解读', 'B1.数据需求业务初步解读', '义务报送', 'TEXT', 0, 26),
-- ('BDR05', 'Business Process', '业务流程', '相关的业务流程描述', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 27),
-- ('BDR06', 'data subject', '数据主题', '数据所属的主题分类', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 28),
-- ('BDR07', 'Product/Service', '产品/服务', '相关的产品或服务', 'B', '业务解读', 'B1.数据需求业务初步解读', 'B', 'TEXT', 0, 29),
-- ('BDR08', 'Data Source Type', '数据来源类型', '1-System; 2-Manual; 3-External', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'ENUM', 0, 30),
-- ('BDR09', 'Source System Name', '源系统名称', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 31),
-- ('BDR10', 'Function/Screen of Source System', '源系统对应功能界面', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 32),
-- ('BDR11', 'Function/Screen Description of Source System', '源系统对应功能界面描述', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 33),
-- ('BDR12', 'Data Field of Source System', '源系统对应字段', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 34),
-- ('BDR13', 'Manual Data Uploading Department', '手工上传数据维护部门', 'for manual data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 35),
-- ('BDR14', 'Manual Uploading Table Name', '手工上传表名称', 'for manual data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 36),
-- ('BDR15', 'External Source Name', '外部数据来源名称', 'for external data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 37),
-- ('BDR16', 'External data element(s)', '外部数据项', 'for external data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 38),
-- ('BDR17', 'Business Data Mapping Logic', '业务数据匹配逻辑', '业务层面的数据匹配逻辑', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 39),
-- ('BDR18', 'Reference Code and Mapping Logic', '码表映射信息', '含版本号的码表映射信息', 'B', '业务解读', 'B2.数据需求业务深度解读', 'B', 'TEXT', 0, 40);

-- -- C类字段：IT解读业务数据需求 (SDR01-SDR15)
-- INSERT INTO dd_fields_metadata (field_code, field_name, field_name_cn, field_desc, category_level, category_name, sub_category, process_type, data_type, is_vectorized, field_order) VALUES
-- ('SDR01', 'Source Data Layer', '源头数据所在的数据层', 'ODS/BDM/IDM/ADM等', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'ENUM', 0, 41),
-- ('SDR02', 'EIM ID', '源系统唯一标识码', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 42),
-- ('SDR03', 'Source System Name', '源系统名称', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 43),
-- ('SDR03.5', 'Source System Name ID', '源系统名称ID', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 44),
-- ('SDR04', 'Source System ITBA Contact', '源系统数据需求分析代表', '源系统的ITBA联系人', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 45),
-- ('SDR05', 'Source System File Name', '源系统文件名', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 46),
-- ('SDR06', 'Source System File Description', '源系统文件名描述', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'TEXT', 0, 47),
-- ('SDR07', 'Table Update Mode', '表更新模式', '全量/增量', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'C', 'ENUM', 0, 48),
-- ('SDR08', 'Data Field of Source System', '源系统对应字段', 'for system data only', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 49),
-- ('SDR08.5', 'Data Field ID of Source System', '源系统对应字段ID', 'for system data only', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 50),
-- ('SDR09', 'Data Field Description of Source System', '源系统对应字段描述', 'for system data only, 多个字段情况用分号"；"拼接', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 51),
-- ('SDR10', 'Data Processing Logic', '数据加工逻辑', '数据处理的具体逻辑', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 52),
-- ('SDR11', 'Trusted Source Indicator', '可信数据源标识', '可信/授权/其他/NA', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'ENUM', 0, 53),
-- ('SDR12', 'Related data element used for data producing', '用于生成此数据的其他关联数据项', '关联数据项信息', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 54),
-- ('SDR13', 'Data Type', '数据类型', 'alphabetic, numeric, date等', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'ENUM', 0, 55),
-- ('SDR14', 'Data Element Refresh Frequency', '数据接入频率', '数据刷新的频率', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 56),
-- ('SDR15', 'Remark', '备注', 'IT解读相关备注', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'C', 'TEXT', 0, 57);

-- -- D类字段：指标解读技术口径 (IDR01-IDR05)
-- INSERT INTO dd_fields_metadata (field_code, field_name, field_name_cn, field_desc, category_level, category_name, sub_category, process_type, data_type, is_vectorized, field_order) VALUES
-- ('IDR01', 'Data Model', '数据模型', '相关的数据模型', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'D', 'TEXT', 0, 58),
-- ('IDR02', 'Data Model ID', '数据模型ID', '数据模型的唯一标识', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'D', 'TEXT', 0, 59),
-- ('IDR03', 'Atomic Indicator Logic', '原子指标逻辑', '原子指标的计算逻辑', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'D', 'TEXT', 0, 60),
-- ('IDR04', 'Calculated Indicator Logic', '计算指标逻辑', '计算指标的逻辑', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'D', 'TEXT', 0, 61),
-- ('IDR05', 'Remark', '备注', '指标解读相关备注', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'D', 'TEXT', 0, 62);

-- -- ==========================================
-- -- 设置枚举值
-- -- ==========================================

-- -- 更新DR01字段的枚举值以支持'范围'
-- UPDATE dd_fields_metadata SET enum_values = JSON_ARRAY('ADS', 'BDM', 'IDM', 'ADM', 'ODS','范围') WHERE field_code = 'DR01';

-- -- 更新短字符串字段为VARCHAR类型（性能优化）
-- UPDATE dd_fields_metadata SET data_type = 'VARCHAR' WHERE field_code IN ('DR04','DR07','DR10','DR14','BDR01','BDR02','BDR03','BDR04','SDR02','SDR03','SDR03_5','SDR04','SDR05','SDR08_5','IDR02');
-- UPDATE dd_fields_metadata SET data_type = 'VARCHAR' WHERE field_code IN ('DR05','DR06','DR11','DR08','DR12','DR19','BDR08','SDR01','SDR07','SDR11','SDR13');

-- ==========================================
-- 初始化完成 - 列存储架构 + 业务表系统
-- ==========================================
--
-- 完整架构说明：
-- 1. dd_fields_metadata表：数据字典，包含62个字段定义
-- 2. dd_submission_data表：列存储架构的主数据表，每行代表一条DD数据记录
-- 3. dd_departments表：部门职责管理
-- 4. biz_dd_pre_distribution表：分发前表（DR01-DR21，增加submission_type、report_type、set字段）
-- 5. biz_dd_post_distribution表：分发后表（DR22及后续，按部门分工，增加submission_type、report_type、set字段）
-- 6. dd_report_data表：报表数据表，使用具体字段名，关联submission_data（增加report_type、set字段）
--
-- 业务流程：
-- 1. 分发前：创建biz_dd_pre_distribution记录（DD-A阶段）
-- 2. 部门分工：根据DR22等字段，确定需要分发的部门
-- 3. 分发后：各部门创建biz_dd_post_distribution记录（DD-B/C/D阶段）
-- 4. 最终数据：业务表完成后，数据落入dd_submission_data表
--
-- 关联关系（层级结构）：
-- 知识库层：knowledge_id (隔离不同知识库)
--   ↓
-- 上层：dd_report_data (报表层，包含knowledge_id)
--   ↓
-- 业务流程层（按submission_id关联）：
--   biz_dd_pre_distribution -> biz_dd_post_distribution -> dd_submission_data
--   ↓
-- 具体关联：
--   biz_dd_pre_distribution.submission_id (业务唯一标识)
--   biz_dd_post_distribution.submission_id (同一业务标识)
--   biz_dd_post_distribution.pre_distribution_id -> biz_dd_pre_distribution.id
--   biz_dd_post_distribution.dept_id -> dd_departments.dept_id
--   dd_submission_data.submission_id (最终数据落地)
--
-- 表结构统计：
-- - A类字段（结果数据需求）：22个 (dr01-dr22)
-- - B类字段（业务解读）：18个 (bdr01-bdr18)
-- - C类字段（IT解读）：15个 (sdr01-sdr15)
-- - D类字段（指标解读）：5个 (idr01-idr05)
-- - 总计：62个DD字段 + 基础管理字段 + 业务表系统
--
-- 验证查询示例：
-- SELECT COUNT(*) FROM dd_fields_metadata;
-- SELECT * FROM dd_departments;
-- SELECT COUNT(*) FROM biz_dd_pre_distribution;
-- SELECT COUNT(*) FROM biz_dd_post_distribution;
-- SELECT COUNT(*) FROM dd_report_data;
--
-- 关联查询示例：
-- SELECT pre.submission_id, pre.dr07, pre.dr01, post.dept_id, post.dr22
-- FROM biz_dd_pre_distribution pre
-- LEFT JOIN biz_dd_post_distribution post ON pre.id = post.pre_distribution_id;
--
-- 报表数据关联查询（上层到下层）：
-- SELECT r.knowledge_id, r.report_name, r.report_code, r.report_layer, r.report_department,
--        s.submission_id, s.dr06, s.dr07, s.dr01, s.dr22
-- FROM dd_report_data r
-- LEFT JOIN dd_submission_data s ON r.id = s.report_data_id
-- WHERE r.knowledge_id = 'your_knowledge_id';
--
-- 业务流程关联查询（按submission_id）：
-- SELECT pre.submission_id, pre.dr07, post.dept_id, sub.dr22
-- FROM biz_dd_pre_distribution pre
-- LEFT JOIN biz_dd_post_distribution post ON pre.submission_id = post.submission_id
-- LEFT JOIN dd_submission_data sub ON pre.submission_id = sub.submission_id;
--
-- 按知识库查询DD数据：
-- SELECT COUNT(*) FROM dd_report_data WHERE knowledge_id = 'your_knowledge_id';
--
-- ==========================================