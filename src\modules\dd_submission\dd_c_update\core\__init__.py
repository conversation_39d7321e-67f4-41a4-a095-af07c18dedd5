"""
DD-C更新核心处理模块 - 完全独立实现

包含DD-C更新的所有核心组件
"""

from .ddc_field_analyzer import DDCFieldAnalyzer, DDCUpdateStrategy, DDCFieldAnalysisResult, DDCPipelineParams
from .ddc_strategy_processor import DDCStrategyProcessor, DDCStrategyExecutionResult
from .ddc_pipeline_executor import DDCPipelineExecutor, DDCPipelineExecutionResult
from .ddc_field_mapper import DDCFieldMapper, DDCFieldMappingResult
from .ddc_update_crud import DDCUpdateCrud
from .ddc_update_processor import DDCUpdateProcessor, DDCUpdateRequest, DDCUpdateItem, DDCUpdateResult, DDCBatchUpdateResult

__all__ = [
    # 字段分析器
    'DDCFieldAnalyzer',
    'DDCUpdateStrategy',
    'DDCFieldAnalysisResult',
    'DDCPipelineParams',

    # 策略处理器
    'DDCStrategyProcessor',
    'DDCStrategyExecutionResult',

    # Pipeline执行器
    'DDCPipelineExecutor',
    'DDCPipelineExecutionResult',

    # 字段映射器
    'DDCFieldMapper',
    'DDCFieldMappingResult',

    # 数据库操作
    'DDCUpdateCrud',

    # 主处理器
    'DDCUpdateProcessor',
    'DDCUpdateRequest',
    'DDCUpdateItem',
    'DDCUpdateResult',
    'DDCBatchUpdateResult'
]
