"""
DD-B数据更新API路由

提供DD-B模块的数据更新功能，包括：
- 数据更新处理
- 更新预览
- 批量更新
- 策略分析
"""

import asyncio
import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from ..models.request_models import DDBUpdateRequest
from ..models.response_models import DDBUpdateResponse, DDBUpdateDataItem
from service import get_client
from modules.dd_submission.dd_b_update.dd_b_update_processor import DDBUpdateProcessor
from modules.dd_submission.dd_b_update.models.update_models import UpdateRequest

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/dd-b",
    tags=["DD-B数据更新"],
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)


async def get_database_clients():
    """获取数据库客户端依赖"""
    try:
        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        
        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None
        
        try:
            vdb_client = await get_client('database.vdbs.pgvector')
        except Exception as e:
            logger.warning(f"向量数据库客户端获取失败: {e}")
        
        try:
            embedding_client = await get_client('llm.embedding')
        except Exception as e:
            logger.warning(f"嵌入模型客户端获取失败: {e}")
        
        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }
        
    except Exception as e:
        logger.error(f"获取数据库客户端失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")


@router.post("/backfill", response_model=List[Dict])
async def update_dd_b_data(
    request: DDBUpdateRequest,
    clients: Dict[str, Any] = Depends(get_database_clients)
) -> List[Dict]:
    """
    DD-B数据更新接口
    
    Args:
        request: 更新请求，包含report_code、dept_id和要更新的数据
        clients: 数据库客户端
        
    Returns:
        DDBUpdateResponse: 更新结果
        
    Raises:
        HTTPException: 请求参数错误或处理失败
    """
    try:
        logger.info(f"🚀 收到DD-B数据更新请求: report_code={request.report_code}, dept_id={request.dept_id}, 数据量={len(request.data)}")
        
        # 验证请求参数
        if not request.data:
            raise HTTPException(status_code=400, detail="data不能为空")
        
        # 验证每个数据项都包含必需字段
        for i, item in enumerate(request.data):
            if 'entry_id' not in item:
                raise HTTPException(status_code=400, detail=f"data[{i}]缺少entry_id字段")
            if 'entry_type' not in item:
                raise HTTPException(status_code=400, detail=f"data[{i}]缺少entry_type字段")
        
        # 创建更新处理器
        processor = DDBUpdateProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client'],
            max_workers=5,
            enable_cache=True
        )
        
        # 转换为内部请求模型
        update_request = UpdateRequest(
            report_code=request.report_code,
            dept_id=request.dept_id,
            data=request.data
        )
        
        # 执行更新处理
        batch_result = await processor.process_update_request(update_request)
        
        # 构建前端兼容的响应格式
        response_data = []

        for result in batch_result.results:
            # 构建数据项
            data_item = {
                "entry_id": result.entry_id,
                "entry_type": getattr(result, 'entry_type', 'ITEM')  # 使用英文格式
            }

            # 只添加有实际值的BDR字段到响应中
            if result.success and result.updated_fields:
                import json
                for field_name, field_value in result.updated_fields.items():
                    # 只处理BDR字段，过滤掉SDR字段
                    if field_name.lower().startswith('bdr'):
                        # 过滤掉null
                        if field_value is not None:

                            # 将字段名转换为大写格式
                            field_name_upper = field_name.upper()

                            # JSON序列化复杂数据类型
                            if isinstance(field_value, (dict, list)):
                                data_item[field_name_upper] = json.dumps(field_value, ensure_ascii=False)
                            else:
                                data_item[field_name_upper] = str(field_value)

            response_data.append(data_item)

        logger.info(f"✅ DD-B数据更新完成: 成功{batch_result.success_count}条, 失败{batch_result.failed_count}条, 耗时={batch_result.execution_time:.2f}s")

        # 直接返回数据数组（前端需要的格式）
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"DD-B数据更新失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.post("/backfill/preview", response_model=Dict[str, Any])
async def preview_dd_b_update(
    request: DDBUpdateRequest,
    limit: int = 5,
    clients: Dict[str, Any] = Depends(get_database_clients)
) -> Dict[str, Any]:
    """
    DD-B数据更新预览接口
    
    Args:
        request: 更新请求
        limit: 预览数量限制
        clients: 数据库客户端
        
    Returns:
        预览结果
    """
    try:
        logger.info(f"🔍 收到DD-B数据更新预览请求: report_code={request.report_code}, dept_id={request.dept_id}, limit={limit}")
        
        # 验证请求参数
        if not request.data:
            raise HTTPException(status_code=400, detail="data不能为空")
        
        # 创建更新处理器
        processor = DDBUpdateProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client'],
            enable_cache=False  # 预览不需要缓存
        )
        
        # 转换为内部请求模型
        update_request = UpdateRequest(
            report_code=request.report_code,
            dept_id=request.dept_id,
            data=request.data
        )
        
        # 获取预览结果
        preview_result = await processor.get_update_preview(update_request, limit)
        
        logger.info(f"✅ DD-B数据更新预览完成: 预览{preview_result['preview_count']}条")
        
        return preview_result
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"DD-B数据更新预览失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/backfill/stats", response_model=Dict[str, Any])
async def get_update_stats(
    clients: Dict[str, Any] = Depends(get_database_clients)
) -> Dict[str, Any]:
    """
    获取更新处理器统计信息
    
    Args:
        clients: 数据库客户端
        
    Returns:
        统计信息
    """
    try:
        # 创建临时处理器获取统计信息
        processor = DDBUpdateProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client'],
            enable_cache=False
        )
        
        stats = processor.get_processor_stats()
        
        return {
            "processor_stats": stats,
            "available_clients": {
                "rdb_client": clients['rdb_client'] is not None,
                "vdb_client": clients['vdb_client'] is not None,
                "embedding_client": clients['embedding_client'] is not None
            }
        }
        
    except Exception as e:
        error_msg = f"获取统计信息失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/backfill/health")
async def health_check() -> Dict[str, Any]:
    """
    健康检查接口
    
    Returns:
        健康状态
    """
    return {
        "status": "healthy",
        "service": "dd-b-update",
        "version": "1.0.0"
    }
