"""
Knowledge V2 API - 模板管理路由

基于老的knowledge接口模板设计理念，为V2的真实表结构提供模板下载功能。

设计理念：
1. 展示给用户他们能修改的字段
2. 不包含自增ID字段
3. 让用户填写业务标识字段（如db_name, table_name等）
4. 对于外键关系，让用户填写业务标识而不是ID
5. 基于V2的真实表结构
"""

import io
import logging
import uuid
from typing import Optional, List
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Path, File, UploadFile, Form, Depends
from fastapi.responses import StreamingResponse
import pandas as pd

# 导入Knowledge V2的统一模型
from ..models.response_models import UnifiedResponse, UnifiedListResponse
from ..models.request_models import BatchOperationConfig
from ..dependencies.common import handle_api_errors, get_metadata_crud

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/templates",
    tags=["模板管理"],
    responses={404: {"description": "Not found"}}
)

# 支持的模板类型（基于V2真实表结构）
SUPPORTED_TEMPLATE_TYPES = {
    "source_database": {
        "name": "源数据库模板",
        "description": "用于批量导入源数据库信息",
        "table": "md_source_database",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称", "example": "customer_db"},
            {"name": "db_name_cn", "type": "string", "required": False, "description": "数据库中文名称", "example": "客户数据库"},
            {"name": "data_layer", "type": "enum", "required": True, "description": "数据层标识", "example": "ods", "options": ["adm", "bdm", "ods", "ads"]},
            {"name": "db_desc", "type": "text", "required": False, "description": "数据库描述", "example": "存储客户相关信息的数据库"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "index_database": {
        "name": "指标数据库模板",
        "description": "用于批量导入指标数据库信息",
        "table": "md_index_database",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称", "example": "index_db"},
            {"name": "db_name_cn", "type": "string", "required": False, "description": "数据库中文名称", "example": "指标数据库"},
            {"name": "data_layer", "type": "enum", "required": True, "description": "数据层标识", "example": "ads", "options": ["adm", "bdm", "ods", "ads"]},
            {"name": "db_desc", "type": "text", "required": False, "description": "数据库描述", "example": "存储业务指标的数据库"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "source_tables": {
        "name": "源表模板",
        "description": "用于批量导入源表信息",
        "table": "md_source_tables",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称（业务标识，非ID）", "example": "customer_db"},
            {"name": "table_name", "type": "string", "required": True, "description": "表名", "example": "customer_info"},
            {"name": "table_name_cn", "type": "string", "required": False, "description": "表中文名", "example": "客户信息表"},
            {"name": "table_desc", "type": "text", "required": False, "description": "表描述", "example": "存储客户基本信息"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "index_tables": {
        "name": "指标表模板",
        "description": "用于批量导入指标表信息",
        "table": "md_index_tables",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称（业务标识，非ID）", "example": "index_db"},
            {"name": "table_name", "type": "string", "required": True, "description": "表名", "example": "customer_metrics"},
            {"name": "table_name_cn", "type": "string", "required": False, "description": "表中文名", "example": "客户指标表"},
            {"name": "table_desc", "type": "text", "required": False, "description": "表描述", "example": "客户相关业务指标"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "source_columns": {
        "name": "源字段模板",
        "description": "用于批量导入源字段信息",
        "table": "md_source_columns",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称（业务标识）", "example": "customer_db"},
            {"name": "table_name", "type": "string", "required": True, "description": "表名（业务标识）", "example": "customer_info"},
            {"name": "column_name", "type": "string", "required": True, "description": "字段名", "example": "customer_id"},
            {"name": "column_name_cn", "type": "string", "required": False, "description": "字段中文名", "example": "客户ID"},
            {"name": "column_desc", "type": "text", "required": False, "description": "字段描述", "example": "客户唯一标识符"},
            {"name": "data_type", "type": "enum", "required": False, "description": "数据类型", "example": "NUMBER", "options": ["NUMBER", "STRING", "DATE", "BOOLEAN"]},
            {"name": "data_example", "type": "text", "required": False, "description": "数据样例", "example": "12345"},
            {"name": "is_vectorized", "type": "boolean", "required": False, "description": "是否已向量化", "example": False, "default": False},
            {"name": "is_primary_key", "type": "boolean", "required": False, "description": "是否主键", "example": True, "default": False},
            {"name": "is_sensitive", "type": "boolean", "required": False, "description": "是否敏感数据", "example": False, "default": False}
        ]
    },
    "index_columns": {
        "name": "指标字段模板",
        "description": "用于批量导入指标字段信息",
        "table": "md_index_columns",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称（业务标识）", "example": "index_db"},
            {"name": "table_name", "type": "string", "required": True, "description": "表名（业务标识）", "example": "customer_metrics"},
            {"name": "column_name", "type": "string", "required": True, "description": "字段名", "example": "total_amount"},
            {"name": "column_name_cn", "type": "string", "required": False, "description": "字段中文名", "example": "总金额"},
            {"name": "index_type", "type": "enum", "required": False, "description": "指标类型", "example": "atom", "options": ["atom", "compute"]},
            {"name": "column_desc", "type": "text", "required": False, "description": "字段描述", "example": "客户总交易金额"},
            {"name": "data_type", "type": "enum", "required": False, "description": "数据类型", "example": "NUMBER", "options": ["NUMBER", "STRING", "DATE", "INDEX_DATE"]},
            {"name": "data_example", "type": "text", "required": False, "description": "数据样例", "example": "1000000.00"},
            {"name": "comment", "type": "text", "required": False, "description": "备注说明", "example": "统计周期为月度"},
            {"name": "is_vectorized", "type": "boolean", "required": False, "description": "是否已向量化", "example": False, "default": False},
            {"name": "is_primary_key", "type": "boolean", "required": False, "description": "是否主键", "example": False, "default": False},
            {"name": "is_sensitive", "type": "boolean", "required": False, "description": "是否敏感数据", "example": False, "default": False}
        ]
    },
    "code_set": {
        "name": "码值集模板",
        "description": "用于批量导入码值集信息",
        "table": "md_reference_code_set",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "code_set_name", "type": "string", "required": True, "description": "码值集名称", "example": "customer_status"},
            {"name": "code_set_desc", "type": "string", "required": False, "description": "码值集描述", "example": "客户状态码值集"},
            {"name": "code_set_type", "type": "enum", "required": False, "description": "码值集类型", "example": "ENUM", "options": ["ENUM", "LOOKUP", "REFERENCE"], "default": "ENUM"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "code_value": {
        "name": "码值模板",
        "description": "用于批量导入码值信息",
        "table": "md_reference_code_value",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "code_set_name", "type": "string", "required": True, "description": "码值集名称（业务标识，非ID）", "example": "customer_status"},
            {"name": "code_value", "type": "string", "required": True, "description": "码值", "example": "ACTIVE"},
            {"name": "code_desc", "type": "string", "required": True, "description": "码值描述", "example": "活跃客户"},
            {"name": "code_value_cn", "type": "string", "required": False, "description": "码值中文描述", "example": "活跃"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True},
            {"name": "comment", "type": "string", "required": False, "description": "备注", "example": "近6个月有交易的客户"}
        ]
    },
    "data_subject": {
        "name": "数据主题模板",
        "description": "用于批量导入数据主题信息",
        "table": "md_data_subject",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "subject_code", "type": "string", "required": True, "description": "数据主题编码", "example": "CUSTOMER_ANALYSIS"},
            {"name": "subject_name", "type": "string", "required": True, "description": "数据主题名称", "example": "客户分析主题"},
            {"name": "subject_desc", "type": "text", "required": False, "description": "数据主题描述", "example": "用于客户行为分析和画像构建的数据主题"},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "data_subject_relation": {
        "name": "数据主题关联模板",
        "description": "用于批量导入数据主题与数据资源的关联关系",
        "table": "md_data_subject_relation",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "subject_code", "type": "string", "required": True, "description": "数据主题编码（业务标识，非ID）", "example": "CUSTOMER_ANALYSIS"},
            {"name": "source_type", "type": "enum", "required": True, "description": "目标类型", "example": "SOURCE", "options": ["SOURCE", "INDEX"]},
            {"name": "relation_path", "type": "string", "required": True, "description": "关联路径", "example": "customer_db.customer_info.customer_id"},
            {"name": "relation_level", "type": "enum", "required": True, "description": "关联层级", "example": "COLUMN", "options": ["DATABASE", "TABLE", "COLUMN"]},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "code_relation": {
        "name": "码值关联模板",
        "description": "用于批量导入字段与码值集的关联关系",
        "table": "md_reference_code_relation",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "db_name", "type": "string", "required": True, "description": "数据库名称（业务标识）", "example": "customer_db"},
            {"name": "table_name", "type": "string", "required": True, "description": "表名（业务标识）", "example": "customer_info"},
            {"name": "column_name", "type": "string", "required": True, "description": "字段名（业务标识）", "example": "customer_status"},
            {"name": "code_set_name", "type": "string", "required": True, "description": "码值集名称（业务标识，非ID）", "example": "customer_status"},
            {"name": "column_type", "type": "enum", "required": True, "description": "字段类型", "example": "source", "options": ["source", "index"]},
            {"name": "is_active", "type": "boolean", "required": False, "description": "是否激活", "example": True, "default": True}
        ]
    },
    "source_key_relation": {
        "name": "源字段关联模板",
        "description": "用于批量导入源字段之间的关联关系",
        "table": "md_source_key_relation_info",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "source_db_name", "type": "string", "required": True, "description": "源数据库名称", "example": "customer_db"},
            {"name": "source_table_name", "type": "string", "required": True, "description": "源表名", "example": "customer_info"},
            {"name": "source_column_name", "type": "string", "required": True, "description": "源字段名", "example": "customer_id"},
            {"name": "target_db_name", "type": "string", "required": True, "description": "目标数据库名称", "example": "order_db"},
            {"name": "target_table_name", "type": "string", "required": True, "description": "目标表名", "example": "order_info"},
            {"name": "target_column_name", "type": "string", "required": True, "description": "目标字段名", "example": "customer_id"},
            {"name": "relation_type", "type": "enum", "required": False, "description": "关联类型", "example": "FK", "options": ["FK", "REF"], "default": "FK"},
            {"name": "comment", "type": "string", "required": False, "description": "备注说明", "example": "客户ID外键关联"}
        ]
    },
    "index_key_relation": {
        "name": "指标字段关联模板",
        "description": "用于批量导入指标字段之间的关联关系",
        "table": "md_index_key_relation_info",
        "fields": [
            {"name": "knowledge_id", "type": "string", "required": True, "description": "知识库ID", "example": "kb_001"},
            {"name": "source_db_name", "type": "string", "required": True, "description": "源数据库名称", "example": "index_db"},
            {"name": "source_table_name", "type": "string", "required": True, "description": "源表名", "example": "customer_metrics"},
            {"name": "source_column_name", "type": "string", "required": True, "description": "源字段名", "example": "customer_id"},
            {"name": "target_db_name", "type": "string", "required": True, "description": "目标数据库名称", "example": "index_db"},
            {"name": "target_table_name", "type": "string", "required": True, "description": "目标表名", "example": "order_metrics"},
            {"name": "target_column_name", "type": "string", "required": True, "description": "目标字段名", "example": "customer_id"},
            {"name": "relation_type", "type": "enum", "required": False, "description": "关联类型", "example": "FK", "options": ["FK", "REF"], "default": "FK"},
            {"name": "comment", "type": "string", "required": False, "description": "备注说明", "example": "客户ID指标关联"}
        ]
    }
}


# ==================== 模板管理接口 ====================

@router.get("/supported", response_model=UnifiedListResponse, summary="获取支持的模板类型")
@handle_api_errors
async def get_supported_templates():
    """
    获取支持的模板类型列表
    
    返回所有支持的模板类型及其详细信息，基于V2的真实表结构
    """
    try:
        template_list = []
        for template_type, config in SUPPORTED_TEMPLATE_TYPES.items():
            template_info = {
                "template_type": template_type,
                "name": config["name"],
                "description": config["description"],
                "table": config["table"],
                "field_count": len(config["fields"]),
                "required_fields": [f["name"] for f in config["fields"] if f["required"]],
                "optional_fields": [f["name"] for f in config["fields"] if not f["required"]]
            }
            template_list.append(template_info)
        
        logger.info(f"返回支持的模板类型: {len(template_list)} 种")
        
        return UnifiedListResponse(
            success=True,
            message=f"成功获取 {len(template_list)} 种支持的模板类型",
            data=template_list,
            pagination={"total_records": len(template_list)}
        )
        
    except Exception as e:
        logger.error(f"获取支持的模板类型失败: {e}")
        return UnifiedListResponse(
            success=False,
            message=f"获取支持的模板类型失败: {str(e)}",
            data=[],
            pagination={"total_records": 0}
        )


@router.get("/download/{template_type}", summary="下载指定类型的模板文件")
@handle_api_errors
async def download_template(
    template_type: str = Path(..., description="模板类型"),
    format: str = Query("excel", description="文件格式", regex="^(excel|csv)$"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID（用于预填充）")
):
    """
    下载指定类型的模板文件
    
    基于V2真实表结构生成模板，遵循老的设计理念：
    1. 不包含自增ID字段
    2. 让用户填写业务标识字段
    3. 对于外键关系，使用业务标识而不是ID
    
    Args:
        template_type: 模板类型（如source_database, source_tables等）
        format: 文件格式（excel或csv）
        knowledge_id: 知识库ID，用于预填充knowledge_id字段
    """
    try:
        # 验证模板类型
        if template_type not in SUPPORTED_TEMPLATE_TYPES:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的模板类型: {template_type}。支持的类型: {list(SUPPORTED_TEMPLATE_TYPES.keys())}"
            )
        
        template_config = SUPPORTED_TEMPLATE_TYPES[template_type]
        
        # 生成模板数据
        template_data = await _generate_template_data(template_config, knowledge_id)
        
        # 根据格式生成文件
        if format == "excel":
            file_content, filename = await _generate_excel_template(template_type, template_data, template_config)
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        else:  # csv
            file_content, filename = await _generate_csv_template(template_type, template_data, template_config)
            media_type = "text/csv"
        
        logger.info(f"成功生成模板文件: template_type={template_type}, format={format}, filename={filename}")
        
        # 返回文件流响应
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载模板失败: template_type={template_type}, format={format}, error={e}")
        raise HTTPException(status_code=500, detail=f"下载模板失败: {str(e)}")


@router.get("/{template_type}/fields", response_model=UnifiedResponse, summary="获取模板字段定义")
@handle_api_errors
async def get_template_fields(
    template_type: str = Path(..., description="模板类型")
):
    """
    获取指定模板类型的字段定义
    
    返回模板的详细字段信息，包括字段名、类型、是否必需、描述、示例等
    """
    try:
        # 验证模板类型
        if template_type not in SUPPORTED_TEMPLATE_TYPES:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的模板类型: {template_type}。支持的类型: {list(SUPPORTED_TEMPLATE_TYPES.keys())}"
            )
        
        template_config = SUPPORTED_TEMPLATE_TYPES[template_type]
        
        field_definitions = {
            "template_type": template_type,
            "name": template_config["name"],
            "description": template_config["description"],
            "table": template_config["table"],
            "fields": template_config["fields"],
            "field_count": len(template_config["fields"]),
            "required_field_count": len([f for f in template_config["fields"] if f["required"]]),
            "design_principles": [
                "不包含自增ID字段",
                "让用户填写业务标识字段",
                "对于外键关系，使用业务标识而不是ID",
                "基于V2真实表结构设计"
            ]
        }
        
        logger.info(f"返回模板字段定义: template_type={template_type}")
        
        return UnifiedResponse(
            success=True,
            message=f"成功获取模板 {template_type} 的字段定义",
            data=field_definitions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板字段定义失败: template_type={template_type}, error={e}")
        raise HTTPException(status_code=500, detail=f"获取模板字段定义失败: {str(e)}")


# ==================== 模板上传和处理接口 ====================

@router.post("/upload", response_model=UnifiedResponse, summary="上传模板文件")
@handle_api_errors
async def upload_template(
    knowledge_id: str = Form(..., description="知识库ID"),
    template_type: str = Form(..., description="模板类型"),
    description: Optional[str] = Form(None, description="模板描述"),
    auto_process: bool = Form(False, description="是否自动处理"),
    file: UploadFile = File(..., description="模板文件"),
    meta_crud = Depends(get_metadata_crud)
):
    """
    上传模板文件并进行解析验证

    支持的模板类型：
    - source_database: 源数据库模板
    - source_tables: 源表模板
    - source_columns: 源字段模板
    - code_set: 码值集模板
    - code_value: 码值模板
    等

    处理流程：
    1. 验证文件格式和大小
    2. 解析文件内容
    3. 验证数据格式
    4. 保存模板记录
    5. 可选：自动处理并入库
    """
    try:
        # 验证模板类型
        if template_type not in SUPPORTED_TEMPLATE_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的模板类型: {template_type}。支持的类型: {list(SUPPORTED_TEMPLATE_TYPES.keys())}"
            )

        # 验证文件格式
        file_extension = file.filename.split('.')[-1].lower() if file.filename else ""
        if file_extension not in ['xlsx', 'xls', 'csv']:
            raise HTTPException(
                status_code=400,
                detail="不支持的文件格式。支持的格式: .xlsx, .xls, .csv"
            )

        # 验证文件大小（50MB限制）
        file_content = await file.read()
        if len(file_content) > 50 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="文件大小超过限制（50MB）"
            )

        # 生成模板ID
        template_id = str(uuid.uuid4())

        # 解析文件内容
        parsed_data = await _parse_template_file(file_content, file.filename, file_extension)

        # 验证模板数据
        validation_result = await _validate_template_data(parsed_data, template_type)

        if not validation_result["valid"]:
            return UnifiedResponse(
                success=False,
                message=f"模板验证失败: {'; '.join(validation_result['errors'])}",
                data={
                    "template_id": template_id,
                    "validation_result": validation_result
                },
                timestamp=datetime.now()
            )

        # 保存模板记录
        template_record = {
            "template_id": template_id,
            "knowledge_id": knowledge_id,
            "template_type": template_type,
            "filename": file.filename,
            "description": description,
            "file_size": len(file_content),
            "status": "uploaded",
            "upload_time": datetime.now(),
            "parsed_data": parsed_data,
            "validation_result": validation_result
        }

        # 如果启用自动处理，则进行数据入库
        if auto_process:
            process_result = await _process_template_data(
                knowledge_id, template_type, parsed_data, meta_crud
            )
            template_record["status"] = "processed"
            template_record["process_result"] = process_result

        logger.info(f"模板上传成功: template_id={template_id}, filename={file.filename}")

        return UnifiedResponse(
            success=True,
            message="模板上传成功" + ("并已自动处理" if auto_process else ""),
            data=template_record,
            timestamp=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板上传失败: filename={file.filename if file else 'unknown'}, error={e}")
        raise HTTPException(status_code=500, detail=f"模板上传失败: {str(e)}")


@router.post("/process/{template_id}", response_model=UnifiedResponse, summary="处理已上传的模板")
@handle_api_errors
async def process_template(
    template_id: str = Path(..., description="模板ID"),
    knowledge_id: str = Form(..., description="知识库ID"),
    auto_extract: bool = Form(True, description="是否自动提取元数据"),
    meta_crud = Depends(get_metadata_crud)
):
    """
    处理已上传的模板，提取元数据并入库

    处理流程：
    1. 获取模板记录
    2. 验证模板状态
    3. 提取元数据
    4. 批量入库
    5. 更新模板状态
    """
    try:
        # 这里应该从数据库获取模板记录，暂时模拟
        # template_record = await _get_template_record(template_id)

        # 模拟处理结果
        process_result = {
            "template_id": template_id,
            "knowledge_id": knowledge_id,
            "status": "processed",
            "auto_extract": auto_extract,
            "extracted_entities": {
                "databases": 0,
                "tables": 0,
                "columns": 0,
                "code_sets": 0,
                "code_values": 0
            },
            "process_time": datetime.now(),
            "success_count": 0,
            "error_count": 0,
            "errors": []
        }

        logger.info(f"模板处理完成: template_id={template_id}")

        return UnifiedResponse(
            success=True,
            message="模板处理成功",
            data=process_result,
            timestamp=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板处理失败: template_id={template_id}, error={e}")
        raise HTTPException(status_code=500, detail=f"模板处理失败: {str(e)}")


@router.get("/validate/{template_type}", response_model=UnifiedResponse, summary="验证模板数据格式")
@handle_api_errors
async def validate_template_format(
    template_type: str = Path(..., description="模板类型"),
    sample_data: str = Query(..., description="示例数据（JSON格式）")
):
    """
    验证模板数据格式是否符合要求

    用于前端实时验证用户输入的数据格式
    """
    try:
        import json

        # 验证模板类型
        if template_type not in SUPPORTED_TEMPLATE_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的模板类型: {template_type}"
            )

        # 解析示例数据
        try:
            data = json.loads(sample_data)
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=400,
                detail=f"示例数据格式错误: {str(e)}"
            )

        # 验证数据格式
        validation_result = await _validate_sample_data(data, template_type)

        return UnifiedResponse(
            success=validation_result["valid"],
            message="数据格式验证完成",
            data=validation_result,
            timestamp=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板格式验证失败: template_type={template_type}, error={e}")
        raise HTTPException(status_code=500, detail=f"模板格式验证失败: {str(e)}")


@router.get("/history", response_model=UnifiedListResponse, summary="获取模板上传历史")
@handle_api_errors
async def get_template_history(
    knowledge_id: Optional[str] = Query(None, description="知识库ID"),
    template_type: Optional[str] = Query(None, description="模板类型"),
    status: Optional[str] = Query(None, description="模板状态"),
    limit: int = Query(20, description="限制数量", ge=1, le=100)
):
    """
    获取模板上传和处理历史记录

    支持按知识库ID、模板类型、状态等条件筛选
    """
    try:
        # 模拟历史记录数据
        history_records = []
        for i in range(min(limit, 10)):  # 模拟最多10条记录
            record = {
                "template_id": f"template_{i+1}",
                "knowledge_id": knowledge_id or f"kb_{i+1:03d}",
                "template_type": template_type or ["source_database", "source_tables", "source_columns"][i % 3],
                "filename": f"template_{i+1}.xlsx",
                "status": status or ["uploaded", "processed", "failed"][i % 3],
                "upload_time": datetime.now(),
                "file_size": 1024 * (i + 1),
                "description": f"模板文件 {i+1}"
            }
            history_records.append(record)

        logger.info(f"获取模板历史记录: 返回 {len(history_records)} 条记录")

        return UnifiedListResponse(
            success=True,
            message=f"成功获取 {len(history_records)} 条模板历史记录",
            data=history_records,
            pagination={"total_records": len(history_records)},
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"获取模板历史记录失败: {e}")
        return UnifiedListResponse(
            success=False,
            message=f"获取模板历史记录失败: {str(e)}",
            data=[],
            pagination={"total_records": 0},
            timestamp=datetime.now()
        )


# ==================== 辅助函数 ====================

async def _generate_template_data(template_config: dict, knowledge_id: Optional[str] = None) -> dict:
    """生成模板数据"""
    try:
        # 创建表头
        headers = []
        sample_row = []
        
        for field in template_config["fields"]:
            # 添加表头（包含字段描述）
            header = f"{field['name']}"
            if field["required"]:
                header += " *"  # 必填字段标记
            headers.append(header)
            
            # 生成示例数据
            if field["name"] == "knowledge_id" and knowledge_id:
                sample_row.append(knowledge_id)  # 使用提供的knowledge_id
            elif "example" in field:
                sample_row.append(field["example"])
            elif "default" in field:
                sample_row.append(field["default"])
            else:
                sample_row.append("")  # 空值
        
        return {
            "headers": headers,
            "sample_row": sample_row,
            "field_descriptions": [f["description"] for f in template_config["fields"]]
        }
        
    except Exception as e:
        logger.error(f"生成模板数据失败: {e}")
        raise


async def _generate_excel_template(template_type: str, template_data: dict, template_config: dict) -> tuple:
    """生成Excel模板文件"""
    try:
        # 创建DataFrame
        df = pd.DataFrame([template_data["sample_row"]], columns=template_data["headers"])
        
        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 写入数据
            df.to_excel(writer, sheet_name='模板数据', index=False)
            
            # 创建说明工作表
            description_data = []
            for i, field in enumerate(template_config["fields"]):
                description_data.append({
                    "字段名": field["name"],
                    "中文名": field.get("description", ""),
                    "类型": field["type"],
                    "必填": "是" if field["required"] else "否",
                    "示例": field.get("example", ""),
                    "说明": field.get("description", "")
                })
            
            desc_df = pd.DataFrame(description_data)
            desc_df.to_excel(writer, sheet_name='字段说明', index=False)
        
        output.seek(0)
        filename = f"{template_type}_template.xlsx"
        
        return output.getvalue(), filename
        
    except Exception as e:
        logger.error(f"生成Excel模板失败: {e}")
        raise


async def _generate_csv_template(template_type: str, template_data: dict, template_config: dict) -> tuple:
    """生成CSV模板文件"""
    try:
        # 创建DataFrame
        df = pd.DataFrame([template_data["sample_row"]], columns=template_data["headers"])
        
        # 生成CSV文件
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8-sig')  # 使用utf-8-sig确保中文正确显示
        
        filename = f"{template_type}_template.csv"
        
        return output.getvalue().encode('utf-8-sig'), filename
        
    except Exception as e:
        logger.error(f"生成CSV模板失败: {e}")
        raise


async def _parse_template_file(file_content: bytes, filename: str, file_extension: str) -> dict:
    """解析模板文件内容"""
    try:
        if file_extension in ['xlsx', 'xls']:
            # 解析Excel文件
            import io
            df = pd.read_excel(io.BytesIO(file_content))

            parsed_data = {
                "file_type": "excel",
                "filename": filename,
                "sheets": {
                    "Sheet1": {
                        "rows": len(df),
                        "columns": len(df.columns),
                        "column_names": df.columns.tolist(),
                        "data_types": df.dtypes.astype(str).to_dict(),
                        "sample_data": df.head(5).to_dict("records"),
                        "all_data": df.to_dict("records")  # 用于后续处理
                    }
                }
            }
        elif file_extension == 'csv':
            # 解析CSV文件
            import io
            df = pd.read_csv(io.BytesIO(file_content))

            parsed_data = {
                "file_type": "csv",
                "filename": filename,
                "data": {
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist(),
                    "data_types": df.dtypes.astype(str).to_dict(),
                    "sample_data": df.head(5).to_dict("records"),
                    "all_data": df.to_dict("records")  # 用于后续处理
                }
            }
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")

        return parsed_data

    except Exception as e:
        logger.error(f"解析模板文件失败: filename={filename}, error={e}")
        raise


async def _validate_template_data(parsed_data: dict, template_type: str) -> dict:
    """验证模板数据格式"""
    try:
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "metadata": {
                "template_type": template_type,
                "validation_time": datetime.now()
            }
        }

        # 获取模板配置
        template_config = SUPPORTED_TEMPLATE_TYPES[template_type]
        required_fields = [f["name"] for f in template_config["fields"] if f["required"]]

        # 获取数据
        if parsed_data["file_type"] == "excel":
            data_rows = parsed_data["sheets"]["Sheet1"]["all_data"]
            column_names = parsed_data["sheets"]["Sheet1"]["column_names"]
        else:  # csv
            data_rows = parsed_data["data"]["all_data"]
            column_names = parsed_data["data"]["column_names"]

        # 验证必填字段
        missing_fields = []
        for field in required_fields:
            # 检查字段名（可能带*标记）
            field_variations = [field, f"{field} *", f"{field}*"]
            if not any(var in column_names for var in field_variations):
                missing_fields.append(field)

        if missing_fields:
            validation_result["errors"].append(f"缺少必填字段: {missing_fields}")

        # 验证数据行数
        if len(data_rows) == 0:
            validation_result["errors"].append("模板文件没有数据行")
        elif len(data_rows) == 1:
            validation_result["warnings"].append("模板文件只有示例数据行，请添加实际数据")

        # 验证数据完整性
        for i, row in enumerate(data_rows[:10]):  # 只验证前10行
            for field in required_fields:
                field_variations = [field, f"{field} *", f"{field}*"]
                field_value = None
                for var in field_variations:
                    if var in row:
                        field_value = row[var]
                        break

                if field_value is None or str(field_value).strip() == "" or str(field_value) == "nan":
                    validation_result["errors"].append(f"第{i+1}行缺少必填字段值: {field}")
                    break  # 避免重复报错

        # 设置最终验证状态
        validation_result["valid"] = len(validation_result["errors"]) == 0

        return validation_result

    except Exception as e:
        logger.error(f"验证模板数据失败: template_type={template_type}, error={e}")
        return {
            "valid": False,
            "errors": [f"验证过程异常: {str(e)}"],
            "warnings": [],
            "metadata": {"template_type": template_type}
        }


async def _validate_sample_data(data: dict, template_type: str) -> dict:
    """验证示例数据格式"""
    try:
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "field_validations": {}
        }

        # 获取模板配置
        template_config = SUPPORTED_TEMPLATE_TYPES[template_type]

        # 验证每个字段
        for field_config in template_config["fields"]:
            field_name = field_config["name"]
            field_type = field_config["type"]
            required = field_config["required"]

            field_validation = {
                "valid": True,
                "errors": [],
                "warnings": []
            }

            # 检查字段是否存在
            if field_name not in data:
                if required:
                    field_validation["errors"].append(f"缺少必填字段: {field_name}")
                    field_validation["valid"] = False
                else:
                    field_validation["warnings"].append(f"缺少可选字段: {field_name}")
            else:
                field_value = data[field_name]

                # 验证字段类型
                if field_type == "string" and not isinstance(field_value, str):
                    field_validation["warnings"].append(f"字段类型不匹配，期望string，实际{type(field_value).__name__}")
                elif field_type == "boolean" and not isinstance(field_value, bool):
                    field_validation["warnings"].append(f"字段类型不匹配，期望boolean，实际{type(field_value).__name__}")
                elif field_type == "enum" and "options" in field_config:
                    if field_value not in field_config["options"]:
                        field_validation["errors"].append(f"枚举值无效，期望{field_config['options']}之一，实际{field_value}")
                        field_validation["valid"] = False

            validation_result["field_validations"][field_name] = field_validation

            # 汇总错误
            if not field_validation["valid"]:
                validation_result["errors"].extend(field_validation["errors"])
                validation_result["valid"] = False
            validation_result["warnings"].extend(field_validation["warnings"])

        return validation_result

    except Exception as e:
        logger.error(f"验证示例数据失败: template_type={template_type}, error={e}")
        return {
            "valid": False,
            "errors": [f"验证过程异常: {str(e)}"],
            "warnings": [],
            "field_validations": {}
        }


async def _process_template_data(knowledge_id: str, template_type: str, parsed_data: dict, meta_crud) -> dict:
    """
    处理模板数据并入库

    确保满足CRUD级联需求：
    1. 正确的外键关系处理
    2. 业务标识到ID的映射
    3. 级联删除和更新的支持
    4. 向量化数据的同步处理
    """
    try:
        process_result = {
            "success_count": 0,
            "error_count": 0,
            "errors": [],
            "processed_entities": [],
            "id_mappings": {}  # 业务标识到ID的映射，用于级联操作
        }

        # 获取数据行
        if parsed_data["file_type"] == "excel":
            data_rows = parsed_data["sheets"]["Sheet1"]["all_data"]
        else:  # csv
            data_rows = parsed_data["data"]["all_data"]

        # 根据模板类型处理数据，确保满足CRUD级联需求
        if template_type == "source_database":
            await _process_source_database_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "index_database":
            await _process_index_database_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "source_tables":
            await _process_source_tables_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "index_tables":
            await _process_index_tables_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "source_columns":
            await _process_source_columns_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "index_columns":
            await _process_index_columns_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "code_set":
            await _process_code_set_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "code_value":
            await _process_code_value_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "data_subject":
            await _process_data_subject_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "data_subject_relation":
            await _process_data_subject_relation_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "code_relation":
            await _process_code_relation_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "source_key_relation":
            await _process_source_key_relation_template(data_rows, knowledge_id, meta_crud, process_result)
        elif template_type == "index_key_relation":
            await _process_index_key_relation_template(data_rows, knowledge_id, meta_crud, process_result)
        else:
            raise ValueError(f"不支持的模板类型: {template_type}")

        return process_result

    except Exception as e:
        logger.error(f"处理模板数据失败: template_type={template_type}, error={e}")
        return {
            "success_count": 0,
            "error_count": 1,
            "errors": [f"处理过程异常: {str(e)}"],
            "processed_entities": [],
            "id_mappings": {}
        }


async def _process_source_database_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理源数据库模板，确保满足级联删除需求"""
    for i, row in enumerate(data_rows):
        try:
            # 构建数据库记录
            db_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "db_name": row.get("db_name") or row.get("db_name *"),
                "db_name_cn": row.get("db_name_cn"),
                "data_layer": row.get("data_layer") or row.get("data_layer *"),
                "db_desc": row.get("db_desc"),
                "is_active": row.get("is_active", True)
            }

            # 调用实际的CRUD方法创建数据库
            db_id = await meta_crud.create_source_database(db_record)

            if db_id:
                # 记录业务标识到ID的映射，用于后续级联操作
                db_name = db_record["db_name"]
                process_result["id_mappings"][f"source_database:{db_name}"] = db_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "source_database",
                    "business_key": db_name,
                    "generated_id": db_id,
                    "data": db_record
                })
            else:
                raise Exception("创建数据库失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _process_source_tables_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理源表模板，确保正确的外键关系"""
    for i, row in enumerate(data_rows):
        try:
            db_name = row.get("db_name") or row.get("db_name *")
            table_name = row.get("table_name") or row.get("table_name *")

            # 通过业务标识查找数据库ID
            db_id = await _resolve_database_id(meta_crud, knowledge_id, db_name, "source")
            if not db_id:
                raise Exception(f"找不到数据库: {db_name}")

            # 构建表记录
            table_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "db_id": db_id,  # 使用解析出的数据库ID
                "table_name": table_name,
                "table_name_cn": row.get("table_name_cn"),
                "table_desc": row.get("table_desc"),
                "is_active": row.get("is_active", True)
            }

            # 调用实际的CRUD方法创建表
            table_id = await meta_crud.create_source_table(table_record)

            if table_id:
                # 记录业务标识到ID的映射
                business_key = f"{db_name}.{table_name}"
                process_result["id_mappings"][f"source_table:{business_key}"] = table_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "source_table",
                    "business_key": business_key,
                    "generated_id": table_id,
                    "data": table_record
                })
            else:
                raise Exception("创建表失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _resolve_database_id(meta_crud, knowledge_id: str, db_name: str, db_type: str) -> int:
    """通过业务标识解析数据库ID"""
    try:
        if db_type == "source":
            databases = await meta_crud.list_source_databases(
                knowledge_id=knowledge_id,
                db_name=db_name,
                limit=1
            )
        else:  # index
            databases = await meta_crud.list_index_databases(
                knowledge_id=knowledge_id,
                db_name=db_name,
                limit=1
            )

        if databases and len(databases) > 0:
            return databases[0].get("db_id")
        return None

    except Exception as e:
        logger.error(f"解析数据库ID失败: db_name={db_name}, db_type={db_type}, error={e}")
        return None


async def _resolve_table_id(meta_crud, knowledge_id: str, db_name: str, table_name: str, table_type: str) -> int:
    """通过业务标识解析表ID"""
    try:
        # 先获取数据库ID
        db_id = await _resolve_database_id(meta_crud, knowledge_id, db_name, table_type)
        if not db_id:
            return None

        if table_type == "source":
            tables = await meta_crud.list_source_tables(
                knowledge_id=knowledge_id,
                db_id=db_id,
                table_name=table_name,
                limit=1
            )
        else:  # index
            tables = await meta_crud.list_index_tables(
                knowledge_id=knowledge_id,
                db_id=db_id,
                table_name=table_name,
                limit=1
            )

        if tables and len(tables) > 0:
            return tables[0].get("table_id")
        return None

    except Exception as e:
        logger.error(f"解析表ID失败: db_name={db_name}, table_name={table_name}, table_type={table_type}, error={e}")
        return None


async def _process_source_columns_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理源字段模板，确保正确的外键关系和级联删除支持"""
    for i, row in enumerate(data_rows):
        try:
            db_name = row.get("db_name") or row.get("db_name *")
            table_name = row.get("table_name") or row.get("table_name *")
            column_name = row.get("column_name") or row.get("column_name *")

            # 通过业务标识查找表ID
            table_id = await _resolve_table_id(meta_crud, knowledge_id, db_name, table_name, "source")
            if not table_id:
                raise Exception(f"找不到表: {db_name}.{table_name}")

            # 构建字段记录
            column_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "table_id": table_id,  # 使用解析出的表ID
                "column_name": column_name,
                "column_name_cn": row.get("column_name_cn"),
                "column_desc": row.get("column_desc"),
                "data_type": row.get("data_type"),
                "data_example": row.get("data_example"),
                "is_vectorized": row.get("is_vectorized", False),
                "is_primary_key": row.get("is_primary_key", False),
                "is_sensitive": row.get("is_sensitive", False)
            }

            # 调用实际的CRUD方法创建字段
            column_id = await meta_crud.create_source_column(column_record)

            if column_id:
                # 记录业务标识到ID的映射
                business_key = f"{db_name}.{table_name}.{column_name}"
                process_result["id_mappings"][f"source_column:{business_key}"] = column_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "source_column",
                    "business_key": business_key,
                    "generated_id": column_id,
                    "data": column_record
                })
            else:
                raise Exception("创建字段失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _process_code_set_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理码值集模板，确保级联删除支持"""
    for i, row in enumerate(data_rows):
        try:
            code_set_name = row.get("code_set_name") or row.get("code_set_name *")

            # 构建码值集记录
            code_set_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "code_set_name": code_set_name,
                "code_set_desc": row.get("code_set_desc"),
                "code_set_type": row.get("code_set_type", "ENUM"),
                "is_active": row.get("is_active", True)
            }

            # 调用实际的CRUD方法创建码值集
            code_set_id = await meta_crud.create_code_set(code_set_record)

            if code_set_id:
                # 记录业务标识到ID的映射
                process_result["id_mappings"][f"code_set:{code_set_name}"] = code_set_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "code_set",
                    "business_key": code_set_name,
                    "generated_id": code_set_id,
                    "data": code_set_record
                })
            else:
                raise Exception("创建码值集失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _process_code_value_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理码值模板，确保正确的外键关系"""
    for i, row in enumerate(data_rows):
        try:
            code_set_name = row.get("code_set_name") or row.get("code_set_name *")
            code_value = row.get("code_value") or row.get("code_value *")

            # 通过业务标识查找码值集ID
            code_set_id = await _resolve_code_set_id(meta_crud, knowledge_id, code_set_name)
            if not code_set_id:
                raise Exception(f"找不到码值集: {code_set_name}")

            # 构建码值记录
            code_value_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "code_set_id": code_set_id,  # 使用解析出的码值集ID
                "code_value": code_value,
                "code_desc": row.get("code_desc") or row.get("code_desc *"),
                "code_value_cn": row.get("code_value_cn"),
                "is_active": row.get("is_active", True),
                "comment": row.get("comment")
            }

            # 调用实际的CRUD方法创建码值
            code_value_id = await meta_crud.create_code_value(code_value_record)

            if code_value_id:
                # 记录业务标识到ID的映射
                business_key = f"{code_set_name}.{code_value}"
                process_result["id_mappings"][f"code_value:{business_key}"] = code_value_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "code_value",
                    "business_key": business_key,
                    "generated_id": code_value_id,
                    "data": code_value_record
                })
            else:
                raise Exception("创建码值失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _resolve_code_set_id(meta_crud, knowledge_id: str, code_set_name: str) -> int:
    """通过业务标识解析码值集ID"""
    try:
        code_sets = await meta_crud.list_code_sets(
            knowledge_id=knowledge_id,
            code_set_name=code_set_name,
            limit=1
        )

        if code_sets and len(code_sets) > 0:
            return code_sets[0].get("id")
        return None

    except Exception as e:
        logger.error(f"解析码值集ID失败: code_set_name={code_set_name}, error={e}")
        return None


async def _process_source_key_relation_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理源字段关联模板，确保正确的外键关系处理"""
    for i, row in enumerate(data_rows):
        try:
            # 解析源字段
            source_db_name = row.get("source_db_name") or row.get("source_db_name *")
            source_table_name = row.get("source_table_name") or row.get("source_table_name *")
            source_column_name = row.get("source_column_name") or row.get("source_column_name *")

            # 解析目标字段
            target_db_name = row.get("target_db_name") or row.get("target_db_name *")
            target_table_name = row.get("target_table_name") or row.get("target_table_name *")
            target_column_name = row.get("target_column_name") or row.get("target_column_name *")

            # 通过业务标识查找源字段ID
            source_column_id = await _resolve_column_id(
                meta_crud, knowledge_id, source_db_name, source_table_name, source_column_name, "source"
            )
            if not source_column_id:
                raise Exception(f"找不到源字段: {source_db_name}.{source_table_name}.{source_column_name}")

            # 通过业务标识查找目标字段ID
            target_column_id = await _resolve_column_id(
                meta_crud, knowledge_id, target_db_name, target_table_name, target_column_name, "source"
            )
            if not target_column_id:
                raise Exception(f"找不到目标字段: {target_db_name}.{target_table_name}.{target_column_name}")

            # 构建关联记录
            relation_record = {
                "knowledge_id": row.get("knowledge_id", knowledge_id),
                "source_column_id": source_column_id,  # 使用解析出的源字段ID
                "target_column_id": target_column_id,  # 使用解析出的目标字段ID
                "relation_type": row.get("relation_type", "FK"),
                "comment": row.get("comment")
            }

            # 调用实际的CRUD方法创建关联
            relation_id = await meta_crud.create_source_key_relation(relation_record)

            if relation_id:
                business_key = f"{source_db_name}.{source_table_name}.{source_column_name}->{target_db_name}.{target_table_name}.{target_column_name}"
                process_result["id_mappings"][f"source_key_relation:{business_key}"] = relation_id

                process_result["success_count"] += 1
                process_result["processed_entities"].append({
                    "row": i + 1,
                    "type": "source_key_relation",
                    "business_key": business_key,
                    "generated_id": relation_id,
                    "data": relation_record
                })
            else:
                raise Exception("创建源字段关联失败，返回空ID")

        except Exception as e:
            process_result["error_count"] += 1
            process_result["errors"].append(f"第{i+1}行处理失败: {str(e)}")


async def _resolve_column_id(meta_crud, knowledge_id: str, db_name: str, table_name: str, column_name: str, column_type: str) -> int:
    """通过业务标识解析字段ID"""
    try:
        # 先获取表ID
        table_id = await _resolve_table_id(meta_crud, knowledge_id, db_name, table_name, column_type)
        if not table_id:
            return None

        if column_type == "source":
            columns = await meta_crud.list_source_columns(
                knowledge_id=knowledge_id,
                table_id=table_id,
                column_name=column_name,
                limit=1
            )
        else:  # index
            columns = await meta_crud.list_index_columns(
                knowledge_id=knowledge_id,
                table_id=table_id,
                column_name=column_name,
                limit=1
            )

        if columns and len(columns) > 0:
            return columns[0].get("column_id")
        return None

    except Exception as e:
        logger.error(f"解析字段ID失败: db_name={db_name}, table_name={table_name}, column_name={column_name}, column_type={column_type}, error={e}")
        return None


# 为了简化，其他模板处理函数使用类似的模式
async def _process_index_database_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理指标数据库模板"""
    # 类似于 _process_source_database_template，但调用 create_index_database
    pass

async def _process_index_tables_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理指标表模板"""
    # 类似于 _process_source_tables_template，但调用 create_index_table
    pass

async def _process_index_columns_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理指标字段模板"""
    # 类似于 _process_source_columns_template，但调用 create_index_column
    pass

async def _process_data_subject_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理数据主题模板"""
    # 类似于 _process_code_set_template，但调用 create_data_subject
    pass

async def _process_data_subject_relation_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理数据主题关联模板"""
    # 需要解析 subject_code 到 subject_id，然后创建关联
    pass

async def _process_code_relation_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理码值关联模板"""
    # 需要解析字段路径和码值集名称到对应的ID
    pass

async def _process_index_key_relation_template(data_rows: list, knowledge_id: str, meta_crud, process_result: dict):
    """处理指标字段关联模板"""
    # 类似于 _process_source_key_relation_template，但处理指标字段
    pass


# ==================== 健康检查接口 ====================

@router.get("/health", summary="模板管理健康检查")
async def templates_health_check():
    """模板管理模块健康检查"""
    return {
        "status": "healthy",
        "module": "模板管理",
        "version": "2.0.0",
        "supported_template_types": list(SUPPORTED_TEMPLATE_TYPES.keys()),
        "supported_formats": ["excel", "csv"],
        "design_principles": [
            "基于V2真实表结构",
            "不包含自增ID字段",
            "让用户填写业务标识字段",
            "对于外键关系，使用业务标识而不是ID"
        ],
        "endpoints": {
            "supported_templates": "/templates/supported",
            "download_template": "/templates/download/{template_type}",
            "template_fields": "/templates/{template_type}/fields",
            "upload_template": "/templates/upload",
            "process_template": "/templates/process/{template_id}",
            "validate_template": "/templates/validate/{template_type}",
            "template_history": "/templates/history",
            "health_check": "/templates/health"
        },
        "features": [
            "模板下载（Excel/CSV）",
            "模板上传和解析",
            "数据格式验证",
            "自动数据入库",
            "模板历史记录",
            "实时格式验证"
        ]
    }
