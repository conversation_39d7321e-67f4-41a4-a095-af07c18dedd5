"""
DD-C策略处理器 - 完全独立实现

专注于策略路由和执行协调，整合所有核心组件
"""

import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .ddc_field_analyzer import DDCFieldAnalyzer, DDCUpdateStrategy, DDCFieldAnalysisResult
from .ddc_pipeline_executor import DDCPipelineExecutor, DDCPipelineExecutionResult
from .ddc_update_crud import DDCUpdateCrud

logger = logging.getLogger(__name__)


@dataclass
class DDCStrategyExecutionResult:
    """DD-C策略执行结果"""
    success: bool = False
    strategy: DDCUpdateStrategy = DDCUpdateStrategy.SIMPLE_UPDATE
    updated_fields: Dict[str, Any] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    pipeline_executed: bool = False
    analysis_result: Optional[DDCFieldAnalysisResult] = None
    pipeline_result: Optional[DDCPipelineExecutionResult] = None


class DDCStrategyProcessor:
    """DD-C策略处理器 - 完全独立实现"""
    
    def __init__(self, update_crud: DDCUpdateCrud):
        """
        初始化DD-C策略处理器
        
        Args:
            update_crud: DD-C数据库操作组件
        """
        self.update_crud = update_crud
        self.field_analyzer = DDCFieldAnalyzer()
        self.pipeline_executor = DDCPipelineExecutor(update_crud)
        
        logger.debug("DD-C策略处理器初始化完成")
    
    async def process_strategy(
        self,
        update_fields: Dict[str, Any],
        original_data: Dict[str, Any]
    ) -> DDCStrategyExecutionResult:
        """
        处理策略执行
        
        Args:
            update_fields: 更新字段
            original_data: 原始记录数据
            
        Returns:
            策略执行结果
        """
        start_time = time.time()
        result = DDCStrategyExecutionResult()
        
        try:
            entry_id = original_data.get('submission_id', 'unknown')
            logger.debug(f"开始DD-C策略处理: entry_id={entry_id}, fields={list(update_fields.keys())}")
            
            # 1. 分析更新策略
            analysis_result = await self.field_analyzer.analyze_update_strategy(update_fields, original_data)
            result.analysis_result = analysis_result
            result.strategy = analysis_result.strategy
            
            logger.debug(f"策略分析完成: entry_id={entry_id}, strategy={analysis_result.strategy.value}, pipeline_required={analysis_result.pipeline_required}")
            
            # 2. 根据策略类型执行相应的处理
            if analysis_result.pipeline_required:
                # 执行Pipeline策略
                pipeline_result = await self.pipeline_executor.execute_pipeline_strategy(
                    original_data, 
                    analysis_result.strategy, 
                    analysis_result.pipeline_params,
                    analysis_result.override_fields
                )
                
                result.pipeline_result = pipeline_result
                result.pipeline_executed = True
                
                if pipeline_result.success:
                    # Pipeline执行成功，使用Pipeline结果
                    result.updated_fields = pipeline_result.updated_fields
                    result.success = True
                    logger.debug(f"Pipeline策略执行成功: entry_id={entry_id}, fields={list(pipeline_result.updated_fields.keys())}")
                else:
                    # Pipeline执行失败
                    result.error_message = pipeline_result.error_message
                    result.success = False
                    logger.warning(f"Pipeline策略执行失败: entry_id={entry_id}, error={pipeline_result.error_message}")
            else:
                # 执行简单更新策略
                # 如果分析器生成了override_fields，使用override_fields；否则使用原始update_fields
                final_update_fields = analysis_result.override_fields if analysis_result.override_fields else update_fields
                logger.debug(f"简单更新策略字段: entry_id={entry_id}, 原始字段={list(update_fields.keys())}, 最终字段={list(final_update_fields.keys())}")

                simple_result = await self.pipeline_executor.execute_simple_update(original_data, final_update_fields)
                
                result.pipeline_result = simple_result
                result.pipeline_executed = False
                
                if simple_result.success:
                    # 简单更新成功
                    result.updated_fields = simple_result.updated_fields
                    result.success = True
                    logger.debug(f"简单更新策略执行成功: entry_id={entry_id}, fields={list(simple_result.updated_fields.keys())}")
                else:
                    # 简单更新失败
                    result.error_message = simple_result.error_message
                    result.success = False
                    logger.warning(f"简单更新策略执行失败: entry_id={entry_id}, error={simple_result.error_message}")
            
            result.execution_time = time.time() - start_time
            logger.debug(f"DD-C策略处理完成: entry_id={entry_id}, success={result.success}, 耗时={result.execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            result.execution_time = time.time() - start_time
            result.error_message = f"策略处理异常: {e}"
            result.success = False
            
            logger.error(f"DD-C策略处理失败: entry_id={original_data.get('submission_id', 'unknown')}, error={e}", exc_info=True)
            return result
    
    async def validate_strategy_compatibility(
        self,
        update_fields: Dict[str, Any],
        original_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证策略兼容性
        
        Args:
            update_fields: 更新字段
            original_data: 原始记录数据
            
        Returns:
            验证结果
        """
        try:
            entry_id = original_data.get('submission_id', 'unknown')
            logger.debug(f"开始策略兼容性验证: entry_id={entry_id}")
            
            # 分析策略
            analysis_result = await self.field_analyzer.analyze_update_strategy(update_fields, original_data)
            
            validation_result = {
                'valid': True,
                'strategy': analysis_result.strategy.value,
                'pipeline_required': analysis_result.pipeline_required,
                'affected_fields': analysis_result.affected_fields,
                'reason': analysis_result.reason,
                'warnings': [],
                'errors': []
            }
            
            # 检查字段兼容性
            if analysis_result.strategy == DDCUpdateStrategy.SIMPLE_UPDATE:
                # 简单更新策略，检查字段有效性
                invalid_fields = []
                for field_name in update_fields.keys():
                    field_upper = field_name.upper()
                    if field_upper not in self.field_analyzer.ALL_SDR_FIELDS:
                        invalid_fields.append(field_name)
                
                if invalid_fields:
                    validation_result['warnings'].append(f"包含非SDR字段: {invalid_fields}")
            
            elif analysis_result.pipeline_required:
                # Pipeline策略，检查Pipeline参数有效性
                if not analysis_result.pipeline_params:
                    validation_result['valid'] = False
                    validation_result['errors'].append("Pipeline策略缺少必要参数")
                elif not analysis_result.pipeline_params.candidate_columns:
                    validation_result['warnings'].append("Pipeline参数中candidate_columns为空")
            
            # 检查Range聚合需求
            if hasattr(analysis_result, 'requires_range_check') and analysis_result.requires_range_check:
                validation_result['requires_range_check'] = True
                validation_result['warnings'].append("此更新可能触发Range聚合")
            
            logger.debug(f"策略兼容性验证完成: entry_id={entry_id}, valid={validation_result['valid']}")
            return validation_result
            
        except Exception as e:
            logger.error(f"策略兼容性验证失败: entry_id={original_data.get('submission_id', 'unknown')}, error={e}")
            return {
                'valid': False,
                'strategy': 'unknown',
                'pipeline_required': False,
                'affected_fields': [],
                'reason': f"验证异常: {e}",
                'warnings': [],
                'errors': [str(e)]
            }
    
    async def get_strategy_preview(
        self,
        update_fields: Dict[str, Any],
        original_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        获取策略预览信息
        
        Args:
            update_fields: 更新字段
            original_data: 原始记录数据
            
        Returns:
            策略预览信息
        """
        try:
            entry_id = original_data.get('submission_id', 'unknown')
            logger.debug(f"获取策略预览: entry_id={entry_id}")
            
            # 分析策略
            analysis_result = await self.field_analyzer.analyze_update_strategy(update_fields, original_data)
            
            preview = {
                'entry_id': entry_id,
                'strategy': analysis_result.strategy.value,
                'strategy_description': self._get_strategy_description(analysis_result.strategy),
                'pipeline_required': analysis_result.pipeline_required,
                'affected_fields': analysis_result.affected_fields,
                'reason': analysis_result.reason,
                'estimated_execution_time': self._estimate_execution_time(analysis_result),
                'complexity_level': self._get_complexity_level(analysis_result),
                'risk_level': self._get_risk_level(analysis_result)
            }
            
            # 添加Pipeline相关信息
            if analysis_result.pipeline_required and analysis_result.pipeline_params:
                preview['pipeline_info'] = {
                    'type': analysis_result.pipeline_params.pipeline_type,
                    'candidate_tables': list(analysis_result.pipeline_params.candidate_columns.keys()) if analysis_result.pipeline_params.candidate_columns else [],
                    'has_fixed_fields': bool(analysis_result.pipeline_params.fixed_fields),
                    'has_override_fields': bool(analysis_result.override_fields)
                }
            
            logger.debug(f"策略预览生成完成: entry_id={entry_id}, strategy={analysis_result.strategy.value}")
            return preview
            
        except Exception as e:
            logger.error(f"获取策略预览失败: entry_id={original_data.get('submission_id', 'unknown')}, error={e}")
            return {
                'entry_id': original_data.get('submission_id', 'unknown'),
                'strategy': 'unknown',
                'strategy_description': '策略分析失败',
                'pipeline_required': False,
                'affected_fields': [],
                'reason': f"预览生成异常: {e}",
                'estimated_execution_time': 0,
                'complexity_level': 'unknown',
                'risk_level': 'high'
            }
    
    def _get_strategy_description(self, strategy: DDCUpdateStrategy) -> str:
        """获取策略描述"""
        descriptions = {
            DDCUpdateStrategy.SIMPLE_UPDATE: "简单字段更新，直接修改数据库字段",
            DDCUpdateStrategy.SDR05_PIPELINE: "表英文名修改，触发Pipeline生成相关字段",
            DDCUpdateStrategy.SDR08_PIPELINE: "字段映射修改，触发Pipeline生成完整映射信息",
            DDCUpdateStrategy.SDR10_PIPELINE: "SQL语句修改，触发Pipeline解析表和字段信息",
            DDCUpdateStrategy.SDR05_SDR08_COMPOSITE: "表名和字段映射组合修改，部分固定部分Pipeline生成",
            DDCUpdateStrategy.SDR08_SDR10_COMPOSITE: "字段映射和SQL组合修改，字段映射固定SQL解析",
            DDCUpdateStrategy.SDR05_SDR10_COMPOSITE: "表名和SQL组合修改，表名固定SQL解析",
            DDCUpdateStrategy.RANGE_AGGREGATION: "Range聚合处理，影响多个相关记录"
        }
        return descriptions.get(strategy, "未知策略")
    
    def _estimate_execution_time(self, analysis_result: DDCFieldAnalysisResult) -> float:
        """估算执行时间（秒）"""
        if not analysis_result.pipeline_required:
            return 0.1  # 简单更新很快
        
        # Pipeline策略根据复杂度估算
        base_time = 2.0  # 基础Pipeline时间
        
        if analysis_result.pipeline_params and analysis_result.pipeline_params.candidate_columns:
            table_count = len(analysis_result.pipeline_params.candidate_columns)
            field_count = sum(len(fields) for fields in analysis_result.pipeline_params.candidate_columns.values())
            
            # 根据表和字段数量调整时间
            base_time += table_count * 0.5 + field_count * 0.1
        
        # 组合策略时间更长
        if analysis_result.strategy.value.endswith('_COMPOSITE'):
            base_time *= 1.5
        
        return round(base_time, 1)
    
    def _get_complexity_level(self, analysis_result: DDCFieldAnalysisResult) -> str:
        """获取复杂度级别"""
        if not analysis_result.pipeline_required:
            return 'low'
        
        if analysis_result.strategy.value.endswith('_COMPOSITE'):
            return 'high'
        
        return 'medium'
    
    def _get_risk_level(self, analysis_result: DDCFieldAnalysisResult) -> str:
        """获取风险级别"""
        if not analysis_result.pipeline_required:
            return 'low'
        
        # 组合策略风险较高
        if analysis_result.strategy.value.endswith('_COMPOSITE'):
            return 'medium'
        
        # 需要Range检查的风险较高
        if hasattr(analysis_result, 'requires_range_check') and analysis_result.requires_range_check:
            return 'medium'
        
        return 'low'
