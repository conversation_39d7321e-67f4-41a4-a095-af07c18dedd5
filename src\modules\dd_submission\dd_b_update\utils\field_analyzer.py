"""
字段分析器 - 分析更新字段并确定处理策略
"""

import json
import logging
from typing import Dict, List, Any, Optional, Set
from ..models.update_models import (
    UpdateStrategy, UpdateItem, FieldAnalysisResult,
    PipelineExecutionParams, BDR16Parser
)

logger = logging.getLogger(__name__)


class FieldAnalyzer:
    """字段分析器"""
    
    # Pipeline相关字段
    PIPELINE_FIELDS = {'BDR09', 'BDR10', 'BDR11', 'BDR16'}
    
    # 简单更新字段（非Pipeline生成）
    SIMPLE_FIELDS = {
        'BDR01', 'BDR02', 'BDR03', 'BDR04', 'BDR05', 'BDR06', 'BDR07', 'BDR08',
        'BDR12', 'BDR13', 'BDR14', 'BDR15', 'BDR17',
        'SDR01', 'SDR02', 'SDR03', 'SDR04', 'SDR07', 'SDR10', 'SDR11', 
        'SDR13', 'SDR14', 'SDR15'
    }
    
    def __init__(self):
        """初始化字段分析器"""
        pass
    
    def analyze_update_strategy(self, update_item: UpdateItem) -> FieldAnalysisResult:
        """
        分析更新策略
        
        Args:
            update_item: 更新项目
            
        Returns:
            字段分析结果
        """
        pipeline_fields = update_item.get_pipeline_fields()
        simple_fields = update_item.get_simple_fields()

        logger.debug(f"分析entry_id={update_item.entry_id}: pipeline_fields={pipeline_fields}, simple_fields={simple_fields}")

        # 检查Pipeline字段是否有空值（需要直接处理，不经过Pipeline）
        empty_pipeline_fields = set()
        non_empty_pipeline_fields = set()

        for field_name in pipeline_fields:
            field_value = pipeline_fields[field_name]
            if field_value == '' or field_value is None:
                empty_pipeline_fields.add(field_name)
            else:
                non_empty_pipeline_fields.add(field_name)

        # 如果所有Pipeline字段都是空值，使用简单更新
        if pipeline_fields and not non_empty_pipeline_fields:
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(pipeline_fields.keys()),
                reason="所有Pipeline字段都为空值，直接更新"
            )

        # 如果有混合情况（有空值有非空值），使用简单更新
        if empty_pipeline_fields and non_empty_pipeline_fields:
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(pipeline_fields.keys()),
                reason="Pipeline字段混合空值和非空值，直接更新"
            )

        # 按优先级判断策略
        # 优先级：三字段同时修改 > BDR11 > BDR09 > BDR16 > BDR10 > 简单字段

        logger.debug(f"🔍 策略分析开始: entry_id={update_item.entry_id}")
        logger.debug(f"  Pipeline字段: {list(pipeline_fields.keys())}")
        logger.debug(f"  简单字段: {list(simple_fields.keys())}")

        # 0. 检查BDR09/11/16三字段同时修改（最高优先级）
        bdr_core_fields = {'BDR09', 'BDR11', 'BDR16'}
        pipeline_field_keys = set(pipeline_fields.keys())
        if bdr_core_fields.issubset(pipeline_field_keys):
            # 检查三个字段都不为空
            bdr09_value = pipeline_fields.get('BDR09', '').strip()
            bdr11_value = pipeline_fields.get('BDR11', '').strip()
            bdr16_value = pipeline_fields.get('BDR16', '').strip()

            if bdr09_value and bdr11_value and bdr16_value:
                logger.debug("  → 检测到BDR09/11/16三字段同时修改且都不为空，使用三字段组合策略")
                return self._analyze_three_bdr_fields_strategy(update_item, pipeline_fields)

        # 1. 检查BDR11修改（最高优先级）
        if 'BDR11' in pipeline_fields:
            logger.debug("  → 检测到BDR11修改，使用BDR11策略")
            return self._analyze_bdr11_strategy(update_item, pipeline_fields)

        # 2. 检查BDR09修改
        if 'BDR09' in pipeline_fields:
            logger.debug("  → 检测到BDR09修改，使用BDR09策略")
            return self._analyze_bdr09_strategy(update_item, pipeline_fields)

        # 3. 检查BDR16修改（需要解析表范围）
        if 'BDR16' in pipeline_fields:
            logger.debug("  → 检测到BDR16修改，使用BDR16策略")
            return self._analyze_bdr16_strategy(update_item, pipeline_fields)
        
        # 4. 检查BDR10修改（特殊处理）
        if 'BDR10' in pipeline_fields:
            return FieldAnalysisResult(
                strategy=UpdateStrategy.BDR10_UPDATE,
                pipeline_required=False,
                affected_fields=['BDR10'],
                reason="BDR10中文名修改，直接更新"
            )
        
        # 5. 简单字段修改（最低优先级）
        if simple_fields:
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(simple_fields.keys()),
                reason="非Pipeline字段修改，直接更新"
            )
        
        # 如果没有任何字段需要更新
        return FieldAnalysisResult(
            strategy=UpdateStrategy.SIMPLE_UPDATE,
            pipeline_required=False,
            affected_fields=[],
            reason="没有字段需要更新"
        )
    
    def _analyze_bdr09_strategy(self, update_item: UpdateItem, pipeline_fields: Dict[str, Any]) -> FieldAnalysisResult:
        """分析BDR09修改策略"""
        try:
            bdr09_value = pipeline_fields['BDR09']
            
            # 解析BDR09为candidate_tables格式
            candidate_tables = self._parse_bdr09_to_candidate_tables(bdr09_value)
            if not candidate_tables:
                return FieldAnalysisResult(
                    strategy=UpdateStrategy.SIMPLE_UPDATE,
                    pipeline_required=False,
                    affected_fields=['BDR09'],
                    reason="BDR09格式无法解析，降级为简单更新"
                )
            
            # 直接解析BDR09的JSON字符串为表名列表
            import json
            try:
                table_names = json.loads(bdr09_value)
                if not isinstance(table_names, list):
                    table_names = [table_names]  # 如果不是列表，转为列表
            except (json.JSONDecodeError, TypeError):
                # 如果解析失败，尝试简单分割
                table_names = [bdr09_value] if bdr09_value else []

            # 从candidate_tables推导table_ids（保持兼容性）
            table_ids = self._extract_table_ids_from_candidate_tables(candidate_tables)

            # 构建Pipeline参数
            pipeline_params = PipelineExecutionParams(
                user_question=update_item.original_data.get('dr09', ''),
                hint=update_item.original_data.get('dr17', ''),
                candidate_tables=candidate_tables,
                table_ids=table_ids,
                schema_generation_params={
                    "table_names": table_names,  # 直接使用解析的表名列表
                    "is_final": False,
                    "source_type": "source"
                }
            )
            
            return FieldAnalysisResult(
                strategy=UpdateStrategy.COLUMN_SELECTION,
                pipeline_required=True,
                affected_fields=['BDR09', 'BDR10', 'BDR11', 'BDR16', 'SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR12'],
                pipeline_params=pipeline_params,
                reason="BDR09修改，需要从字段选择开始执行Pipeline"
            )
            
        except Exception as e:
            logger.error(f"分析BDR09策略失败: {e}")
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=['BDR09'],
                reason=f"BDR09分析失败: {e}"
            )
    
    def _analyze_bdr11_strategy(self, update_item: UpdateItem, pipeline_fields: Dict[str, Any]) -> FieldAnalysisResult:
        """分析BDR11修改策略"""
        try:
            bdr11_value = pipeline_fields['BDR11']
            
            # 解析BDR11为candidate_columns格式
            candidate_columns = self._parse_bdr11_to_candidate_columns(bdr11_value)
            if not candidate_columns:
                return FieldAnalysisResult(
                    strategy=UpdateStrategy.SIMPLE_UPDATE,
                    pipeline_required=False,
                    affected_fields=['BDR11'],
                    reason="BDR11格式无法解析，降级为简单更新"
                )
            
            # 从candidate_columns推导table_ids
            table_ids = list(candidate_columns.keys())



            # 构建Pipeline参数
            pipeline_params = PipelineExecutionParams(
                user_question=update_item.original_data.get('dr09', ''),
                hint=update_item.original_data.get('dr17', ''),
                candidate_columns=candidate_columns,
                table_ids=table_ids,
                schema_generation_params={
                    "candidate_columns": candidate_columns,  # SQL生成策略需要candidate_columns
                    "is_final": True,
                    "source_type": "source"
                }
            )
            
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SQL_GENERATION,
                pipeline_required=True,
                affected_fields=['BDR16', 'SDR09', 'SDR12'],  # 保持BDR09, BDR11, SDR05, SDR08不变
                pipeline_params=pipeline_params,
                reason="BDR11修改，需要从SQL生成开始执行Pipeline"
            )
            
        except Exception as e:
            logger.error(f"分析BDR11策略失败: {e}")
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=['BDR11'],
                reason=f"BDR11分析失败: {e}"
            )
    
    def _analyze_bdr16_strategy(self, update_item: UpdateItem, pipeline_fields: Dict[str, Any]) -> FieldAnalysisResult:
        """分析BDR16修改策略"""
        try:
            old_bdr16 = update_item.original_data.get('bdr16', '')
            new_bdr16 = pipeline_fields['BDR16']

            logger.debug(f"🔍 BDR16策略分析: entry_id={update_item.entry_id}")
            logger.debug(f"  原始BDR16: {repr(old_bdr16)}")
            logger.debug(f"  新BDR16: {repr(new_bdr16)}")

            # 比较表范围是否变化
            ranges_equal = BDR16Parser.compare_table_ranges(old_bdr16, new_bdr16)
            logger.debug(f"  表范围比较结果: {ranges_equal}")

            if ranges_equal:
                # 表范围没有变化，直接更新
                logger.debug("  → 表范围未变化，使用SIMPLE_UPDATE策略")
                return FieldAnalysisResult(
                    strategy=UpdateStrategy.SIMPLE_UPDATE,
                    pipeline_required=False,
                    affected_fields=['BDR16'],
                    reason="BDR16表范围未变化，直接更新"
                )
            else:
                # 表范围变化了，按BDR09策略处理
                logger.debug("  → 表范围发生变化，开始解析新表范围")
                new_table_list = BDR16Parser.extract_table_range(new_bdr16)
                logger.debug(f"  解析出的表列表: {new_table_list}")

                if not new_table_list:
                    logger.warning("  → 表范围解析失败，降级为SIMPLE_UPDATE")
                    return FieldAnalysisResult(
                        strategy=UpdateStrategy.SIMPLE_UPDATE,
                        pipeline_required=False,
                        affected_fields=['BDR16'],
                        reason="BDR16表范围解析失败，直接更新"
                    )
                
                # 构建candidate_tables（假设都在同一个数据库中）
                candidate_tables = {"source": new_table_list}
                table_ids = new_table_list
                table_names = new_table_list  # BDR16解析出的表名列表

                logger.debug(f"  → 构建COLUMN_SELECTION策略参数:")
                logger.debug(f"    candidate_tables: {candidate_tables}")
                logger.debug(f"    table_ids: {table_ids}")
                logger.debug(f"    table_names: {table_names}")

                # 构建Pipeline参数
                pipeline_params = PipelineExecutionParams(
                    user_question=update_item.original_data.get('dr09', ''),
                    hint=update_item.original_data.get('dr17', ''),
                    candidate_tables=candidate_tables,
                    table_ids=table_ids,
                    schema_generation_params={
                        "table_names": table_names,  # 字段选择策略需要table_names
                        "is_final": False,
                        "source_type": "source"
                    }
                )

                logger.debug("  → 返回COLUMN_SELECTION策略")
                return FieldAnalysisResult(
                    strategy=UpdateStrategy.COLUMN_SELECTION,
                    pipeline_required=True,
                    affected_fields=['BDR09', 'BDR10', 'BDR11', 'BDR16', 'SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR12'],
                    pipeline_params=pipeline_params,
                    reason="BDR16表范围变化，按BDR09策略处理"
                )
                
        except Exception as e:
            logger.error(f"分析BDR16策略失败: {e}")
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=['BDR16'],
                reason=f"BDR16分析失败: {e}"
            )
    
    def _parse_bdr09_to_candidate_tables(self, bdr09_value: Any) -> Optional[Dict[str, List[str]]]:
        """将BDR09值解析为candidate_tables格式"""
        try:
            if isinstance(bdr09_value, str):
                table_list = json.loads(bdr09_value)
            elif isinstance(bdr09_value, list):
                table_list = bdr09_value
            else:
                return None
            
            if not isinstance(table_list, list):
                return None
            
            # 假设所有表都在source数据库中
            return {"source": table_list}
            
        except Exception as e:
            logger.error(f"解析BDR09失败: {e}")
            return None
    
    def _parse_bdr11_to_candidate_columns(self, bdr11_value: Any) -> Optional[Dict[str, List[str]]]:
        """将BDR11值解析为candidate_columns格式"""
        try:
            if isinstance(bdr11_value, str):
                columns_dict = json.loads(bdr11_value)
            elif isinstance(bdr11_value, dict):
                columns_dict = bdr11_value
            else:
                return None
            
            if not isinstance(columns_dict, dict):
                return None
            
            return columns_dict
            
        except Exception as e:
            logger.error(f"解析BDR11失败: {e}")
            return None
    
    def _extract_table_ids_from_candidate_tables(self, candidate_tables: Dict[str, List[str]]) -> List[str]:
        """从candidate_tables中提取table_ids"""
        table_ids = []
        for db_name, tables in candidate_tables.items():
            table_ids.extend(tables)
        return table_ids

    def _analyze_three_bdr_fields_strategy(self, update_item: UpdateItem, pipeline_fields: Dict[str, Any]) -> FieldAnalysisResult:
        """
        分析BDR09/11/16三字段同时修改策略

        类似DD-C的SDR05/08/10三字段同时修改逻辑：
        1. 复用BDR11策略的保留字段生成逻辑
        2. 复用BDR09策略的保留字段生成逻辑
        3. BDR09覆盖合并BDR11的字段
        4. BDR16覆盖合并前面的字段
        5. 不走Pipeline，使用SIMPLE_UPDATE策略
        """
        try:
            bdr09_value = pipeline_fields['BDR09']
            bdr11_value = pipeline_fields['BDR11']
            bdr16_value = pipeline_fields['BDR16']

            logger.debug(f"🔍 三字段同时修改策略分析: entry_id={update_item.entry_id}")
            logger.debug(f"  BDR09: {repr(bdr09_value)}")
            logger.debug(f"  BDR11: {repr(bdr11_value)}")
            logger.debug(f"  BDR16: {repr(bdr16_value)}")

            # 1. 复用BDR11策略生成保留字段
            bdr11_fields = self._get_bdr11_strategy_fields(bdr11_value)
            logger.debug(f"  BDR11策略字段: {list(bdr11_fields.keys()) if bdr11_fields else 'None'}")

            # 2. 复用BDR09策略生成保留字段
            bdr09_fields = self._get_bdr09_strategy_fields(bdr09_value)
            logger.debug(f"  BDR09策略字段: {list(bdr09_fields.keys()) if bdr09_fields else 'None'}")

            # 3. 按优先级合并字段：BDR09覆盖BDR11
            all_override_fields = {}
            if bdr11_fields:
                all_override_fields.update(bdr11_fields)
            if bdr09_fields:
                all_override_fields.update(bdr09_fields)  # BDR09优先覆盖BDR11

            # 4. 添加BDR16字段（不会与前面字段冲突）
            if bdr16_value:
                all_override_fields['BDR16'] = bdr16_value

            logger.debug(f"  合并后字段: {list(all_override_fields.keys())}")

            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(all_override_fields.keys()),
                override_fields=all_override_fields,
                reason=f"同时修改3个轴心字段且都不为空: {{'BDR09', 'BDR11', 'BDR16'}}，生成保留字段并集"
            )

        except Exception as e:
            logger.error(f"三字段同时修改策略分析失败: {e}")
            return FieldAnalysisResult(
                strategy=UpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=['BDR09', 'BDR11', 'BDR16'],
                reason=f"三字段分析失败: {e}"
            )

    def _get_bdr11_strategy_fields(self, bdr11_value: str) -> Dict[str, Any]:
        """
        复用BDR11策略的保留字段生成逻辑

        Args:
            bdr11_value: BDR11字段值

        Returns:
            BDR11策略生成的保留字段字典
        """
        try:
            # BDR11策略生成的字段：BDR11本身 + 对应的SDR08
            fields = {}

            # 添加BDR11字段
            fields['BDR11'] = bdr11_value

            # 添加对应的SDR08字段（BDR11 → SDR08映射）
            fields['SDR08'] = bdr11_value

            logger.debug(f"BDR11策略生成字段: {list(fields.keys())}")
            return fields

        except Exception as e:
            logger.error(f"BDR11策略字段生成失败: {e}")
            return {}

    def _get_bdr09_strategy_fields(self, bdr09_value: str) -> Dict[str, Any]:
        """
        复用BDR09策略的保留字段生成逻辑

        Args:
            bdr09_value: BDR09字段值

        Returns:
            BDR09策略生成的保留字段字典
        """
        try:
            # BDR09策略生成的字段：BDR09本身 + 对应的SDR05 + 生成的BDR10和SDR06
            fields = {}

            # 添加BDR09字段
            fields['BDR09'] = bdr09_value

            # 添加对应的SDR05字段（BDR09 → SDR05映射）
            fields['SDR05'] = bdr09_value

            # 生成BDR10和SDR06（表中文名）
            try:
                import json
                table_names = json.loads(bdr09_value)
                if not isinstance(table_names, list):
                    table_names = [table_names]

                # 生成中文名（简化版本，实际应该查询数据库）
                chinese_names = [f"{name}_中文名" for name in table_names]
                chinese_names_json = json.dumps(chinese_names, ensure_ascii=False)

                fields['BDR10'] = chinese_names_json
                fields['SDR06'] = chinese_names_json

            except (json.JSONDecodeError, TypeError):
                # 如果解析失败，使用默认值
                fields['BDR10'] = f"{bdr09_value}_中文名"
                fields['SDR06'] = f"{bdr09_value}_中文名"

            logger.debug(f"BDR09策略生成字段: {list(fields.keys())}")
            return fields

        except Exception as e:
            logger.error(f"BDR09策略字段生成失败: {e}")
            return {}
