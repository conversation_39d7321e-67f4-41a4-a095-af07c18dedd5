[project]
name = "hsbc-langflow"
version = "0.1.0"
description = "Add support for langflow"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiomysql>=0.2.0",
    "aiopg>=1.4.0",
    "aiosqlite>=0.21.0",
    "asyncpg>=0.30.0",
    "cachetools>=6.1.0",
    "dateparser>=1.2.1",
    "dbutils>=3.1.0",
    "dotenv>=0.9.9",
    "fastapi>=0.115.12",
    "func-timeout>=4.3.5",
    "hydra-core>=1.3.2",
    "jieba>=0.42.1",
    "jionlp>=1.5.23",
    "langchain>=0.3.25",
    "llama-index>=0.12.36",
    "loguru>=0.7.3",
    "mysql>=0.0.3",
    "mysql-connector-python>=9.3.0",
    "omegaconf>=2.3.0",
    "openai>=1.79.0",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "psutil>=7.0.0",
    "psycopg2>=2.9.10",
    "pydantic>=2.11.4",
    "pydantic-extra-types>=2.10.4",
    "pymilvus>=2.5.9",
    "pymysql>=1.1.1",
    "python-dotenv>=1.1.0",
    "httpx>=0.27.0",
    "scikit-learn>=1.3.0",
    "shortuuid>=1.0.13",
    "sqlalchemy>=2.0.41",
    "sqlglot>=26.17.1",
    "structlog>=25.4.0",
    "uvicorn>=0.34.2",
    "python-multipart>=0.0.20",
    "python-docx>=1.2.0",
    "pypdf2>=3.0.1",
]

