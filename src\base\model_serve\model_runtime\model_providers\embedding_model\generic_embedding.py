import time
import logging
import psutil
from typing import Optional, List, Dict, Any
import aiohttp
import requests
from decimal import Decimal
from pydantic import PrivateAttr, Field

logger = logging.getLogger(__name__)

from ..__base.text_embedding_model import TextEmbeddingModel
from ...entities.text_embedding_entities import TextEmbeddingResult, EmbeddingUsage
from ...utils.http_session_manager import (
    HTTPSessionManager, HTTPSessionConfig, HTTPConnectionManager,
    HTTPMetrics, HTTPCache, get_session_manager
)


# 旧的EmbeddingMetrics和EmbeddingConnectionManager类已移除
# 现在使用统一的HTTPMetrics和HTTPConnectionManager

# 保留向后兼容的别名（如果需要）
# EmbeddingMetrics = HTTPMetrics
# EmbeddingConnectionManager = HTTPConnectionManager

# 旧的EmbeddingSessionManager类已移除，现在使用统一的HTTPSessionManager


# 旧的EmbeddingCache类也已移除，现在使用统一的HTTPCache


class GenericEmbedding(TextEmbeddingModel):
    """
    高性能通用文本嵌入模型实现 - 支持批量处理、缓存、重试和性能监控
    """

    base_url: str = Field(..., description="The base URL of the embedding model API.")
    api_key: Optional[str] = Field(None, description="The API key for authentication.")
    model_name: str = Field(..., description="The default model name for this embedding instance.")
    provider: str = Field(..., description="The provider name for this embedding instance.")

    # 性能优化配置
    batch_size: int = Field(default=32, description="Batch size for processing multiple texts")
    enable_cache: bool = Field(default=True, description="Enable embedding caching")
    cache_size: int = Field(default=10000, description="Maximum cache size")
    max_memory_mb: int = Field(default=1000, description="Maximum memory usage in MB")

    # 🔧 并发处理配置
    enable_batch_concurrency: bool = Field(default=True, description="Enable concurrent batch processing for better performance")
    max_concurrent_batches: int = Field(default=5, description="Maximum number of concurrent batches (0 = auto-calculate based on connection pool)")
    batch_retry_attempts: int = Field(default=2, description="Number of retry attempts for failed batches")

    # 内部状态，使用 PrivateAttr 避免 Pydantic 验证
    _headers: Dict[str, str] = PrivateAttr()
    _session_manager: HTTPSessionManager = PrivateAttr()
    _connection_manager: HTTPConnectionManager = PrivateAttr()
    _metrics: HTTPMetrics = PrivateAttr()
    _cache: Optional[HTTPCache] = PrivateAttr(default=None)
    _instance_id: str = PrivateAttr()

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)

        # 初始化私有属性
        self._instance_id = f"embedding_{id(self)}"

        # 创建HTTP会话配置 - 针对embedding API优化
        session_config = HTTPSessionConfig.for_embedding(
            limit=100,
            limit_per_host=30,
            total_timeout=30.0,      # 大幅降低超时时间
            sock_read_timeout=15.0,  # 读取超时
            sock_connect_timeout=5.0, # 连接超时
            max_retries=2,           # 降低重试次数
            base_timeout=10.0,       # 基础超时时间
            retry_delay=0.3          # 更短的重试延迟
        )

        # 获取统一的HTTP会话管理器
        self._session_manager = get_session_manager(
            config=session_config,
            instance_id=self._instance_id,
            model_type="embedding"
        )

        # 初始化连接管理器和指标
        self._connection_manager = self._session_manager.get_connection_manager()
        self._metrics = HTTPMetrics()

        # 设置请求头
        self._headers = {"Content-Type": "application/json"}
        if self.api_key:
            self._headers["Authorization"] = f"Bearer {self.api_key}"

        # 初始化缓存
        if self.enable_cache:
            self._cache = HTTPCache(max_size=self.cache_size)

        logger.debug(f"GenericEmbedding initialized: model={self.model_name}, batch_size={self.batch_size}, session_manager={self._session_manager.instance_id}")

    @property
    def async_session(self) -> aiohttp.ClientSession:
        """通过统一HTTP会话管理器获取session"""
        return self._session_manager.async_session

    @property
    def sync_session(self) -> requests.Session:
        """通过统一HTTP会话管理器获取同步session"""
        return self._session_manager.sync_session

    def invoke(
        self,
        texts: list[str],
        model: Optional[str] = None,
        credentials: Optional[dict] = None,
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        调用文本嵌入模型 - 支持批量处理和缓存
        """
        model = model or self.model_name
        
        try:
            return self.invoke_text_embedding(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'generic'),
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            self._metrics.record_error()
            raise self._transform_invoke_error(e)

    async def ainvoke(
        self,
        texts: list[str],
        model: Optional[str] = None,
        credentials: Optional[dict] = None,
        user: Optional[str] = None,
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型 - 支持批量处理和缓存
        """
        model = model or self.model_name
        
        try:
            return await self.ainvoke_text_embedding(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'generic'),
                model=model,
                credentials=credentials,
                texts=texts,
            )
        except Exception as e:
            self._metrics.record_error()
            raise self._transform_invoke_error(e)

    def invoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        同步调用文本嵌入模型 - 支持批量处理、缓存和重试
        """
        start_time = time.perf_counter()
        
        # 内存检查
        self._check_memory_usage(texts)
        
        # 处理缓存 - 分离已缓存和未缓存的文本
        cached_embeddings = {}
        uncached_texts = []
        uncached_indices = []
        
        if self._cache:
            for i, text in enumerate(texts):
                cached_emb = self._cache.get(text, model)
                if cached_emb:
                    cached_embeddings[i] = cached_emb
                    self._metrics.record_cache_hit()
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
                    self._metrics.record_cache_miss()
        else:
            uncached_texts = texts
            uncached_indices = list(range(len(texts)))
        
        # 批量处理未缓存的文本
        all_embeddings = [None] * len(texts)
        
        # 填充缓存的嵌入
        for idx, embedding in cached_embeddings.items():
            all_embeddings[idx] = embedding
        
        # 批量请求未缓存的文本
        if uncached_texts:
            uncached_embeddings = self._process_texts_in_batches_sync(uncached_texts, model)
            
            # 填充新获取的嵌入并更新缓存
            for i, embedding in enumerate(uncached_embeddings):
                original_idx = uncached_indices[i]
                all_embeddings[original_idx] = embedding
                
                if self._cache:
                    self._cache.put(uncached_texts[i], model, embedding)
        
        # 计算使用情况和指标
        latency = time.perf_counter() - start_time
        token_count = sum(len(text) for text in texts)  # 简化的token计算
        
        self._metrics.record_request(len(texts), token_count, latency)
        self._metrics.update_memory_peak()
        
        usage = self._calc_embedding_usage(model, credentials, texts)
        usage.latency = latency

        return TextEmbeddingResult(
            model=model,
            embeddings=all_embeddings,
            usage=usage
        )

    async def ainvoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型 - 支持批量处理、缓存和重试
        """
        start_time = time.perf_counter()

        # 内存检查
        self._check_memory_usage(texts)
        
        # 处理缓存 - 分离已缓存和未缓存的文本
        cached_embeddings = {}
        uncached_texts = []
        uncached_indices = []
        
        if self._cache:
            for i, text in enumerate(texts):
                cached_emb = self._cache.get(text, model)
                if cached_emb:
                    cached_embeddings[i] = cached_emb
                    self._metrics.record_cache_hit()
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
                    self._metrics.record_cache_miss()
        else:
            uncached_texts = texts
            uncached_indices = list(range(len(texts)))
        
        # 批量处理未缓存的文本
        all_embeddings = [None] * len(texts)
        
        # 填充缓存的嵌入
        for idx, embedding in cached_embeddings.items():
            all_embeddings[idx] = embedding
        
        # 批量请求未缓存的文本
        if uncached_texts:
            uncached_embeddings = await self._process_texts_in_batches_async(uncached_texts, model)
            
            # 填充新获取的嵌入并更新缓存
            for i, embedding in enumerate(uncached_embeddings):
                original_idx = uncached_indices[i]
                all_embeddings[original_idx] = embedding
                
                if self._cache:
                    self._cache.put(uncached_texts[i], model, embedding)
        
        # 计算使用情况和指标
        latency = time.perf_counter() - start_time
        token_count = sum(len(text) for text in texts)
        
        self._metrics.record_request(len(texts), token_count, latency)
        self._metrics.update_memory_peak()
        
        usage = self._calc_embedding_usage(model, credentials, texts)
        usage.latency = latency

        return TextEmbeddingResult(
            model=model,
            embeddings=all_embeddings,
            usage=usage
        )

    def _process_texts_in_batches_sync(self, texts: List[str], model: str) -> List[List[float]]:
        """同步批量处理文本嵌入"""
        all_embeddings = []
        
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            self._metrics.record_batch()
            
            payload = {
                "input": batch_texts,
                "model": model
            }
            
            try:
                response_data = self._connection_manager.make_sync_request_with_retry(
                    self.sync_session, self.base_url, self._headers, payload
                )
                
                batch_embeddings = [item['embedding'] for item in response_data.get('data', [])]
                all_embeddings.extend(batch_embeddings)
                
            except Exception as e:
                logger.error(f"Failed to process batch {i//self.batch_size + 1}: {str(e)}")
                self._metrics.record_error()
                raise Exception(f"Failed to get embeddings for batch: {str(e)}") from e
        
        return all_embeddings

    async def _process_texts_in_batches_async(self, texts: List[str], model: str) -> List[List[float]]:
        """异步批量处理文本嵌入 - 支持批次间并发处理"""
        import asyncio

        logger.debug(f"Processing {len(texts)} texts in batches of {self.batch_size}")

        # 🔧 检查是否启用并发处理
        if not self.enable_batch_concurrency:
            logger.debug("Batch concurrency disabled, using sequential processing")
            return await self._process_texts_sequentially(texts, model)

        # 🔧 计算并发度
        total_batches = (len(texts) + self.batch_size - 1) // self.batch_size
        if self.max_concurrent_batches <= 0:
            # 自动计算：基于连接池限制，但不超过总批次数
            auto_concurrent = min(
                max(1, self._session_manager.config.limit_per_host // 3),  # 连接池的1/3
                total_batches,  # 不超过总批次数
                8  # 硬性上限
            )
            concurrent_batches = auto_concurrent
        else:
            concurrent_batches = min(self.max_concurrent_batches, total_batches)

        logger.debug(f"Using concurrent processing: {concurrent_batches} concurrent batches for {total_batches} total batches")

        # 🔧 准备批次数据
        batch_infos = []
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            start_idx = i
            batch_infos.append((batch_num, batch_texts, start_idx, model))

        # 🔧 并发处理批次
        all_embeddings = [None] * len(texts)  # 预分配结果数组，保持顺序

        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(concurrent_batches)

        async def process_batch_with_control(batch_info):
            """带并发控制的批次处理"""
            async with semaphore:
                return await self._process_single_batch_with_retry(*batch_info)

        # 🔧 并发执行所有批次
        try:
            start_time = time.perf_counter()
            tasks = [process_batch_with_control(batch_info) for batch_info in batch_infos]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.perf_counter() - start_time

            # 🔧 处理结果和错误
            successful_batches = 0
            failed_batches = 0

            for i, (batch_info, result) in enumerate(zip(batch_infos, batch_results)):
                batch_num, batch_texts, start_idx, _ = batch_info

                if isinstance(result, Exception):
                    failed_batches += 1
                    logger.error(f"Failed to process async batch {batch_num} after retries: {str(result)}")
                    raise Exception(f"Failed to get embeddings for async batch {batch_num}: {str(result)}") from result
                else:
                    successful_batches += 1
                    batch_embeddings = result
                    # 🔧 保持结果顺序
                    end_idx = start_idx + len(batch_embeddings)
                    all_embeddings[start_idx:end_idx] = batch_embeddings

            logger.debug(f"Concurrent processing completed in {total_time:.3f}s: {successful_batches} successful, {failed_batches} failed")

            # 🔧 移除None值并展平结果
            final_embeddings = [emb for emb in all_embeddings if emb is not None]
            logger.debug(f"All batches completed, total embeddings: {len(final_embeddings)}")
            return final_embeddings

        except Exception as e:
            logger.error(f"Concurrent batch processing failed: {str(e)}")
            raise

    async def _process_texts_sequentially(self, texts: List[str], model: str) -> List[List[float]]:
        """串行处理文本嵌入 - 保持向后兼容的原始逻辑"""
        all_embeddings = []

        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            batch_num = i//self.batch_size + 1
            self._metrics.record_batch()

            payload = {
                "input": batch_texts,
                "model": model
            }

            logger.debug(f"Processing batch {batch_num}: {len(batch_texts)} texts, URL: {self.base_url}")

            try:
                start_time = time.perf_counter()
                response_data = await self._connection_manager.make_request_with_retry(
                    self.async_session, self.base_url, self._headers, payload
                )
                request_time = time.perf_counter() - start_time

                logger.debug(f"Batch {batch_num} completed in {request_time:.3f}s")

                batch_embeddings = [item['embedding'] for item in response_data.get('data', [])]
                all_embeddings.extend(batch_embeddings)

            except Exception as e:
                logger.error(f"Failed to process async batch {batch_num}: {str(e)}")
                logger.error(f"Request details - URL: {self.base_url}, Headers: {self._headers}, Payload size: {len(str(payload))}")
                self._metrics.record_error()
                raise Exception(f"Failed to get embeddings for async batch: {str(e)}") from e

        logger.debug(f"All batches completed, total embeddings: {len(all_embeddings)}")
        return all_embeddings

    async def _process_single_batch_with_retry(self, batch_num: int, batch_texts: List[str], start_idx: int, model: str) -> List[List[float]]:
        """处理单个批次，支持重试机制"""
        last_exception = None

        for attempt in range(self.batch_retry_attempts + 1):  # +1 for initial attempt
            try:
                self._metrics.record_batch()

                payload = {
                    "input": batch_texts,
                    "model": model
                }

                if attempt > 0:
                    logger.debug(f"Retrying batch {batch_num}, attempt {attempt + 1}/{self.batch_retry_attempts + 1}")
                else:
                    logger.debug(f"Processing batch {batch_num}: {len(batch_texts)} texts, URL: {self.base_url}")

                start_time = time.perf_counter()
                response_data = await self._connection_manager.make_request_with_retry(
                    self.async_session, self.base_url, self._headers, payload
                )
                request_time = time.perf_counter() - start_time

                logger.debug(f"Batch {batch_num} completed in {request_time:.3f}s")

                batch_embeddings = [item['embedding'] for item in response_data.get('data', [])]
                return batch_embeddings

            except Exception as e:
                last_exception = e
                if attempt < self.batch_retry_attempts:
                    wait_time = 0.5 * (2 ** attempt)  # 指数退避
                    logger.warning(f"Batch {batch_num} failed (attempt {attempt + 1}), retrying in {wait_time:.1f}s: {str(e)}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"Batch {batch_num} failed after {self.batch_retry_attempts + 1} attempts: {str(e)}")
                    logger.error(f"Request details - URL: {self.base_url}, Headers: {self._headers}, Payload size: {len(str(payload))}")
                    self._metrics.record_error()

        # 如果所有重试都失败，抛出最后一个异常
        raise Exception(f"Failed to get embeddings for batch {batch_num} after {self.batch_retry_attempts + 1} attempts") from last_exception

    def _check_memory_usage(self, texts: List[str]):
        """检查内存使用情况，实施背压控制"""
        current_memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
        estimated_text_size_mb = sum(len(text.encode('utf-8')) for text in texts) / 1024 / 1024
        
        if current_memory_mb + estimated_text_size_mb > self.max_memory_mb:
            logger.warning(f"Memory usage ({current_memory_mb:.1f}MB) + text size ({estimated_text_size_mb:.1f}MB) "
                         f"exceeds limit ({self.max_memory_mb}MB)")
            # 可以实施背压策略，比如减小批次大小或抛出异常
            if estimated_text_size_mb > self.max_memory_mb * 0.5:
                raise MemoryError(f"Text batch too large: {estimated_text_size_mb:.1f}MB exceeds 50% of memory limit")

    def get_text_embedding_num_tokens(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> list[int]:
        """
        获取文本的令牌数 - 简化实现
        """
        return [len(text) for text in texts]

    def _calc_embedding_usage(
            self,
            model: str,
            credentials: dict,
            texts: list[str]
    ) -> EmbeddingUsage:
        """
        计算嵌入使用情况
        """
        total_tokens = sum(len(text) for text in texts)
        unit_price = Decimal("0.0001")
        price_unit = Decimal("1000")
        total_price = (Decimal(total_tokens) * unit_price) / price_unit

        return EmbeddingUsage(
            tokens=total_tokens,
            total_tokens=total_tokens,
            unit_price=unit_price,
            price_unit=price_unit,
            total_price=total_price,
            currency="USD",
            latency=0.0  # 将在调用方设置
        )

    async def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        # 合并本地指标和会话管理器指标
        local_metrics = self._metrics.get_stats()
        session_metrics = self._session_manager.get_metrics()

        stats = {
            "metrics": {
                **local_metrics,
                "session_manager": session_metrics
            },
            "connection_manager": {
                "max_retries": self._connection_manager.max_retries,
                "base_timeout": self._connection_manager.base_timeout,
                "config": {
                    "limit": self._session_manager.config.limit,
                    "limit_per_host": self._session_manager.config.limit_per_host,
                    "total_timeout": self._session_manager.config.total_timeout
                }
            },
            "configuration": {
                "batch_size": self.batch_size,
                "enable_cache": self.enable_cache,
                "max_memory_mb": self.max_memory_mb,
                "enable_batch_concurrency": self.enable_batch_concurrency,
                "max_concurrent_batches": self.max_concurrent_batches,
                "batch_retry_attempts": self.batch_retry_attempts,
                "instance_id": self._instance_id,
                "model_type": "embedding"
            }
        }

        if self._cache:
            stats["cache"] = self._cache.get_stats()

        return stats

    def clear_cache(self):
        """清空嵌入缓存"""
        if self._cache:
            self._cache.clear()
            logger.info("Embedding cache cleared")

    async def close(self):
        """手动关闭resources - 通常不需要调用，由统一会话管理器管理"""
        logger.debug(f"GenericEmbedding instance {self._instance_id} close() called - resources managed by HTTPSessionManager")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        logger.debug("GenericEmbedding context manager exit - resources managed by HTTPSessionManager")

    def __del__(self):
        logger.debug(f"GenericEmbedding instance {self._instance_id} being garbage collected")
