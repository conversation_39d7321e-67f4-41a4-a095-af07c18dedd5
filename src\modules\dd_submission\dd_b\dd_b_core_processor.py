"""
DD-B核心处理器

专注于核心逻辑的简化版本，用于测试和调试：
1. 输入：report_code (version) 和 dept_id
2. 输出：最终的 list[dict] 格式数据
3. 支持批量处理（300条一批）
4. 优化向量搜索（批量搜索而非逐个）
5. 完整的数据流可视化和调试

核心流程：
查询数据 → 批量向量搜索 → Pipeline处理 → 字段聚合 → 最终输出
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Any, Optional,Tuple
from dataclasses import dataclass, field

from utils.common.cache_manager import cache_manager

logger = logging.getLogger(__name__)


@dataclass
class DDBProcessProgress:
    """DD-B处理进度"""
    total_records: int = 0
    processed_records: int = 0
    successful_records: int = 0
    failed_records: int = 0
    current_batch: int = 0
    total_batches: int = 0
    start_time: float = field(default_factory=time.time)
    last_checkpoint_time: float = field(default_factory=time.time)
    checkpoint_file: str = ""

    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_records == 0:
            return 0.0
        return (self.processed_records / self.total_records) * 100

    def get_elapsed_time(self) -> float:
        """获取已用时间（秒）"""
        return time.time() - self.start_time

    def get_estimated_remaining_time(self) -> float:
        """估算剩余时间（秒）"""
        if self.processed_records == 0:
            return 0.0
        elapsed = self.get_elapsed_time()
        avg_time_per_record = elapsed / self.processed_records
        remaining_records = self.total_records - self.processed_records
        return remaining_records * avg_time_per_record

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_records': self.total_records,
            'processed_records': self.processed_records,
            'successful_records': self.successful_records,
            'failed_records': self.failed_records,
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'start_time': self.start_time,
            'last_checkpoint_time': self.last_checkpoint_time,
            'checkpoint_file': self.checkpoint_file,
            'progress_percentage': self.get_progress_percentage(),
            'elapsed_time': self.get_elapsed_time(),
            'estimated_remaining_time': self.get_estimated_remaining_time()
        }


@dataclass
class CoreProcessResult:
    """核心处理结果"""
    success: bool = False
    total_records: int = 0
    processed_records: int = 0
    processing_time: float = 0.0
    
    # 最终输出数据
    final_data: List[Dict[str, Any]] = field(default_factory=list)
    
    # 调试信息
    debug_info: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)


class DDBCoreProcessor:
    """DD-B核心处理器"""
    
    def __init__(
        self,
        rdb_client: Any,
        vdb_client: Any = None,
        embedding_client: Any = None,
        batch_size: int = 300
    ):
        """
        初始化核心处理器
        
        Args:
            rdb_client: RDB客户端 (get_client('database.rdbs.mysql'))
            vdb_client: VDB客户端 (get_client('database.vdbs.pgvector'))
            embedding_client: 嵌入客户端 (get_client('model.embeddings.moka-m3e-base'))
            batch_size: 批处理大小，默认300
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.batch_size = batch_size
        self.max_workers = 5  # 并发worker数量（减少以缓解PostgreSQL连接池压力）
        self.enable_checkpoint = True
        self.cache_session_dir: Optional[str] = None
        self.progress: Optional[DDBProcessProgress] = None

        # 🔧 移除初始化debug日志，减少启动噪音

    def _create_checkpoint_filename(self, report_code: str, dept_id: str) -> str:
        """创建检查点文件名"""
        timestamp = int(time.time())
        return f"checkpoint_{report_code}_{dept_id}_{timestamp}.json"

    def _save_checkpoint(self, results: List[Dict[str, Any]]) -> None:
        """保存检查点"""
        if not self.enable_checkpoint or not self.progress or not self.cache_session_dir:
            return

        try:
            checkpoint_data = {
                'progress': self.progress.to_dict(),
                'results': results,
                'timestamp': time.time()
            }

            # 使用缓存管理器保存检查点
            cache_manager.save_file(self.progress.checkpoint_file, checkpoint_data, as_json=True)

            self.progress.last_checkpoint_time = time.time()
            # 🔧 检查点保存成功不需要debug日志，进度日志已有info级别

        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def _load_checkpoint(self, checkpoint_file: str) -> Optional[Dict[str, Any]]:
        """加载检查点"""
        if not self.enable_checkpoint or not self.cache_session_dir:
            return None

        try:
            # 使用缓存管理器加载检查点
            checkpoint_data = cache_manager.load_file(checkpoint_file, as_json=True)

            # 🔧 检查点加载成功不需要debug日志

            return checkpoint_data

        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            return None

    def _update_progress(self, processed: int = 1, successful: bool = True, current_results: List[Dict[str, Any]] = None) -> None:
        """更新进度"""
        if not self.progress:
            return

        self.progress.processed_records += processed
        if successful:
            self.progress.successful_records += processed
        else:
            self.progress.failed_records += processed

        # 每10条记录或每5分钟保存一次检查点
        should_save = (
            self.progress.processed_records % 10 == 0 or
            (time.time() - self.progress.last_checkpoint_time) > 300
        )

        if should_save:
            logger.info(f"进度更新: {self.progress.processed_records}/{self.progress.total_records} "
                       f"({self.progress.get_progress_percentage():.1f}%), "
                       f"成功: {self.progress.successful_records}, "
                       f"失败: {self.progress.failed_records}, "
                       f"预计剩余: {self.progress.get_estimated_remaining_time()/60:.1f}分钟")

            # 真正保存检查点
            if current_results:
                self._save_checkpoint(current_results)

    def clear_checkpoint(self, checkpoint_file: str = None) -> None:
        """清理检查点文件"""
        try:
            if self.cache_session_dir:
                # 清理整个会话目录（包含所有检查点文件）
                success = cache_manager.cleanup_session(keep_on_error=True)
                if success:
                    self.cache_session_dir = None
                else:
                    logger.warning(f"会话目录清理失败: {self.cache_session_dir}")
        except Exception as e:
            logger.error(f"清理检查点失败: {e}")

    async def process_core_logic(
        self,
        report_code: str,
        dept_id: str
    ) -> CoreProcessResult:
        """
        执行核心处理逻辑

        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID

        Returns:
            CoreProcessResult: 核心处理结果
        """
        start_time = time.time()
        result = CoreProcessResult()

        try:
            process_start_time = time.time()
            logger.info(f"🚀 开始核心处理: report_code={report_code}, dept_id={dept_id}")

            # 1. 查询数据
            logger.info("1️⃣ 查询数据...")
            query_start_time = time.time()
            raw_records = await self._query_data(report_code, dept_id)
            query_end_time = time.time()
            logger.info(f"1️⃣ 查询数据完成: 记录数={len(raw_records)}, 耗时={query_end_time-query_start_time:.2f}s")

            # 初始化缓存会话和进度监控
            if self.enable_checkpoint:
                # 创建缓存会话目录
                self.cache_session_dir = cache_manager.create_session(
                    app_name="dd_b_process",
                    session_id=f"{report_code}_{dept_id}_{int(time.time())}"
                )

                checkpoint_file = self._create_checkpoint_filename(report_code, dept_id)
                self.progress = DDBProcessProgress(
                    total_records=len(raw_records),
                    checkpoint_file=checkpoint_file
                )
                # � 进度监控启动不需要debug日志
            result.total_records = len(raw_records)
            result.debug_info['raw_records_count'] = len(raw_records)

            if not raw_records:
                logger.warning("未找到匹配的记录")
                result.success = False
                return result

            # 2. 批量向量搜索
            logger.info("2️⃣ 批量向量搜索...")
            vector_results = await self._batch_vector_search(raw_records)
            result.debug_info['vector_search_results'] = len(vector_results)

            # 3. 批量处理
            logger.info("3️⃣ 批量处理...")
            batch_start_time = time.time()
            processed_data = await self._batch_process_records(raw_records, vector_results)
            batch_end_time = time.time()
            logger.info(f"3️⃣ 批量处理完成: 处理记录数={len(processed_data)}, 耗时={batch_end_time-batch_start_time:.2f}s")
            result.debug_info['processed_batches'] = len(processed_data)

            # 4. 字段聚合
            logger.info("4️⃣ 字段聚合...")
            agg_start_time = time.time()
            aggregated_data = await self._aggregate_fields(processed_data)
            agg_end_time = time.time()
            logger.info(f"4️⃣ 字段聚合完成: 聚合记录数={len(aggregated_data)}, 耗时={agg_end_time-agg_start_time:.2f}s")
            result.debug_info['aggregated_records'] = len(aggregated_data)

            # 5. 生成最终输出
            logger.info("5️⃣ 生成最终输出...")
            final_data = await self._generate_final_output(aggregated_data)

            # 设置结果
            result.final_data = final_data
            result.processed_records = len(final_data)
            result.success = True

            logger.info(f"✅ 核心处理完成: 输出 {len(final_data)} 条记录")

        except Exception as e:
            error_msg = f"核心处理失败: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            result.success = False
        finally:
            # 清理缓存会话目录
            if self.enable_checkpoint and self.cache_session_dir:
                try:
                    # 清理旧的会话目录（保留最近24小时的）
                    cache_manager.cleanup_old_sessions("dd_b", max_age_hours=24)

                    # 清理当前会话目录
                    self.clear_checkpoint()
                except Exception as e:
                    logger.warning(f"清理缓存会话失败: {e}")

        result.processing_time = time.time() - start_time
        return result
    
    async def _query_data(
        self,
        report_code: str,
        dept_id: str
    ) -> List[Dict[str, Any]]:
        """查询数据"""
        try:
            # 使用DD CRUD查询
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)
            
            # 1. 先查询post_distribution数据
            conditions = [{"version": report_code, "dept_id": dept_id}]
            records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions,
                batch_size=100,
                max_concurrency=5
            )

            # 🔧 合并相关的debug信息，减少日志行数
            if not records:
                return []

            # 2. 提取所有的pre_distribution_id
            pre_distribution_ids = []
            for record in records:
                pre_id = record.get('pre_distribution_id')
                if pre_id:
                    pre_distribution_ids.append(pre_id)

            logger.debug(f"数据查询: post_distribution={len(records)}条, pre_distribution_ids={len(pre_distribution_ids)}个")

            if not pre_distribution_ids:
                logger.warning("没有找到有效的pre_distribution_id")
                return records

            # 3. 根据pre_distribution_id查询pre_distribution表
            pre_conditions = [{"id": pre_id} for pre_id in pre_distribution_ids]
            pre_records = await dd_crud.batch_query_pre_distributions(
                conditions_list=pre_conditions,
                batch_size=100,
                max_concurrency=5
            )

            logger.debug(f"查询到 {len(pre_records)} 条pre_distribution记录")

            # 4. 创建pre_distribution_id到pre记录的映射
            pre_record_map = {}
            for pre_record in pre_records:
                pre_id = pre_record.get('id')
                if pre_id:
                    pre_record_map[pre_id] = pre_record

            # 5. 将dr01, dr09, dr17和bdr01-bdr04合并到records中
            merged_records = []
            for record in records:
                pre_id = record.get('pre_distribution_id')
                if pre_id and pre_id in pre_record_map:
                    pre_record = pre_record_map[pre_id]
                    # 合并dr字段
                    record['dr01'] = pre_record.get('dr01', '')
                    record['dr09'] = pre_record.get('dr09', '')
                    record['dr17'] = pre_record.get('dr17', '')

                    # 合并bdr01-bdr04字段（从post_distribution表读取）
                    record['bdr01'] = record.get('bdr01', '')
                    record['bdr02'] = record.get('bdr02', '')
                    record['bdr03'] = record.get('bdr03', '')
                    record['bdr04'] = record.get('bdr04', '')
                else:
                    # 如果没有找到对应的pre记录，设置为空
                    record['dr01'] = ''
                    record['dr09'] = ''
                    record['dr17'] = ''

                    # bdr01-bdr04字段保持原值
                    record['bdr01'] = record.get('bdr01', '')
                    record['bdr02'] = record.get('bdr02', '')
                    record['bdr03'] = record.get('bdr03', '')
                    record['bdr04'] = record.get('bdr04', '')

                merged_records.append(record)

            # 6. 批量查询所有dept_id对应的table_ids
            unique_dept_ids = list(set(record.get('dept_id') for record in merged_records if record.get('dept_id')))
            dept_table_map = await self._batch_query_dept_table_relations(unique_dept_ids)

            # 7. 将table_ids添加到每个记录中
            for record in merged_records:
                dept_id = record.get('dept_id')
                table_ids = dept_table_map.get(dept_id, [str(i) for i in range(1, 95)])  # 默认值
                record['table_ids'] = table_ids

            # 8. 统计有效记录（dr09不为空）
            valid_records = [r for r in merged_records if r.get('dr09', '').strip()]

            # 🔧 合并最终统计信息
            logger.debug(f"数据处理完成: 总记录={len(merged_records)}, 有效记录={len(valid_records)}, dept_ids={len(unique_dept_ids)}")

            return merged_records
            
        except Exception as e:
            logger.error(f"数据查询失败: {e}")
            return []

    async def _batch_query_dept_table_relations(self, dept_ids: List[str]) -> Dict[str, List[str]]:
        """批量查询部门对应的table_ids"""
        try:
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            # 构建批量查询条件
            conditions_list = [{"dept_id": dept_id} for dept_id in dept_ids]

            # 使用新的批量查询方法
            relations = await dd_crud.batch_query_department_relations(
                conditions_list=conditions_list,
                batch_size=100,
                max_concurrency=5
            )

            # 🔧 移除冗余的debug日志

            # 构建dept_id到table_ids的映射
            dept_table_map = {}
            for relation in relations:
                dept_id = relation.get('dept_id')
                table_id = relation.get('table_id')

                if dept_id and table_id:
                    if dept_id not in dept_table_map:
                        dept_table_map[dept_id] = []
                    # 安全转换table_id为int，然后转为字符串
                    try:
                        table_id_int = int(table_id)
                        dept_table_map[dept_id].append(table_id_int)
                    except (ValueError, TypeError):
                        # 🔧 移除详细的转换错误日志，只在warning级别记录
                        continue

            # 为没有找到关联的dept_id设置默认值
            missing_dept_count = 0
            for dept_id in dept_ids:
                if dept_id not in dept_table_map:
                    dept_table_map[dept_id] = [str(i) for i in range(1, 95)]
                    missing_dept_count += 1

            # 🔧 合并统计信息，只在有缺失时输出warning
            if missing_dept_count > 0:
                logger.warning(f"{missing_dept_count}个dept_id没有找到关联的table_ids，使用默认值")

            logger.debug(f"部门关系查询完成: {len(dept_ids)}个dept_id, 缺失={missing_dept_count}个")
            return dept_table_map

        except Exception as e:
            logger.error(f"批量查询department relations失败: {e}")
            # 出错时为所有dept_id设置默认值
            return {dept_id: [str(i) for i in range(1, 95)] for dept_id in dept_ids}
    
    async def _batch_vector_search(
        self,
        records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量向量搜索（多字段混合搜索版本）

        实现逻辑：
        1. 对每条记录，使用 [dr09, dr17] 字段列表进行混合向量搜索
        2. 搜索参数：同时搜索 field_code='dr09' 和 field_code='dr17' 的向量数据
        3. 搜索限制：limit=1, min_score=0.6
        4. 判断逻辑：如果找到 score > 0.6 的记录，标记为高置信度；否则为低置信度

        Returns:
            List[Dict]: 每个元素包含：
            {
                'record_id': 原始记录ID,
                'confidence': 'high' | 'low',
                'best_match': 最佳匹配结果 (如果有),
                'data_row_id': 匹配的data_row_id (如果有),
                'score': 最高分数 (如果有)
            }
        """
        if not self.vdb_client or not self.embedding_client:
            logger.warning("向量搜索客户端未配置，跳过向量搜索")
            return []

        try:
            # 提取有效记录
            valid_records = []
            for record in records:
                dr09 = record.get('dr09', '').strip()
                dr17 = record.get('dr17', '').strip()
                if dr09 or dr17:
                    valid_records.append({
                        'record_id': record.get('id'),
                        'dr09': dr09,
                        'dr17': dr17
                    })

            if not valid_records:
                logger.warning("没有有效的查询文本")
                return []

            logger.info(f"🔍 开始多字段混合向量搜索: {len(valid_records)} 个查询")

            # 使用DD搜索的高级混合搜索功能
            from modules.knowledge.dd.search import DDSearch
            dd_search = DDSearch(self.rdb_client, self.vdb_client, self.embedding_client)

            # 批量处理向量搜索（Semaphore模式 + 超时保护）
            search_results = []
            batch_size = 100  # 增大批次大小，提高处理效率
            max_concurrent = 25  # 增加并发数，但保持在连接池安全范围内

            for i in range(0, len(valid_records), batch_size):
                batch_records = valid_records[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(valid_records) + batch_size - 1) // batch_size

                logger.debug(f"🔍 处理向量搜索批次 {batch_num}/{total_batches}: {len(batch_records)} 条记录, 最大并发={max_concurrent}")

                # 使用改进的Semaphore模式处理当前批次
                try:
                    batch_results = await self._process_batch_with_semaphore_safe(dd_search, batch_records, max_concurrent)

                    # 处理批次结果
                    for j, result in enumerate(batch_results):
                        if isinstance(result, dict):
                            search_results.append(result)
                        elif isinstance(result, Exception):
                            logger.error(f"向量搜索异常: record_id={batch_records[j]['record_id']}, error={result}")
                            # 添加失败记录
                            search_results.append({
                                'record_id': batch_records[j]['record_id'],
                                'confidence': 'low',
                                'best_match': None,
                                'data_row_id': None,
                                'score': 0.0
                            })
                        else:
                            logger.warning(f"未知搜索结果类型: {type(result)}")
                            search_results.append({
                                'record_id': batch_records[j]['record_id'],
                                'confidence': 'low',
                                'best_match': None,
                                'data_row_id': None,
                                'score': 0.0
                            })

                    logger.debug(f"✅ 向量搜索批次 {batch_num} 完成: 成功={len([r for r in batch_results if isinstance(r, dict)])}/{len(batch_records)}")

                except Exception as e:
                    logger.error(f"向量搜索批次 {batch_num} 失败: {e}")
                    # 为整个批次添加失败记录
                    for record_data in batch_records:
                        search_results.append({
                            'record_id': record_data['record_id'],
                            'confidence': 'low',
                            'best_match': None,
                            'data_row_id': None,
                            'score': 0.0
                        })

            # 统计结果
            high_confidence_count = sum(1 for r in search_results if r['confidence'] == 'high')
            low_confidence_count = len(search_results) - high_confidence_count

            logger.info(f"🔍 多字段混合向量搜索完成: 总数={len(search_results)}, 高置信度={high_confidence_count}, 低置信度={low_confidence_count}")

            return search_results

        except Exception as e:
            logger.error(f"批量向量搜索失败: {e}")
            return []

    async def _single_vector_search(
        self,
        dd_search: Any,
        record_data: Dict[str, Any],
        min_score: float = 0.6
    ) -> Dict[str, Any]:
        """
        单个记录的向量搜索

        Args:
            dd_search: DDSearch实例
            record_data: 记录数据 {'record_id', 'dr09', 'dr17'}
            min_score: 最小相似度分数阈值

        Returns:
            搜索结果信息，包含优化后的字段结构
        """
        try:
            # 构建字段查询字典
            field_queries = {}
            if record_data['dr09']:
                field_queries['dr09'] = record_data['dr09']
            if record_data['dr17']:
                field_queries['dr17'] = record_data['dr17']

            # 执行高级混合搜索
            hybrid_results = await dd_search.vector_hybrid_search(
                query=record_data['dr09'] or record_data['dr17'],  # 主查询文本
                field_queries=field_queries,
                limit=1,  # 只要最佳匹配
                min_score=min_score,  # 🔧 使用参数化的min_score
                rank_algorithm="rrf"
            )

            # 🔧 优化后的结果处理逻辑

            result_info = {
                'record_id': record_data['record_id'],
                'confidence': 'none',  # 默认为无匹配
                'best_match': None,
                'data_row_id': None,
                'score': 0.0,
                'search_strategy': '2-way_direct_matching',  # 新增：记录搜索策略
                'min_score_threshold': min_score,  # 新增：记录使用的阈值
                'total_results': len(hybrid_results) if hybrid_results else 0  # 新增：记录结果总数
            }

            if hybrid_results and len(hybrid_results) > 0:
                best_match = hybrid_results[0]
                score = best_match.get('score', 0.0)
                data_row_id = best_match.get('data_row_id')

                # 🔧 移除冗余的分数验证 - SQL层面已经过滤了低分结果
                # 所有返回的结果都应该满足min_score要求
                result_info['confidence'] = 'high'  # SQL过滤后的结果都是高置信度
                result_info['best_match'] = best_match
                result_info['data_row_id'] = data_row_id
                result_info['score'] = score

                # 🔧 优化后的日志信息 - 降级为debug减少噪音
                logger.debug(f"✅ 2路直接匹配成功: record_id={record_data['record_id']}, score={score:.3f}, data_row_id={data_row_id}")

            else:
                # 🔧 改进的边缘情况处理
                result_info['confidence'] = 'none'

                logger.debug(f"❌ 无匹配结果: record_id={record_data['record_id']}, min_score={min_score}")

            return result_info

        except Exception as e:
            logger.error(f"单个记录搜索失败: record_id={record_data['record_id']}, error={e}")
            return {
                'record_id': record_data['record_id'],
                'confidence': 'low',
                'best_match': None,
                'data_row_id': None,
                'score': 0.0
            }

    async def _single_vector_search_with_semaphore(
        self,
        dd_search: Any,
        record_data: Dict[str, Any],
        semaphore: asyncio.Semaphore,
        min_score: float = 0.6
    ) -> Dict[str, Any]:
        """
        带信号量控制的单个记录向量搜索

        Args:
            dd_search: DDSearch实例
            record_data: 记录数据 {'record_id', 'dr09', 'dr17'}
            semaphore: 并发控制信号量
            min_score: 最小相似度分数阈值

        Returns:
            搜索结果信息
        """
        async with semaphore:
            return await self._single_vector_search(dd_search, record_data, min_score)

    # Worker模式相关代码已移除，使用更安全的Semaphore模式

    # 旧的Worker实现已删除，使用更安全的Semaphore模式

    async def _process_batch_with_semaphore_safe(
        self,
        dd_search: Any,
        batch_records: List[Dict[str, Any]],
        max_concurrent: int,
        min_score: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        使用Semaphore模式处理批次记录（带超时保护）

        Args:
            dd_search: DDSearch实例
            batch_records: 批次记录列表
            max_concurrent: 最大并发数
            min_score: 最小相似度分数阈值

        Returns:
            处理结果列表
        """
        # 创建信号量
        semaphore = asyncio.Semaphore(max_concurrent)

        # 创建所有任务
        tasks = []
        for record_data in batch_records:
            task = self._single_vector_search_with_semaphore(dd_search, record_data, semaphore, min_score)
            tasks.append(task)

        try:
            # 设置合理的超时时间：每个记录最多10秒，但至少5分钟，最多30分钟
            timeout_seconds = min(1800, max(300, len(batch_records) * 10))
            logger.debug(f"开始Semaphore批次处理，超时限制: {timeout_seconds}秒")

            # 执行所有任务（带超时保护）
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=timeout_seconds
            )

            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, dict):
                    processed_results.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"Semaphore任务异常: idx={i}, error={result}")
                    processed_results.append({
                        'record_id': batch_records[i]['record_id'],
                        'confidence': 'low',
                        'best_match': None,
                        'data_row_id': None,
                        'score': 0.0
                    })
                else:
                    logger.warning(f"未知结果类型: idx={i}, type={type(result)}")
                    processed_results.append({
                        'record_id': batch_records[i]['record_id'],
                        'confidence': 'low',
                        'best_match': None,
                        'data_row_id': None,
                        'score': 0.0
                    })

            return processed_results

        except asyncio.TimeoutError:
            logger.error(f"Semaphore批次处理超时({timeout_seconds}秒)，取消所有任务")

            # 取消所有未完成的任务
            cancelled_count = 0
            for task in tasks:
                if not task.done():
                    task.cancel()
                    cancelled_count += 1

            logger.warning(f"已取消 {cancelled_count} 个未完成任务")

            # 返回失败结果
            return [{
                'record_id': record_data['record_id'],
                'confidence': 'low',
                'best_match': None,
                'data_row_id': None,
                'score': 0.0
            } for record_data in batch_records]
    
    async def _batch_process_records(
        self,
        raw_records: List[Dict[str, Any]],
        vector_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量处理记录（支持高置信度直接输出路径）

        处理逻辑：
        1. 高置信度路径（score > 0.6）：直接查询dd_submission_data表获取完整数据
        2. 低置信度路径（score ≤ 0.6 或无结果）：继续执行Pipeline处理流程
        """
        processed_data = []

        # 创建record_id到向量搜索结果的映射
        vector_result_map = {}
        if vector_results:
            for vector_result in vector_results:
                record_id = vector_result.get('record_id')
                if record_id:
                    vector_result_map[record_id] = vector_result

        # 分离高置信度和低置信度记录
        high_confidence_records = []
        low_confidence_records = []

        for record in raw_records:
            record_id = record.get('id')
            vector_result = vector_result_map.get(record_id, {})
            confidence = vector_result.get('confidence', 'low')

            if confidence == 'high':
                high_confidence_records.append((record, vector_result))
            else:
                low_confidence_records.append(record)

        logger.info(f"🔄 记录分类完成: 高置信度={len(high_confidence_records)}, 低置信度={len(low_confidence_records)}")

        # 处理高置信度记录（直接输出路径）
        if high_confidence_records:
            logger.info("� 处理高置信度记录（直接输出路径）...")
            high_confidence_results = await self._process_high_confidence_records(high_confidence_records)
            processed_data.extend(high_confidence_results)

        # 处理低置信度记录（Pipeline路径）
        if low_confidence_records:
            logger.info("🔄 处理低置信度记录（Pipeline路径）...")
            low_confidence_results = await self._process_low_confidence_records(low_confidence_records)
            processed_data.extend(low_confidence_results)

        logger.info(f"🔄 批量处理完成: 总处理={len(processed_data)} 条记录")
        return processed_data

    async def _process_high_confidence_records(
        self,
        high_confidence_records: List[Tuple[Dict[str, Any], Dict[str, Any]]]
    ) -> List[Dict[str, Any]]:
        """
        处理高置信度记录（直接输出路径 - 批量优化版本）

        Args:
            high_confidence_records: [(原始记录, 向量搜索结果), ...]

        Returns:
            处理后的记录列表
        """
        results = []

        try:
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            # 提取所有有效的data_row_id
            valid_records = []
            data_row_ids = []

            for original_record, vector_result in high_confidence_records:
                data_row_id = vector_result.get('data_row_id')
                score = vector_result.get('score', 0.0)

                if not data_row_id:
                    logger.warning(f"高置信度记录缺少data_row_id: record_id={original_record.get('id')}")
                    self._update_progress(1, successful=False)
                    continue

                valid_records.append((original_record, vector_result))
                data_row_ids.append(data_row_id)

            if not data_row_ids:
                logger.warning("没有有效的data_row_id进行批量查询")
                return results

            logger.info(f"🔍 批量查询dd_submission_data: {len(data_row_ids)} 个data_row_id")

            # 批量查询dd_submission_data表
            submission_data_list = await dd_crud.batch_get_submission_data(data_row_ids)

            # 创建data_row_id到submission_data的映射
            submission_data_map = {}
            for submission_data in submission_data_list:
                if submission_data and 'id' in submission_data:
                    submission_data_map[submission_data['id']] = submission_data

            logger.debug(f"批量查询结果: 查询{len(data_row_ids)}个，成功{len(submission_data_map)}个")

            # 处理每个记录
            for original_record, vector_result in valid_records:
                try:
                    data_row_id = vector_result.get('data_row_id')
                    score = vector_result.get('score', 0.0)
                    submission_data = submission_data_map.get(data_row_id)

                    if submission_data:
                        # 构建最终输出格式
                        final_record = {
                            'record_id': str(original_record.get('id')),
                            'submission_id': original_record.get('submission_id'),
                            'dept_id': original_record.get('dept_id'),
                            'version': original_record.get('version'),
                            'submission_type': original_record.get('submission_type', 'NORMAL'),

                            # 原始DR字段（保留原始数据）
                            'dr01': original_record.get('dr01', ''),
                            'dr09': original_record.get('dr09', ''),
                            'dr17': original_record.get('dr17', ''),

                            # BDR01-BDR04字段（从原始records保留）
                            'bdr01': original_record.get('bdr01', ''),
                            'bdr02': original_record.get('bdr02', ''),
                            'bdr03': original_record.get('bdr03', ''),
                            'bdr04': original_record.get('bdr04', ''),

                            # BDR05-BDR17字段（从dd_submission_data获取）
                            'bdr05': submission_data.get('bdr05', ''),
                            'bdr06': submission_data.get('bdr06', ''),
                            'bdr07': submission_data.get('bdr07', ''),
                            'bdr08': submission_data.get('bdr08', ''),
                            'bdr09': submission_data.get('bdr09', ''),
                            'bdr10': submission_data.get('bdr10', ''),
                            'bdr11': submission_data.get('bdr11', ''),
                            'bdr12': submission_data.get('bdr12', ''),
                            'bdr13': submission_data.get('bdr13', ''),
                            'bdr14': submission_data.get('bdr14', ''),
                            'bdr15': submission_data.get('bdr15', ''),
                            'bdr16': submission_data.get('bdr16', ''),
                            'bdr17': submission_data.get('bdr17', ''),

                            # SDR字段（从dd_submission_data获取）
                            'sdr01': submission_data.get('sdr01', original_record.get('dr01', '')),
                            'sdr02': submission_data.get('sdr02', ''),
                            'sdr03': submission_data.get('sdr03', ''),
                            'sdr04': submission_data.get('sdr04', ''),
                            'sdr05': submission_data.get('sdr05', ''),
                            'sdr06': submission_data.get('sdr06', ''),
                            'sdr07': submission_data.get('sdr07', ''),
                            'sdr08': submission_data.get('sdr08', ''),
                            'sdr09': submission_data.get('sdr09', ''),
                            'sdr10': submission_data.get('sdr10', ''),
                            'sdr11': submission_data.get('sdr11', ''),
                            'sdr12': submission_data.get('sdr12', ''),
                            'sdr13': submission_data.get('sdr13', ''),
                            'sdr14': submission_data.get('sdr14', ''),
                            'sdr15': submission_data.get('sdr15', ''),

                            # 状态信息
                            'processing_status': 'high_confidence_direct',
                            'vector_score': score,
                            'matched_data_row_id': data_row_id
                        }

                        results.append(final_record)
                        self._update_progress(1, successful=True)

                        logger.debug(f"✅ 高置信度记录处理完成: record_id={original_record.get('id')}, data_row_id={data_row_id}")

                    else:
                        logger.warning(f"未找到dd_submission_data记录: data_row_id={data_row_id}")
                        self._update_progress(1, successful=False)

                except Exception as e:
                    logger.error(f"高置信度记录处理失败: record_id={original_record.get('id')}, error={e}")
                    self._update_progress(1, successful=False)
                    continue

            logger.info(f"✅ 高置信度记录处理完成: 成功={len(results)}/{len(high_confidence_records)}")
            return results

        except Exception as e:
            logger.error(f"高置信度记录批量处理失败: {e}")
            return []

    async def _process_low_confidence_records(
        self,
        low_confidence_records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        处理低置信度记录（Pipeline路径）

        Args:
            low_confidence_records: 低置信度记录列表

        Returns:
            处理后的记录列表
        """
        # 使用原有的批量处理逻辑
        return await self._process_records_with_pipeline(low_confidence_records)

    async def _process_records_with_pipeline(
        self,
        records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """使用Pipeline处理记录（原有逻辑）"""
        processed_data = []

        # 分批处理
        total_batches = (len(records) + self.batch_size - 1) // self.batch_size
        logger.debug(f"🔄 Pipeline处理分批: 总记录数={len(records)}, 批次大小={self.batch_size}, 总批次数={total_batches}")

        for i in range(0, len(records), self.batch_size):
            batch_num = i // self.batch_size + 1
            batch = records[i:i + self.batch_size]

            logger.debug(f"🔄 处理Pipeline批次 {batch_num}/{total_batches}: {len(batch)} 条记录")

            # 处理当前批次
            batch_result = await self._process_single_batch(batch, [])  # 空的vector_results
            processed_data.extend(batch_result)

        return processed_data

    async def _process_single_batch(
        self,
        batch_records: List[Dict[str, Any]],
        batch_vector_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """处理单个批次"""
        batch_start_time = time.time()  # 在方法开始时定义
        try:
            # 使用现有的Pipeline集成器
            from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineIntegrator
            from modules.knowledge.metadata.crud import MetadataCrud

            metadata_crud = MetadataCrud(self.rdb_client)
            pipeline_integrator = PipelineIntegrator(metadata_crud)
            
            # 并发处理批次中的记录
            semaphore = asyncio.Semaphore(10)  # 15个并发
            
            async def process_single_record(record: Dict[str, Any]) -> Dict[str, Any]:
                async with semaphore:
                    try:
                        # 检查submission_type，RANGE类型不进入pipeline处理
                        submission_type = record.get('submission_type', 'NORMAL')
                        if submission_type == 'RANGE':
                            logger.debug(f"记录 {record.get('id')} 是RANGE类型，跳过pipeline处理")
                            # 直接返回原始记录，包含完整的默认值
                            return {
                                'record_id': str(record.get('id')),
                                'submission_id': record.get('submission_id'),  # 添加submission_id字段
                                'dept_id': record.get('dept_id'),
                                'version': record.get('version'),
                                'submission_type': submission_type,
                                'dr01': record.get('dr01', ''),
                                'dr09': record.get('dr09', ''),
                                'dr17': record.get('dr17', ''),
                                'bdr01': record.get('bdr01', ''),
                                'bdr02': record.get('bdr02', ''),
                                'bdr03': record.get('bdr03', ''),
                                'bdr04': record.get('bdr04', ''),

                                # BDR05-BDR17字段默认值
                                'bdr05': '',
                                'bdr06': '不适用',
                                'bdr07': '1',
                                'bdr08': 'CRD',
                                'bdr09': '',
                                'bdr10': '',
                                'bdr11': '',
                                'bdr12': '不适用',
                                'bdr13': '不适用',
                                'bdr14': '不适用',
                                'bdr15': '不适用',
                                'bdr16': '',
                                'bdr17': '不适用',

                                # SDR字段默认值
                                'sdr01': record.get('dr01', ''),
                                'sdr02': '1',
                                'sdr03': 'CRD',
                                'sdr04': '不适用',
                                'sdr05': '',
                                'sdr06': '',
                                'sdr07': '不适用',
                                'sdr08': '',
                                'sdr09': '',
                                'sdr10': '',
                                'sdr11': '不适用',
                                'sdr12': '',
                                'sdr13': '不适用',
                                'sdr14': '不适用',
                                'sdr15': '不适用',

                                'status': 'range_skipped'
                            }

                        # 创建Pipeline请求
                        from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineRequest

                        # 确保user_question不为空
                        user_question = record.get('dr09', '').strip()
                        hint = record.get('dr17', '').strip()

                        # 如果dr09为空，跳过这条记录
                        if not user_question:
                            logger.warning(f"记录 {record.get('id')} 的dr09为空，跳过处理")
                            return self._create_empty_result(record)

                        # 使用记录中预先查询好的table_ids
                        table_ids = record.get('table_ids', [str(i) for i in range(1, 95)])

                        request = PipelineRequest(
                            record_id=record.get('id'),
                            table_ids=table_ids,
                            user_question=user_question,
                            hint=hint
                        )
                        
                        # 执行Pipeline
                        pipeline_result = await pipeline_integrator.execute_pipeline(request)
                        
                        # 映射字段
                        if pipeline_result.execution_success:
                            return await self._map_pipeline_result(record, pipeline_result)
                        else:
                            logger.warning(f"Pipeline执行失败: record_id={record.get('id')}")
                            return self._create_empty_result(record)
                            
                    except Exception as e:
                        logger.error(f"记录处理失败: record_id={record.get('id')}, error={e}")
                        return self._create_empty_result(record)
            
            # 并发处理所有记录，添加超时保护
            tasks = [process_single_record(record) for record in batch_records]

            # 计算批次超时时间：每条记录最多1小时，但至少2小时
            batch_timeout = max(7200, len(batch_records) * 3600)  # 最少2小时，每条最多1小时

            try:
                logger.debug(f"开始批次处理: {len(batch_records)}条记录, 超时限制: {batch_timeout/3600:.1f}小时")
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=batch_timeout
                )

                # 过滤有效结果并更新进度
                valid_results = []
                for i, result in enumerate(results):
                    if isinstance(result, dict):
                        valid_results.append(result)
                        self._update_progress(1, successful=True)
                    elif isinstance(result, Exception):
                        logger.debug(f"批次处理异常: {result}")
                        self._update_progress(1, successful=False)
                    else:
                        logger.debug(f"未知结果类型: {type(result)}")
                        self._update_progress(1, successful=False)

                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                logger.debug(f"✅ 单批次处理完成: 成功={len(valid_results)}/{len(batch_records)}, 耗时={batch_duration:.2f}s")

                # 保存检查点
                if self.enable_checkpoint and len(valid_results) > 0:
                    self._save_checkpoint(valid_results)

                return valid_results

            except asyncio.TimeoutError:
                logger.error(f"批次处理超时: {batch_timeout/3600:.1f}小时, 记录数: {len(batch_records)}")
                # 超时时也要更新进度（标记为失败）
                for record in batch_records:
                    self._update_progress(1, successful=False)
                return []
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}")
            return []
    
    async def _map_pipeline_result(
        self,
        original_record: Dict[str, Any],
        pipeline_result: Any
    ) -> Dict[str, Any]:
        """映射Pipeline结果到字段"""
        try:
            # 使用Pipeline字段映射器
            from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
            from modules.knowledge.metadata.crud import MetadataCrud

            metadata_crud = MetadataCrud(self.rdb_client)
            field_mapper = PipelineFieldMapper(metadata_crud)
            
            # 准备Pipeline结果数据
            if hasattr(pipeline_result, '__dict__'):
                # 如果是PipelineResult对象，转换为字典
                pipeline_data = pipeline_result.__dict__.copy()

                # 从request中提取record_id
                if hasattr(pipeline_result, 'request') and hasattr(pipeline_result.request, 'record_id'):
                    pipeline_data['record_id'] = pipeline_result.request.record_id

            else:
                # 如果已经是字典，直接使用
                pipeline_data = pipeline_result.copy() if isinstance(pipeline_result, dict) else {}

            # 确保有record_id字段
            if 'record_id' not in pipeline_data:
                pipeline_data['record_id'] = original_record.get('id')

            # � 移除详细的调试日志，只在出错时输出
            parser_info = pipeline_data.get('parser_info', {})

            # 创建一个简单的对象来满足字段映射器的期望
            class RecordWrapper:
                def __init__(self, record_dict):
                    self.id = record_dict.get('id')
                    self.__dict__.update(record_dict)

            record_wrapper = RecordWrapper(original_record)

            # 执行字段映射
            mapping_result = await field_mapper.map_single_record(
                pipeline_result=pipeline_data,
                original_record=record_wrapper,
                keep_raw_format=True
            )
            
            if mapping_result.mapping_success:
                # 转换为字符串格式
                string_result = mapping_result.to_string_format()

                # 添加基础字段（确保数据完整性）
                string_result['record_id'] = str(original_record.get('id'))
                string_result['submission_id'] = original_record.get('submission_id')  # 添加submission_id字段
                string_result['dept_id'] = original_record.get('dept_id')
                string_result['version'] = original_record.get('version')
                string_result['submission_type'] = original_record.get('submission_type', 'NORMAL')

                # 添加原始DR字段
                string_result['dr01'] = original_record.get('dr01', '')
                string_result['dr09'] = original_record.get('dr09', '')
                string_result['dr17'] = original_record.get('dr17', '')

                # 添加原始BDR01-BDR04字段
                string_result['bdr01'] = original_record.get('bdr01', '')
                string_result['bdr02'] = original_record.get('bdr02', '')
                string_result['bdr03'] = original_record.get('bdr03', '')
                string_result['bdr04'] = original_record.get('bdr04', '')

                return string_result
            else:
                return self._create_empty_result(original_record)
                
        except Exception as e:
            logger.error(f"字段映射失败: {e}")
            return self._create_empty_result(original_record)
    

    def _create_empty_result(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建空结果"""
        return {
            'record_id': str(record.get('id')),
            'submission_id': record.get('submission_id'),  # 添加submission_id字段
            'dept_id': record.get('dept_id'),
            'version': record.get('version'),
            'status': 'failed'
        }
    
    async def _aggregate_fields(
        self,
        processed_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """字段聚合"""
        try:
            # 分离普通记录和RANGE记录
            normal_records = []
            range_records = []

            for record in processed_data:
                if record.get('submission_type') == 'RANGE':
                    range_records.append(record)
                else:
                    normal_records.append(record)

            logger.debug(f"记录分类: 普通={len(normal_records)}, RANGE={len(range_records)}")

            # 如果有RANGE记录，进行聚合
            if range_records and normal_records:
                logger.info("开始RANGE字段聚合...")

                # 使用RANGE聚合器
                from modules.dd_submission.dd_b.utils.range_aggregator import RangeAggregator
                range_aggregator = RangeAggregator()

                # 对每个RANGE记录进行聚合
                aggregated_range_records = []
                for range_record in range_records:
                    try:
                        aggregated_record = await range_aggregator.aggregate_range_fields(
                            range_record=range_record,
                            submission_list=normal_records
                        )
                        aggregated_range_records.append(aggregated_record)
                        logger.debug(f"RANGE记录聚合完成: record_id={range_record.get('record_id')}")
                    except Exception as e:
                        logger.error(f"RANGE记录聚合失败: record_id={range_record.get('record_id')}, error={e}")
                        aggregated_range_records.append(range_record)  # 保留原记录

                # 返回普通记录 + 聚合后的RANGE记录
                result = normal_records + aggregated_range_records
                logger.info(f"RANGE字段聚合完成: 普通={len(normal_records)}, RANGE={len(aggregated_range_records)}")
                return result
            else:
                logger.debug("无需RANGE聚合，直接返回原数据")
                return processed_data

        except Exception as e:
            logger.error(f"字段聚合失败: {e}")
            return processed_data
    
    async def _generate_final_output(
        self,
        aggregated_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """生成最终输出格式"""
        try:
            final_output = []
            
            for record in aggregated_data:
                # 🔍 调试：检查最终输出生成阶段的数据
                logger.debug(f"🔍 最终输出调试 - record_id={record.get('record_id')}")
                logger.debug(f"  record中的sdr12: {repr(record.get('sdr12'))}")
                logger.debug(f"  record所有键: {list(record.keys())}")

                # 确保输出格式一致
                output_record = {
                    'record_id': record.get('record_id'),
                    'submission_id': record.get('submission_id'),  # 添加submission_id字段
                    'dept_id': record.get('dept_id'),
                    'version': record.get('version'),
                    'submission_type': record.get('submission_type', 'NORMAL'),

                    # 原始DR字段（保留原始数据）
                    'dr01': record.get('dr01', ''),
                    'dr09': record.get('dr09', ''),
                    'dr17': record.get('dr17', ''),

                    # BDR01-BDR04字段（从原始records保留）
                    'bdr01': record.get('bdr01', ''),
                    'bdr02': record.get('bdr02', ''),
                    'bdr03': record.get('bdr03', ''),
                    'bdr04': record.get('bdr04', ''),

                    # BDR05-BDR17字段（Pipeline处理生成）
                    'bdr05': record.get('bdr05', ''),
                    'bdr06': record.get('bdr06', ''),
                    'bdr07': record.get('bdr07', ''),
                    'bdr08': record.get('bdr08', ''),
                    'bdr09': record.get('bdr09', ''),
                    'bdr10': record.get('bdr10', ''),
                    'bdr11': record.get('bdr11', ''),
                    'bdr12': record.get('bdr12', ''),
                    'bdr13': record.get('bdr13', ''),
                    'bdr14': record.get('bdr14', ''),
                    'bdr15': record.get('bdr15', ''),
                    'bdr16': record.get('bdr16', ''),
                    'bdr17': record.get('bdr17', ''),

                    # 完整的SDR字段 (SDR01-SDR15)，包含原始数据映射
                    'sdr01': record.get('sdr01', record.get('dr01', '')),  # dr01映射到sdr01
                    'sdr02': record.get('sdr02', ''),  # dr02映射到sdr02
                    'sdr03': record.get('sdr03', ''),  # dr03映射到sdr03
                    'sdr04': record.get('sdr04', ''),  # dr04映射到sdr04
                    'sdr05': record.get('sdr05', ''),
                    'sdr06': record.get('sdr06', ''),
                    'sdr07': record.get('sdr07', ''),  # dr07映射到sdr07
                    'sdr08': record.get('sdr08', ''),
                    'sdr09': record.get('sdr09', ''),
                    'sdr10': record.get('sdr10', ''),
                    'sdr11': record.get('sdr11', ''),  # dr11映射到sdr11
                    'sdr12': record.get('sdr12', ''),
                    'sdr13': record.get('sdr13', ''),  # dr13映射到sdr13
                    'sdr14': record.get('sdr14', ''),  # dr14映射到sdr14
                    'sdr15': record.get('sdr15', ''),  # dr15映射到sdr15

                    # 状态信息
                    'processing_status': record.get('status', 'success'),
                    'processing_time': record.get('processing_time', 0.0)
                }

                final_output.append(output_record)

            logger.debug(f"最终输出生成完成: {len(final_output)} 条记录")
            return final_output
            
        except Exception as e:
            logger.error(f"最终输出生成失败: {e}")
            return aggregated_data


async def process_dd_b_core(
    rdb_client: Any,
    report_code: str,
    dept_id: str,
    vdb_client: Any = None,
    embedding_client: Any = None,
    batch_size: int = 300
) -> CoreProcessResult:
    """
    DD-B核心处理便捷函数
    
    Args:
        rdb_client: RDB客户端
        report_code: 报告代码
        dept_id: 部门ID
        vdb_client: VDB客户端
        embedding_client: 嵌入客户端
        batch_size: 批处理大小
        
    Returns:
        CoreProcessResult: 核心处理结果
    """
    processor = DDBCoreProcessor(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client,
        batch_size=batch_size
    )
    
    return await processor.process_core_logic(report_code, dept_id)
