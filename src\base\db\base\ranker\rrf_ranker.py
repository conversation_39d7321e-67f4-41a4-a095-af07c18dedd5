"""
RRF (Reciprocal Rank Fusion) 排序器

实现RRF算法，通过倒数排名融合多个搜索结果。
RRF算法公式: score = 1 / (k + rank)

参考Milvus的RRFRanker实现。

作者: HSBC Knowledge Team
日期: 2025-01-14
"""

from typing import List, Dict, Any, Optional
import logging
from collections import defaultdict

from .base_ranker import BaseRanker

logger = logging.getLogger(__name__)


class RRFRanker(BaseRanker):
    """
    RRF (Reciprocal Rank Fusion) 排序器
    
    使用倒数排名融合算法合并多个搜索结果。
    该算法对在多个搜索中都出现的项目给予更高的权重。
    """
    
    def __init__(self, k: int = 60):
        """
        初始化RRF排序器
        
        Args:
            k: RRF参数，用于平滑排名，推荐值10-100，默认60
               score = 1 / (k + rank)
               k值越大，排名差异的影响越小
        """
        super().__init__(f"RRFRanker(k={k})")
        
        if k < 0:
            raise ValueError("RRF参数k必须为非负数")
            
        self.k = k
        logger.debug(f"初始化RRF排序器，k={k}")
    
    def rank(self, 
             results_list: List[List[Dict[str, Any]]], 
             top_k: Optional[int] = None,
             **kwargs) -> List[Dict[str, Any]]:
        """
        使用RRF算法对多个搜索结果进行重排序
        
        Args:
            results_list: 多个搜索结果列表
            top_k: 最终返回的结果数量
            **kwargs: 额外参数（RRF不使用）
            
        Returns:
            RRF重排序后的结果列表
        """
        # 验证输入
        self.validate_results(results_list)
        
        if not results_list:
            return []
        
        logger.debug(f"开始RRF排序，输入{len(results_list)}个搜索结果列表")
        
        # 收集所有唯一的结果项
        all_items = {}  # id -> 完整的result dict
        rrf_scores = defaultdict(float)  # id -> RRF累积分数
        rank_details = defaultdict(list)  # id -> [(search_index, rank, individual_score), ...]
        
        # 遍历每个搜索结果列表
        for search_index, results in enumerate(results_list):
            logger.debug(f"处理第{search_index + 1}个搜索结果，包含{len(results)}个项目")
            
            # 按原始分数排序（确保排名正确）
            # 对于相似度分数，应该降序排序（分数越高越好）
            sorted_results = sorted(results, key=lambda x: self.get_score(x), reverse=True)
            
            for rank, result in enumerate(sorted_results, 1):
                item_id = result['id']
                
                # 计算RRF分数
                rrf_score = 1.0 / (self.k + rank)
                rrf_scores[item_id] += rrf_score
                
                # 记录排名详情
                original_score = self.get_score(result)
                rank_details[item_id].append((search_index, rank, original_score))
                
                # 保存完整的结果项（使用第一次出现的版本）
                if item_id not in all_items:
                    all_items[item_id] = result.copy()
                
                logger.debug(f"项目{item_id}: 搜索{search_index + 1}中排名{rank}, "
                           f"原始分数{original_score:.4f}, RRF分数{rrf_score:.4f}")
        
        # 创建最终结果
        final_results = []
        for item_id, total_rrf_score in rrf_scores.items():
            result = all_items[item_id].copy()
            
            # 添加RRF分数和详细信息
            result['rank_score'] = total_rrf_score

            # 🔧 修复：完全保留原始的distance和score，不做任何修改
            # RRF的作用仅仅是重新排序，不应该修改原始的相似度分数
            # 原始的score和distance字段保持不变，只添加rank_score用于排序
            
            # 添加排名详情到entity中
            if 'entity' not in result:
                result['entity'] = {}
            
            result['entity']['rrf_details'] = {
                'total_rrf_score': total_rrf_score,
                'k_parameter': self.k,
                'search_details': [
                    {
                        'search_index': search_idx,
                        'rank': rank,
                        'original_score': score,
                        'rrf_contribution': 1.0 / (self.k + rank)
                    }
                    for search_idx, rank, score in rank_details[item_id]
                ],
                'fusion_method': 'rrf'
            }
            
            final_results.append(result)
        
        # 按RRF分数降序排序
        final_results.sort(key=lambda x: x['rank_score'], reverse=True)

        # 🔧 添加排序验证日志
        if final_results:
            logger.debug(f"RRF排序结果验证:")
            for i, result in enumerate(final_results[:3]):  # 显示前3个结果
                rank_score = result.get('rank_score', 0)
                original_score = result.get('score', 'N/A')
                original_distance = result.get('distance', 'N/A')
                logger.debug(f"  排名{i+1}: rank_score={rank_score:.6f}, original_score={original_score}, original_distance={original_distance}")

        # 应用top_k限制
        if top_k is not None and top_k > 0:
            final_results = final_results[:top_k]

        logger.info(f"RRF排序完成，返回{len(final_results)}个结果")
        
        # 添加最终排名信息
        for final_rank, result in enumerate(final_results, 1):
            result['entity']['rrf_details']['final_rank'] = final_rank
        
        return final_results
    
    def get_rrf_score(self, rank: int) -> float:
        """
        计算单个排名的RRF分数
        
        Args:
            rank: 排名（从1开始）
            
        Returns:
            RRF分数
        """
        return 1.0 / (self.k + rank)
    
    def explain_ranking(self, results: List[Dict[str, Any]]) -> str:
        """
        解释RRF排序结果
        
        Args:
            results: RRF排序后的结果
            
        Returns:
            排序解释文本
        """
        explanation = [f"RRF排序解释 (k={self.k}):\n"]
        
        for i, result in enumerate(results[:5], 1):  # 只解释前5个
            rrf_details = result.get('entity', {}).get('rrf_details', {})
            total_score = rrf_details.get('total_rrf_score', 0)
            
            explanation.append(f"{i}. ID={result['id']}, 总RRF分数={total_score:.4f}")
            
            for detail in rrf_details.get('search_details', []):
                search_idx = detail['search_index']
                rank = detail['rank']
                contribution = detail['rrf_contribution']
                explanation.append(f"   搜索{search_idx + 1}: 排名{rank} -> RRF贡献{contribution:.4f}")
            
            explanation.append("")
        
        return "\n".join(explanation)
