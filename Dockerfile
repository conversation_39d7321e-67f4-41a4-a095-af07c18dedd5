# BaseImage
FROM python:3.12

# Environment variables
ENV SERVER_PORT=30351 \
    PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Use China mirror for pip (if needed)
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# Upgrade pip and install build tools first
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Copy only dependency files first (for better caching)
COPY pyproject.toml ./

# Install dependencies (this layer will be cached if dependencies don't change)
RUN pip install --no-cache-dir -e .

# Copy the rest of the project files
COPY . .

# Expose port
EXPOSE ${SERVER_PORT}

# Use shell form for better environment variable handling
SHELL ["/bin/bash", "-c"]

# Entry point with environment variable support
ENTRYPOINT python -u src/api/main.py
