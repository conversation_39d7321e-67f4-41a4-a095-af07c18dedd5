"""
Knowledge V2 API 通用依赖

提供CRUD客户端、验证函数和工具函数
"""

import time
import logging
from typing import Any, Dict, List, Optional, Tuple
from fastapi import HTTPException, Depends
from datetime import datetime

from service import get_client
from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
from modules.knowledge.metadata.crud_modules.crud_codes import MetadataCrudCodes
from modules.knowledge.metadata.crud_modules.crud_relations import MetadataCrudRelations
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.metadata.search import MetadataSearch
from ..models.request_models import BatchOperationConfig
from ..models.response_models import BatchOperationStats

logger = logging.getLogger(__name__)


# ==================== CRUD客户端依赖 ====================

async def get_metadata_crud() -> MetadataCrudMeta:
    """获取元数据CRUD客户端"""
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
        
        return MetadataCrudMeta(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取元数据CRUD客户端失败: {e}")
        raise HTTPException(status_code=500, detail="无法连接到元数据服务")


async def get_codes_crud() -> MetadataCrudCodes:
    """获取码值CRUD客户端"""
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
        
        return MetadataCrudCodes(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取码值CRUD客户端失败: {e}")
        raise HTTPException(status_code=500, detail="无法连接到码值服务")


async def get_relations_crud() -> MetadataCrudRelations:
    """获取关联CRUD客户端"""
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")

        return MetadataCrudRelations(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取关联CRUD客户端失败: {e}")
        raise HTTPException(status_code=500, detail="无法连接到关联服务")


async def get_dd_crud() -> DDCrud:
    """获取DD CRUD客户端"""
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")

        return DDCrud(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取DD CRUD客户端失败: {e}")
        raise HTTPException(status_code=500, detail="无法连接到DD服务")


async def get_metadata_search() -> MetadataSearch:
    """获取元数据搜索客户端"""
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")

        return MetadataSearch(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取元数据搜索客户端失败: {e}")
        raise HTTPException(status_code=500, detail="无法连接到搜索服务")


# ==================== 验证函数 ====================

def validate_knowledge_id(knowledge_id: str) -> str:
    """验证知识库ID"""
    if not knowledge_id or not knowledge_id.strip():
        raise HTTPException(status_code=400, detail="知识库ID不能为空")
    
    # 简单的UUID格式验证
    if len(knowledge_id) < 10:
        raise HTTPException(status_code=400, detail="知识库ID格式无效")
    
    return knowledge_id.strip()


def validate_batch_config(config: Optional[BatchOperationConfig]) -> BatchOperationConfig:
    """验证批量操作配置"""
    if config is None:
        return BatchOperationConfig()
    
    # 验证批次大小
    if config.batch_size > 1000:
        logger.warning(f"批次大小过大，调整为1000: {config.batch_size}")
        config.batch_size = 1000
    
    # 验证并发数
    if config.max_concurrency > 10:
        logger.warning(f"并发数过大，调整为10: {config.max_concurrency}")
        config.max_concurrency = 10
    
    # 验证超时时间
    if config.timeout_per_batch > 600:
        logger.warning(f"超时时间过长，调整为600秒: {config.timeout_per_batch}")
        config.timeout_per_batch = 600.0
    
    return config


# ==================== 工具函数 ====================

def create_batch_stats(
    total_requested: int,
    total_successful: int,
    execution_time: float,
    batch_count: int = 1,
    concurrency_used: int = 1
) -> BatchOperationStats:
    """创建批量操作统计信息"""
    return BatchOperationStats(
        total_requested=total_requested,
        total_successful=total_successful,
        total_failed=total_requested - total_successful,
        execution_time=execution_time,
        batch_count=batch_count,
        concurrency_used=concurrency_used
    )


def handle_batch_errors(errors: List[Exception]) -> List[Dict[str, Any]]:
    """处理批量操作错误"""
    error_details = []
    for i, error in enumerate(errors):
        error_details.append({
            "index": i,
            "error_type": type(error).__name__,
            "message": str(error),
            "timestamp": datetime.now().isoformat()
        })
    return error_details


def log_batch_operation(
    operation: str,
    entity_type: str,
    count: int,
    execution_time: float,
    success: bool = True
):
    """记录批量操作日志"""
    status = "成功" if success else "失败"
    logger.info(
        f"批量{operation} {entity_type}: "
        f"数量={count}, 耗时={execution_time:.2f}秒, 状态={status}"
    )


# ==================== 错误处理装饰器 ====================

def handle_api_errors(func):
    """API错误处理装饰器"""
    import functools

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"API操作失败: {func.__name__}, 错误: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"内部服务器错误: {str(e)}"
            )
    return wrapper


# ==================== 性能监控 ====================

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        execution_time = self.end_time - self.start_time
        
        if exc_type is None:
            logger.info(f"操作完成: {self.operation_name}, 耗时: {execution_time:.2f}秒")
        else:
            logger.error(f"操作失败: {self.operation_name}, 耗时: {execution_time:.2f}秒, 错误: {exc_val}")
    
    @property
    def execution_time(self) -> float:
        """获取执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


# ==================== 数据转换工具 ====================

def convert_to_dict_list(data: List[Any]) -> List[Dict[str, Any]]:
    """将数据转换为字典列表"""
    result = []
    for item in data:
        if hasattr(item, 'dict'):
            # Pydantic模型
            result.append(item.dict())
        elif isinstance(item, dict):
            result.append(item)
        else:
            # 其他类型，尝试转换为字典
            try:
                result.append(dict(item))
            except (TypeError, ValueError):
                result.append({"data": str(item)})
    return result


def extract_ids_from_response(response: Any) -> List[int]:
    """从响应中提取ID列表"""
    ids = []
    if hasattr(response, 'data') and response.data:
        for item in response.data:
            if isinstance(item, dict):
                # 尝试不同的ID字段名
                for id_field in ['id', 'db_id', 'table_id', 'column_id']:
                    if id_field in item:
                        ids.append(item[id_field])
                        break
    return ids
