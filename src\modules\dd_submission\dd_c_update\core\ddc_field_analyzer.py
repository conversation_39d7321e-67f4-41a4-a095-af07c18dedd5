"""
DD-C字段分析器 - 完全独立实现

基于正确的DD-C策略逻辑：
1. 3个轴心字段：SDR05/SDR08/SDR10 (单独修改触发Pipeline)
2. 简单更新字段：SDR06/SDR09等 (直接更新，无关联逻辑)  
3. 组合修改：同时修改多个轴心字段时直接更新，不走Pipeline
4. 映射方向：DD-C → DD-B (SDR字段更新对应的BDR字段)
"""

import json
import logging
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DDCUpdateStrategy(Enum):
    """DD-C更新策略枚举"""
    SIMPLE_UPDATE = "simple_update"                    # 简单字段更新
    SDR05_PIPELINE = "sdr05_pipeline"                  # SDR05单独修改
    SDR08_PIPELINE = "sdr08_pipeline"                  # SDR08单独修改
    SDR10_PIPELINE = "sdr10_pipeline"                  # SDR10单独修改
    SDR05_SDR08_COMPOSITE = "sdr05_sdr08_composite"    # SDR05+SDR08组合
    SDR08_SDR10_COMPOSITE = "sdr08_sdr10_composite"    # SDR08+SDR10组合
    SDR05_SDR10_COMPOSITE = "sdr05_sdr10_composite"    # SDR05+SDR10组合
    RANGE_AGGREGATION = "range_aggregation"            # Range聚合


@dataclass
class DDCPipelineParams:
    """DD-C Pipeline执行参数"""
    candidate_columns: Dict[str, List[str]]
    schema_generation_params: Dict[str, Any]
    user_question: str = ""
    hint: str = ""
    db_type: str = "mysql"
    pipeline_type: str = "sql_generation"  # sql_generation 或 sql_parser
    fixed_fields: Dict[str, Any] = None    # 需要固定的字段（不从Pipeline获取）


@dataclass
class DDCFieldAnalysisResult:
    """DD-C字段分析结果"""
    strategy: DDCUpdateStrategy
    pipeline_required: bool
    affected_fields: List[str]
    pipeline_params: Optional[DDCPipelineParams]
    reason: str
    requires_range_check: bool = False     # 是否需要Range检查
    override_fields: Dict[str, Any] = None # 需要覆盖Pipeline结果的字段


class DDCFieldAnalyzer:
    """DD-C字段分析器 - 完全独立实现"""
    
    # 3个轴心字段：单独修改触发Pipeline
    CORE_PIPELINE_FIELDS = {'SDR05', 'SDR08', 'SDR10'}
    
    # 简单更新字段：直接更新，无关联逻辑
    SIMPLE_UPDATE_FIELDS = {
        'SDR01', 'SDR02', 'SDR03', 'SDR04', 'SDR06', 'SDR07', 'SDR09', 
        'SDR11', 'SDR12', 'SDR13', 'SDR14', 'SDR15'
    }
    
    # 所有支持的SDR字段
    ALL_SDR_FIELDS = CORE_PIPELINE_FIELDS | SIMPLE_UPDATE_FIELDS
    
    def __init__(self):
        """初始化DD-C字段分析器"""
        logger.debug("DD-C字段分析器初始化完成")
        # 导入metadata查询工具
        try:
            from src.modules.metadata.metadata_query import MetadataQuery
            self.metadata_query = MetadataQuery()
        except ImportError:
            logger.warning("无法导入MetadataQuery，将使用默认中文名生成")
            self.metadata_query = None
    
    async def analyze_update_strategy(self, update_fields: Dict[str, Any], original_data: Dict[str, Any] = None) -> DDCFieldAnalysisResult:
        """
        分析更新策略
        
        Args:
            update_fields: 用户要更新的字段
            original_data: 原始记录数据
            
        Returns:
            字段分析结果
        """
        logger.debug(f"开始DD-C策略分析: update_fields={list(update_fields.keys())}")
        
        # 提取SDR字段（忽略大小写）
        sdr_fields = {}
        for field_name, field_value in update_fields.items():
            field_upper = field_name.upper()
            if field_upper in self.ALL_SDR_FIELDS:
                sdr_fields[field_upper] = field_value
        
        logger.debug(f"提取的SDR字段: {list(sdr_fields.keys())}")
        
        # 检查轴心字段
        core_fields = set(sdr_fields.keys()) & self.CORE_PIPELINE_FIELDS
        simple_fields = set(sdr_fields.keys()) & self.SIMPLE_UPDATE_FIELDS
        
        logger.debug(f"轴心字段: {core_fields}, 简单字段: {simple_fields}")
        
        # 策略判断逻辑
        if len(core_fields) == 1:
            # 单独修改一个轴心字段 → 触发Pipeline
            core_field = list(core_fields)[0]
            return await self._analyze_single_core_field(core_field, sdr_fields, original_data)

        elif len(core_fields) > 1:
            # 多个轴心字段修改 → 需要进一步判断
            return await self._analyze_composite_core_fields(core_fields, sdr_fields, original_data)

        else:
            # 只有简单字段 → 直接更新
            logger.debug("只有简单字段修改，使用简单更新策略")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason="只有简单字段修改，直接更新"
            )
    
    async def _analyze_composite_core_fields(self, core_fields: Set[str], sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析组合轴心字段修改

        Args:
            core_fields: 轴心字段集合
            sdr_fields: 所有SDR字段
            original_data: 原始记录数据

        Returns:
            字段分析结果
        """
        logger.debug(f"分析组合轴心字段: {core_fields}")

        # 检查是否有空值
        non_empty_fields = set()
        for field in core_fields:
            value = sdr_fields.get(field, '')
            if value and str(value).strip():
                non_empty_fields.add(field)

        logger.debug(f"非空轴心字段: {non_empty_fields}")

        # 如果所有字段都不为空，判断是否为3个字段的完整修改
        if len(non_empty_fields) == len(core_fields):
            if len(non_empty_fields) == 3:
                # 同时修改3个轴心字段且都不为空，复用各策略的保留字段生成逻辑
                logger.debug("同时修改3个轴心字段且都不为空，复用策略生成保留字段并集")

                sdr05_value = sdr_fields.get('SDR05', '')
                sdr08_value = sdr_fields.get('SDR08', '')
                sdr10_value = sdr_fields.get('SDR10', '')

                # 1. 复用SDR08策略的保留字段生成逻辑
                sdr08_fields = await self._get_sdr08_strategy_fields(sdr08_value)

                # 2. 复用SDR05策略的保留字段生成逻辑
                sdr05_fields = await self._get_sdr05_strategy_fields(sdr05_value)

                # 3. 按优先级合并字段：SDR05覆盖SDR08（SDR05优先）
                all_override_fields = self._merge_strategy_fields(sdr05_fields, sdr08_fields)

                # 4. 添加SDR10字段（不会与前面字段冲突）
                if sdr10_value:
                    all_override_fields['SDR10'] = sdr10_value

                return DDCFieldAnalysisResult(
                    strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                    pipeline_required=False,  # 不走Pipeline
                    affected_fields=list(all_override_fields.keys()),
                    pipeline_params=None,
                    reason=f"同时修改3个轴心字段且都不为空: {core_fields}，生成保留字段并集",
                    requires_range_check=True,  # 修改了SDR05/08/10中的组合，需要Range检查
                    override_fields=all_override_fields
                )
            # 如果是2个字段都不为空，继续走组合策略逻辑

        # 忽略空值，进入复合判断
        if len(non_empty_fields) == 1:
            # 只有一个字段不为空，按单个字段处理
            single_field = list(non_empty_fields)[0]
            logger.debug(f"只有一个轴心字段不为空: {single_field}")
            return await self._analyze_single_core_field(single_field, sdr_fields, original_data)
        elif non_empty_fields == {'SDR05', 'SDR08'}:
            return await self._analyze_sdr05_sdr08_composite(sdr_fields, original_data)
        elif non_empty_fields == {'SDR08', 'SDR10'}:
            return await self._analyze_sdr08_sdr10_composite(sdr_fields, original_data)
        elif non_empty_fields == {'SDR05', 'SDR10'}:
            return await self._analyze_sdr05_sdr10_composite(sdr_fields, original_data)
        else:
            # 其他组合，回退到简单更新
            logger.debug(f"未知组合 {non_empty_fields}，回退到简单更新")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"未知轴心字段组合: {non_empty_fields}，回退到简单更新"
            )

    async def _analyze_single_core_field(self, core_field: str, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析单个轴心字段修改
        
        Args:
            core_field: 轴心字段名 (SDR05/SDR08/SDR10)
            sdr_fields: 所有SDR字段
            original_data: 原始记录数据
            
        Returns:
            字段分析结果
        """
        logger.debug(f"分析单个轴心字段: {core_field}")
        
        if core_field == 'SDR05':
            return await self._analyze_sdr05_strategy(sdr_fields, original_data)
        elif core_field == 'SDR08':
            return await self._analyze_sdr08_strategy(sdr_fields, original_data)
        elif core_field == 'SDR10':
            return self._analyze_sdr10_strategy(sdr_fields, original_data)
        else:
            raise ValueError(f"未知的轴心字段: {core_field}")
    
    async def _analyze_sdr05_strategy(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR05策略（表英文名修改）
        生成保留字段SDR05，其他字段通过Pipeline生成

        Args:
            sdr_fields: SDR字段
            original_data: 原始记录数据

        Returns:
            字段分析结果
        """
        logger.debug("分析SDR05策略: 表英文名修改")

        sdr05_value = sdr_fields.get('SDR05', '')

        try:
            # 解析表列表
            if isinstance(sdr05_value, str):
                table_list = json.loads(sdr05_value)
            else:
                table_list = sdr05_value

            if not isinstance(table_list, list):
                raise ValueError("SDR05必须是表名列表")

            logger.debug(f"SDR05表列表: {table_list}")

            # 构建保留字段（SDR05策略需要预生成4个字段）
            # 1. SDR05 - 表英文名列表（用户输入）
            # 2. SDR06 - 表中文名列表（通过metadata查询生成）
            # 3. BDR09 - 同步SDR05
            # 4. BDR10 - 同步SDR06

            # 生成表中文名
            table_chinese_names = await self._generate_table_chinese_names(table_list)

            # SDR05策略保留字段（4个字段，BDR16由Pipeline生成）
            override_fields = {
                'SDR05': sdr05_value,  # 用户输入的表英文名
                'SDR06': json.dumps(table_chinese_names, ensure_ascii=False),  # 生成的表中文名
                'BDR09': sdr05_value,  # 同步SDR05
                'BDR10': json.dumps(table_chinese_names, ensure_ascii=False),  # 同步SDR06
            }

            # 构建Pipeline参数
            candidate_columns = {table: [] for table in table_list}  # 空列表表示包含所有字段

            pipeline_params = DDCPipelineParams(
                candidate_columns={},  # SDR05不需要candidate_columns
                schema_generation_params={
                    "table_names": table_list,  # 只提供table_names
                    "is_final": False,  # SDR05使用False
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="column_selection"  # SDR05应该使用字段选择Pipeline
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR05_PIPELINE,
                pipeline_required=True,
                affected_fields=['BDR09', 'BDR10', 'BDR11', 'BDR16', 'SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR10', 'SDR12'],  # 完整的12个字段
                pipeline_params=pipeline_params,
                reason="SDR05表英文名修改，需要Pipeline处理",
                requires_range_check=True,  # SDR05修改需要Range检查
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR05解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR05解析失败，回退到简单更新: {e}"
            )
    
    async def _analyze_sdr08_strategy(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR08策略（字段映射修改）
        生成保留字段SDR08，其他字段通过Pipeline生成

        Args:
            sdr_fields: SDR字段
            original_data: 原始记录数据

        Returns:
            字段分析结果
        """
        logger.debug("分析SDR08策略: 字段映射修改")

        sdr08_value = sdr_fields.get('SDR08', '')

        try:
            # 解析字段映射
            if isinstance(sdr08_value, str):
                field_mapping = json.loads(sdr08_value)
            else:
                field_mapping = sdr08_value

            if not isinstance(field_mapping, dict):
                raise ValueError("SDR08必须是字段映射字典")

            logger.debug(f"SDR08字段映射: {field_mapping}")

            # 构建保留字段（SDR08策略需要预生成7个字段）
            # 1. SDR08 - 字段映射（用户输入）
            # 2. SDR09 - 字段中文名映射（通过metadata查询生成）
            # 3. SDR05 - 表英文名列表（从字段映射提取）
            # 4. SDR06 - 表中文名列表（通过metadata查询生成）
            # 5. BDR09 - 同步SDR05
            # 6. BDR10 - 同步SDR06
            # 7. BDR11 - 同步SDR08

            # 从字段映射提取表名
            table_list = list(field_mapping.keys())

            # 生成表中文名
            table_chinese_names = await self._generate_table_chinese_names(table_list)

            # 生成字段中文名映射
            column_chinese_mapping = await self._generate_column_chinese_mapping(field_mapping)

            # SDR08策略保留字段（7个字段，BDR16由Pipeline生成）
            override_fields = {
                'SDR08': sdr08_value,  # 用户输入的字段映射
                'SDR09': json.dumps(column_chinese_mapping, ensure_ascii=False),  # 生成的字段中文名映射
                'SDR05': json.dumps(table_list, ensure_ascii=False),  # 提取的表英文名列表
                'SDR06': json.dumps(table_chinese_names, ensure_ascii=False),  # 生成的表中文名列表
                'BDR09': json.dumps(table_list, ensure_ascii=False),  # 同步SDR05
                'BDR10': json.dumps(table_chinese_names, ensure_ascii=False),  # 同步SDR06
                'BDR11': sdr08_value,  # 同步SDR08
            }

            # 构建Pipeline参数
            table_list = list(field_mapping.keys())

            pipeline_params = DDCPipelineParams(
                candidate_columns=field_mapping,
                schema_generation_params={
                    # SDR08只提供candidate_columns，不提供table_names避免冲突
                    "selected_tables": table_list,
                    "selected_columns": field_mapping,
                    "is_final": True,  # SDR08使用True
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="sql_generation"  # SDR08应该使用SQL生成Pipeline
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR08_PIPELINE,
                pipeline_required=True,
                affected_fields=['BDR09', 'BDR10', 'BDR11', 'BDR16', 'SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR10', 'SDR12'],  # 完整的12个字段
                pipeline_params=pipeline_params,
                reason="SDR08字段映射修改，需要Pipeline处理",
                requires_range_check=True,  # SDR08修改需要Range检查
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR08解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR08解析失败，回退到简单更新: {e}"
            )
    
    def _analyze_sdr10_strategy(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR10策略（SQL语句修改）
        生成保留字段SDR10，其他字段通过Pipeline生成

        Args:
            sdr_fields: SDR字段
            original_data: 原始记录数据

        Returns:
            字段分析结果
        """
        logger.debug("分析SDR10策略: SQL语句修改")

        sdr10_value = sdr_fields.get('SDR10', '')

        # 从SQL中提取表信息（简化处理）
        try:
            # 简化处理：从SQL中提取表名，为每个表提供空的字段列表
            # 这样Pipeline可以自动推断字段
            import re
            sql_text = str(sdr10_value).upper()

            # 简单的表名提取（匹配FROM和JOIN后的表名）
            table_pattern = r'(?:FROM|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*)'
            tables = re.findall(table_pattern, sql_text)

            # 为每个表创建空的字段列表，让Pipeline自动推断
            candidate_columns = {table.lower(): [] for table in tables} if tables else {"default_table": []}

            # 构建保留字段（SDR10固定值）
            override_fields = {
                'SDR10': sdr10_value,
                # 其他字段通过Pipeline生成
            }

            pipeline_params = DDCPipelineParams(
                candidate_columns={},  # SDR10不需要candidate_columns
                schema_generation_params={
                    "sql_candidates": [sdr10_value],  # SDR10使用sql_candidates
                    "is_final": False,
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="sql_parser"  # SDR10应该使用SQL解析Pipeline
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR10_PIPELINE,
                pipeline_required=True,
                affected_fields=['BDR09', 'BDR10', 'BDR11', 'BDR16', 'SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR10', 'SDR12'],  # 完整的12个字段
                pipeline_params=pipeline_params,
                reason="SDR10 SQL语句修改，需要Pipeline处理",
                requires_range_check=True,  # SDR10修改需要Range检查
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR10解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR10解析失败，回退到简单更新: {e}"
            )

    async def _get_sdr08_strategy_fields(self, sdr08_value: Any) -> Dict[str, str]:
        """
        复用SDR08策略的保留字段生成逻辑

        Returns:
            Dict[str, str]: SDR08策略生成的保留字段
        """
        try:
            # 解析SDR08字段映射
            if isinstance(sdr08_value, str):
                field_mapping = json.loads(sdr08_value)
            else:
                field_mapping = sdr08_value

            if not isinstance(field_mapping, dict):
                raise ValueError("SDR08必须是字段映射字典")

            # 从字段映射提取表名
            table_list = list(field_mapping.keys())

            # 生成表中文名
            table_chinese_names = await self._generate_table_chinese_names(table_list)

            # 生成字段中文名映射
            column_chinese_mapping = await self._generate_column_chinese_mapping(field_mapping)

            # SDR08策略保留字段（7个字段）
            return {
                'SDR08': sdr08_value,  # 用户输入的字段映射
                'SDR09': json.dumps(column_chinese_mapping, ensure_ascii=False),  # 生成的字段中文名映射
                'SDR05': json.dumps(table_list, ensure_ascii=False),  # 提取的表英文名列表
                'SDR06': json.dumps(table_chinese_names, ensure_ascii=False),  # 生成的表中文名列表
                'BDR09': json.dumps(table_list, ensure_ascii=False),  # 同步SDR05
                'BDR10': json.dumps(table_chinese_names, ensure_ascii=False),  # 同步SDR06
                'BDR11': sdr08_value,  # 同步SDR08
            }
        except Exception as e:
            logger.warning(f"SDR08策略字段生成失败: {e}")
            return {}

    async def _get_sdr05_strategy_fields(self, sdr05_value: Any) -> Dict[str, str]:
        """
        复用SDR05策略的保留字段生成逻辑

        Returns:
            Dict[str, str]: SDR05策略生成的保留字段
        """
        try:
            # 解析SDR05表列表
            if isinstance(sdr05_value, str):
                table_list = json.loads(sdr05_value)
            else:
                table_list = sdr05_value

            if not isinstance(table_list, list):
                raise ValueError("SDR05必须是表名列表")

            # 生成表中文名
            table_chinese_names = await self._generate_table_chinese_names(table_list)

            # SDR05策略保留字段（4个字段）
            return {
                'SDR05': sdr05_value,  # 用户输入的表列表
                'SDR06': json.dumps(table_chinese_names, ensure_ascii=False),  # 生成的表中文名列表
                'BDR09': sdr05_value,  # 同步SDR05
                'BDR10': json.dumps(table_chinese_names, ensure_ascii=False),  # 同步SDR06
            }
        except Exception as e:
            logger.warning(f"SDR05策略字段生成失败: {e}")
            return {}

    def _merge_strategy_fields(self, primary_fields: Dict[str, str], secondary_fields: Dict[str, str]) -> Dict[str, str]:
        """
        合并两个策略的保留字段

        Args:
            primary_fields: 主要策略字段（优先级高）
            secondary_fields: 次要策略字段（优先级低）

        Returns:
            Dict[str, str]: 合并后的字段（取并集，冲突时primary_fields优先）
        """
        merged_fields = secondary_fields.copy()
        merged_fields.update(primary_fields)  # primary_fields覆盖secondary_fields
        return merged_fields

    async def _analyze_sdr05_sdr08_composite(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR05+SDR08组合策略
        复用SDR08和SDR05策略的保留字段生成逻辑，合并规则：SDR05覆盖SDR08
        """
        logger.debug("分析SDR05+SDR08组合策略")

        sdr05_value = sdr_fields.get('SDR05', '')
        sdr08_value = sdr_fields.get('SDR08', '')

        try:
            # 1. 复用SDR08策略的保留字段生成逻辑
            sdr08_fields = await self._get_sdr08_strategy_fields(sdr08_value)

            # 2. 复用SDR05策略的保留字段生成逻辑
            sdr05_fields = await self._get_sdr05_strategy_fields(sdr05_value)

            # 3. 合并字段：SDR05覆盖SDR08（SDR05优先）
            override_fields = self._merge_strategy_fields(sdr05_fields, sdr08_fields)

            # 4. 解析SDR08字段映射用于Pipeline
            if isinstance(sdr08_value, str):
                field_mapping = json.loads(sdr08_value)
            else:
                field_mapping = sdr08_value

            if not isinstance(field_mapping, dict):
                raise ValueError("SDR08必须是字段映射字典")

            table_list = list(field_mapping.keys())

            # 5. 构建Pipeline参数（使用SQL生成Pipeline）
            pipeline_params = DDCPipelineParams(
                candidate_columns=field_mapping,
                schema_generation_params={
                    "selected_tables": table_list,
                    "selected_columns": field_mapping,
                    "is_final": True,
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="sql_generation"
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR05_SDR08_COMPOSITE,
                pipeline_required=True,
                affected_fields=list(override_fields.keys()) + ['BDR16'],  # 保留字段 + Pipeline生成的BDR16
                pipeline_params=pipeline_params,
                reason="SDR05+SDR08组合修改，复用两个策略的保留字段生成逻辑",
                requires_range_check=True,
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR05+SDR08组合解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR05+SDR08组合解析失败，回退到简单更新: {e}"
            )

    async def _analyze_sdr08_sdr10_composite(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR08+SDR10组合策略
        按SDR08内容直接生成保留字段，再走SQL_PARSER Pipeline，SDR08生成的字段覆盖Pipeline结果
        """
        logger.debug("分析SDR08+SDR10组合策略")

        sdr08_value = sdr_fields.get('SDR08', '')
        sdr10_value = sdr_fields.get('SDR10', '')

        try:
            # 1. 复用SDR08策略的保留字段生成逻辑
            sdr08_fields = await self._get_sdr08_strategy_fields(sdr08_value)

            # 2. 添加SDR10字段
            override_fields = sdr08_fields.copy()
            override_fields['SDR10'] = sdr10_value

            # 3. 解析SDR08字段映射用于Pipeline
            if isinstance(sdr08_value, str):
                field_mapping = json.loads(sdr08_value)
            else:
                field_mapping = sdr08_value

            if not isinstance(field_mapping, dict):
                raise ValueError("SDR08必须是字段映射字典")

            # 4. Pipeline只处理SDR10的SQL解析，不处理SDR08
            pipeline_params = DDCPipelineParams(
                candidate_columns=field_mapping,  # 提供从SDR08提取的表信息作为candidate_tables
                schema_generation_params={
                    "sql_candidates": [sdr10_value],  # 提供SDR10的SQL
                    "is_final": False,
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="sql_parser"  # 只用于解析SDR10的SQL
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR08_SDR10_COMPOSITE,
                pipeline_required=True,
                affected_fields=['SDR05', 'SDR06', 'SDR08', 'SDR09', 'SDR10', 'SDR12', 'BDR09', 'BDR10', 'BDR11', 'BDR16'],
                pipeline_params=pipeline_params,
                reason="SDR08+SDR10组合修改，按SDR08内容生成保留字段，覆盖Pipeline结果",
                requires_range_check=True,
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR08+SDR10组合解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR08+SDR10组合解析失败，回退到简单更新: {e}"
            )

    async def _analyze_sdr05_sdr10_composite(self, sdr_fields: Dict[str, Any], original_data: Dict[str, Any]) -> DDCFieldAnalysisResult:
        """
        分析SDR05+SDR10组合策略
        按SDR05内容直接生成保留字段，走SQL_PARSER Pipeline，SDR05生成的字段覆盖Pipeline结果
        """
        logger.debug("分析SDR05+SDR10组合策略")

        sdr05_value = sdr_fields.get('SDR05', '')
        sdr10_value = sdr_fields.get('SDR10', '')

        try:
            # 1. 复用SDR05策略的保留字段生成逻辑
            sdr05_fields = await self._get_sdr05_strategy_fields(sdr05_value)

            # 2. 添加SDR10字段
            override_fields = sdr05_fields.copy()
            override_fields['SDR10'] = sdr10_value

            # 3. 解析SDR05表列表用于Pipeline
            if isinstance(sdr05_value, str):
                table_list = json.loads(sdr05_value)
            else:
                table_list = sdr05_value

            if not isinstance(table_list, list):
                raise ValueError("SDR05必须是表名列表")

            # 4. Pipeline只处理SDR10的SQL解析，不处理SDR05
            # 从SDR05提取表信息作为candidate_tables
            candidate_tables = {table: [] for table in table_list}

            pipeline_params = DDCPipelineParams(
                candidate_columns=candidate_tables,  # 提供从SDR05提取的表信息
                schema_generation_params={
                    "sql_candidates": [sdr10_value],  # 提供SDR10的SQL
                    "is_final": False,
                    "source_type": "source"
                },
                user_question=original_data.get('dr09', '') if original_data else '',
                hint=original_data.get('dr17', '') if original_data else '',
                pipeline_type="sql_parser"  # 只用于解析SDR10的SQL
            )

            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SDR05_SDR10_COMPOSITE,
                pipeline_required=True,
                affected_fields=['SDR05', 'SDR06', 'SDR10', 'SDR12', 'BDR09', 'BDR10', 'BDR16'],
                pipeline_params=pipeline_params,
                reason="SDR05+SDR10组合修改，按SDR05内容生成保留字段，覆盖Pipeline结果",
                requires_range_check=True,
                override_fields=override_fields
            )

        except Exception as e:
            logger.warning(f"SDR05+SDR10组合解析失败: {e}")
            return DDCFieldAnalysisResult(
                strategy=DDCUpdateStrategy.SIMPLE_UPDATE,
                pipeline_required=False,
                affected_fields=list(sdr_fields.keys()),
                pipeline_params=None,
                reason=f"SDR05+SDR10组合解析失败，回退到简单更新: {e}"
            )

    async def _generate_table_chinese_names(self, table_list: List[str]) -> List[str]:
        """
        生成表中文名列表

        Args:
            table_list: 表英文名列表

        Returns:
            表中文名列表
        """
        try:
            if self.metadata_query:
                # 使用metadata查询获取真实的中文名
                chinese_names = []
                for table_name in table_list:
                    chinese_name = await self.metadata_query.get_table_chinese_name(table_name)
                    chinese_names.append(chinese_name or f"{table_name}_中文名")
                return chinese_names
            else:
                # 默认生成方式
                return [f"{table}_中文名" for table in table_list]
        except Exception as e:
            logger.warning(f"生成表中文名失败: {e}")
            return [f"{table}_中文名" for table in table_list]

    async def _generate_column_chinese_mapping(self, field_mapping: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """
        生成字段中文名映射

        Args:
            field_mapping: 字段映射 {table: [columns]}

        Returns:
            字段中文名映射 {table: [chinese_columns]}
        """
        try:
            if self.metadata_query:
                # 使用metadata查询获取真实的中文名
                chinese_mapping = {}
                for table_name, columns in field_mapping.items():
                    chinese_columns = []
                    for column in columns:
                        chinese_name = await self.metadata_query.get_column_chinese_name(table_name, column)
                        chinese_columns.append(chinese_name or f"{column}_中文名")
                    chinese_mapping[table_name] = chinese_columns
                return chinese_mapping
            else:
                # 默认生成方式
                return {
                    table: [f"{col}_中文名" for col in columns]
                    for table, columns in field_mapping.items()
                }
        except Exception as e:
            logger.warning(f"生成字段中文名映射失败: {e}")
            return {
                table: [f"{col}_中文名" for col in columns]
                for table, columns in field_mapping.items()
            }
