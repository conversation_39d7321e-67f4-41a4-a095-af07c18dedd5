"""
Vector模块数据访问层
"""

from typing import Any, Dict, List, Optional
import logging
import hashlib

from .entities import Embedding
from ..shared.constants import DDTableNames, DDConstants
from ..shared.exceptions import DDDatabaseError, DDVectorizationError, DDValidationError
from ..shared.utils import DDUtils

logger = logging.getLogger(__name__)


class VectorRepository:
    """Vector模块数据仓储 - 直接使用PGVector客户端"""

    def __init__(self, vdb_client: Any, embedding_client: Any = None):
        """
        初始化向量仓储

        Args:
            vdb_client: PGVector客户端实例
            embedding_client: 向量化模型客户端
        """
        self.vdb_client = vdb_client  # 直接使用PGVector客户端
        self.embedding_client = embedding_client
        self.collection_name = DDTableNames.VECTOR_EMBEDDINGS
    
    async def create_embedding_for_field(
        self,
        content: str,
        field_code: str,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int
    ) -> Dict[str, Any]:
        """
        为字段创建向量嵌入

        Args:
            submission_pk: dd_submission_data表的主键id，直接存储为向量表的data_row_id
        """
        # 清理文本
        clean_content = DDUtils.clean_text_for_vectorization(content)
        if not clean_content:
            raise DDValidationError(f"字段 {field_code} 的内容为空或无效")

        # 生成向量 - 必须是真实向量
        embedding_vector = await self._generate_embedding(clean_content)
        if not embedding_vector:
            raise DDVectorizationError(f"向量生成失败: {field_code}")

        # 构建向量数据
        embedding_data = {
            "embedding": embedding_vector,
            "knowledge_id": knowledge_id,
            "data_row_id": submission_pk,  # 直接使用dd_submission_data的主键
            "field_id": field_code,        # 直接使用字段名(dr09/dr17)
            "data_layer": data_layer,
            "is_latest": True
        }

        # 插入向量数据库
        vector_id = await self.insert_vector(embedding_data)

        return {
            "vector_id": vector_id,
            "data_row_id": submission_pk,  # 返回dd_submission_data的主键id
            "field_id": field_code,  # 使用与数据库表一致的字段名
            "embedding_dimension": len(embedding_vector)
            # 移除content字段：该字段在dd_embeddings表中不存在
            # content内容已经向量化存储在embedding字段中，不需要重复存储原文
        }
    
    async def search_similar_vectors(
        self,
        query_text: str,
        field_code: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            # 清理查询文本
            clean_query = DDUtils.clean_text_for_vectorization(query_text)
            if not clean_query:
                return []

            # 生成查询向量
            query_vector = await self._generate_embedding(clean_query)

            # 构建过滤条件
            filters = {
                "field_id": field_code,  # 直接使用字段名(dr09/dr17)
                # "is_latest": True
            }
            if knowledge_id:
                filters["knowledge_id"] = knowledge_id
            if data_layer:
                filters["data_layer"] = data_layer

            # 执行向量搜索 - 传递min_score到PGVector客户端进行SQL层面过滤
            results = await self._search_vectors(
                query_vector=query_vector,
                filters=filters,
                limit=limit,
                min_score=min_score  # 🔧 传递min_score参数
            )

            # 🔧 移除重复过滤：PGVector客户端已经在SQL层面处理了min_score过滤
            # 直接返回结果，保持向后兼容性
            logger.debug(f"VectorRepository搜索完成: 结果数={len(results)}")
            return results

        except Exception as e:
            logger.error(f"搜索相似向量失败: {e}")
            raise DDDatabaseError(f"搜索相似向量失败: {e}")

    async def batch_search_similar_vectors(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """
        批量搜索相似向量

        Args:
            search_requests: 搜索请求列表，每个请求包含：
                - query_text: 查询文本
                - field_code: 字段代码
                - knowledge_id: 知识库ID（可选）
                - data_layer: 数据层（可选）
                - limit: 结果数量限制（可选，默认10）
                - min_score: 最小分数（可选，默认0.5）

        Returns:
            List[List[Dict[str, Any]]]: 每个搜索请求对应的结果列表
        """
        if not search_requests:
            return []

        try:
            # 尝试使用批量向量搜索优化
            return await self._batch_search_optimized(search_requests)

        except Exception as e:
            logger.warning(f"批量向量搜索失败，降级到逐个搜索: {e}")
            # 降级到逐个搜索
            return await self._batch_search_fallback(search_requests)

    async def _batch_search_optimized(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """优化的批量向量搜索实现 - 带连接预热"""
        import asyncio

        # 🔧 移除连接预热逻辑 - 现在完全依赖asyncpg自己的连接池管理
        # asyncpg连接池会自动管理连接，无需手动预热
        logger.debug(f"开始批量搜索: {len(search_requests)}个请求，依赖asyncpg连接池自动管理")

        # 第一步：批量生成查询向量
        query_texts = []
        for request in search_requests:
            clean_query = DDUtils.clean_text_for_vectorization(request.get("query_text", ""))
            query_texts.append(clean_query if clean_query else "")

        # 批量生成嵌入向量
        query_vectors = await self._batch_generate_embeddings(query_texts)

        # 第二步：构建批量搜索任务
        search_tasks = []
        for i, request in enumerate(search_requests):
            if not query_texts[i]:  # 跳过空查询
                search_tasks.append(asyncio.create_task(self._empty_search_result()))
                continue

            # 构建过滤条件
            filters = {
                "field_id": request.get("field_code", "dr09")
            }
            if request.get("knowledge_id"):
                filters["knowledge_id"] = request["knowledge_id"]
            if request.get("data_layer"):
                filters["data_layer"] = request["data_layer"]

            # 创建搜索任务
            task = asyncio.create_task(self._search_vectors_with_filter(
                query_vector=query_vectors[i],
                filters=filters,
                limit=request.get("limit", 10),
                min_score=request.get("min_score", 0.5)
            ))
            search_tasks.append(task)

        # 第三步：并行执行所有搜索
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)

        # 第四步：处理结果
        final_results = []
        failed_count = 0
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.warning(f"批量搜索第{i}个请求失败: {result}")
                final_results.append([])
                failed_count += 1
            else:
                final_results.append(result)

        # 记录批量搜索完成后的连接池状态
        if hasattr(self.vdb_client, 'session_manager') and hasattr(self.vdb_client.session_manager, 'log_connection_pool_status'):
            self.vdb_client.session_manager.log_connection_pool_status(f"批量搜索完成后(成功:{len(search_requests)-failed_count}, 失败:{failed_count})")

        logger.debug(f"批量向量搜索完成: 处理{len(search_requests)}个请求，成功{len(search_requests)-failed_count}个，失败{failed_count}个")
        return final_results

    async def _batch_generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量生成嵌入向量"""
        try:
            if not self.embedding_client:
                raise DDVectorizationError("未配置embedding客户端，无法生成向量")

            # 过滤空文本
            valid_texts = [text if text else "empty" for text in texts]

            import asyncio
            result = await asyncio.wait_for(
                self.embedding_client.ainvoke(texts=valid_texts),
                timeout=60.0  # 批量操作增加超时时间
            )

            if not result.embeddings or len(result.embeddings) != len(texts):
                raise DDVectorizationError("批量embedding生成失败：返回结果数量不匹配")

            # 验证向量维度
            for i, embedding in enumerate(result.embeddings):
                if len(embedding) != DDConstants.VECTOR_DIMENSION:
                    raise DDVectorizationError(
                        f"向量{i}维度不匹配: 期望{DDConstants.VECTOR_DIMENSION}, 实际{len(embedding)}"
                    )

            return result.embeddings

        except asyncio.TimeoutError:
            raise DDVectorizationError("批量向量生成超时")
        except Exception as e:
            raise DDVectorizationError(f"批量向量生成失败: {e}")

    async def _search_vectors_with_filter(
        self,
        query_vector: List[float],
        filters: Dict[str, Any],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """带过滤条件的向量搜索"""
        try:
            results = await self._search_vectors(
                query_vector=query_vector,
                filters=filters,
                limit=limit,
                min_score=min_score  # 🔧 传递min_score参数
            )

            # 🔧 移除重复过滤：PGVector客户端已经在SQL层面处理了min_score过滤
            # 直接返回结果，保持向后兼容性
            return results

        except Exception as e:
            logger.warning(f"向量搜索失败: {e}")
            return []

    async def _empty_search_result(self) -> List[Dict[str, Any]]:
        """返回空搜索结果"""
        return []

    async def _batch_search_fallback(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """批量搜索的降级方案（逐个搜索）"""
        results = []

        for request in search_requests:
            try:
                single_result = await self.search_similar_vectors(
                    query_text=request.get("query_text", ""),
                    field_code=request.get("field_code", "dr09"),
                    knowledge_id=request.get("knowledge_id"),
                    data_layer=request.get("data_layer"),
                    limit=request.get("limit", 10),
                    min_score=request.get("min_score", 0.5)
                )
                results.append(single_result)
            except Exception as e:
                logger.warning(f"单个向量搜索失败: {e}")
                results.append([])

        return results
    
    async def delete_embeddings_by_submission(
        self,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int
    ) -> bool:
        """
        根据dd_submission_data主键删除所有相关向量

        Args:
            submission_pk: dd_submission_data表的主键id
        """
        try:
            # 🔧 使用PGVector客户端的adelete方法，更加标准化
            # 构建删除条件表达式
            expr = f"knowledge_id = '{knowledge_id}' AND data_layer = '{data_layer}' AND data_row_id = {submission_pk}"

            # 调用PGVector客户端的标准删除方法
            result = await self.vdb_client.adelete(
                collection_name=self.collection_name,
                expr=expr
            )

            deleted_count = result.get('delete_count', 0) if isinstance(result, dict) else 0
            logger.info(f"删除向量: knowledge_id={knowledge_id}, data_layer={data_layer}, submission_pk={submission_pk}, 删除数量={deleted_count}")
            return True

        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise DDDatabaseError(f"删除向量失败: {e}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量 - 公共接口"""
        return await self._generate_embedding(text)

    async def _generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量 - 必须是真实向量"""
        if not self.embedding_client:
            raise DDVectorizationError("未配置embedding客户端，无法生成向量")

        try:
            import asyncio
            result = await asyncio.wait_for(
                self.embedding_client.ainvoke(texts=[text]),
                timeout=30.0
            )

            if not result.embeddings or len(result.embeddings) == 0:
                raise DDVectorizationError("embedding客户端返回空结果")

            embedding = result.embeddings[0]

            # 验证向量维度
            if len(embedding) != DDConstants.VECTOR_DIMENSION:
                raise DDVectorizationError(
                    f"向量维度不匹配: 期望{DDConstants.VECTOR_DIMENSION}, 实际{len(embedding)}"
                )

            return embedding

        except asyncio.TimeoutError:
            raise DDVectorizationError("向量生成超时")
        except Exception as e:
            raise DDVectorizationError(f"向量生成失败: {e}")
    
    async def insert_vector(self, embedding_data: Dict[str, Any], enable_versioning: bool = True) -> Optional[Any]:
        """
        插入向量数据，支持版本控制

        Args:
            embedding_data: 向量数据
            enable_versioning: 是否启用版本控制

        Returns:
            插入记录的ID
        """
        try:
            knowledge_id = embedding_data.get("knowledge_id")
            data_layer = embedding_data.get("data_layer")
            field_id = embedding_data.get("field_id")
            embedding = embedding_data.get("embedding")
            data_row_id = embedding_data.get("data_row_id")

            if enable_versioning:
                # 🔧 版本控制：先删除旧版本，再插入新版本
                # 使用PGVector客户端的标准删除方法
                try:
                    old_version_expr = f"knowledge_id = '{knowledge_id}' AND data_layer = '{data_layer}' AND field_id = '{field_id}' AND data_row_id = {data_row_id} AND is_latest = true"
                    await self.vdb_client.adelete(
                        collection_name=self.collection_name,
                        expr=old_version_expr
                    )
                except Exception as e:
                    logger.debug(f"删除旧版本向量时出错（可能不存在）: {e}")

            # 🔧 使用PGVector客户端的标准插入方法
            insert_data = [{
                "embedding": embedding,
                "knowledge_id": knowledge_id,
                "data_row_id": data_row_id,
                "field_id": field_id,
                "data_layer": data_layer,
                "is_latest": True
            }]

            result = await self.vdb_client.ainsert(
                collection_name=self.collection_name,
                data=insert_data
            )

            # 返回插入的ID
            if result and result.get('ids'):
                return result['ids'][0]

            return None

        except Exception as e:
            logger.error(f"插入向量数据失败: {e}")
            raise DDDatabaseError(f"插入向量数据失败: {e}")

    async def _insert_vector(self, embedding_data: Dict[str, Any]) -> Optional[Any]:
        """私有方法：向后兼容，调用公共方法"""
        return await self.insert_vector(embedding_data, enable_versioning=True)

    async def batch_insert_vectors(
        self,
        embeddings_data: List[Dict[str, Any]],
        enable_versioning: bool = True,
        batch_size: int = 100
    ) -> List[Any]:
        """
        批量插入向量数据

        Args:
            embeddings_data: 向量数据列表
            enable_versioning: 是否启用版本控制
            batch_size: 每批处理的记录数，batch_size越小分批越多

        Returns:
            插入记录的ID列表
        """
        if not embeddings_data:
            return []

        results = []
        total_records = len(embeddings_data)

        # 🔧 batch_size越小，分批越多，并发粒度越细
        logger.info(f"开始批量插入向量: 总记录数={total_records}, batch_size={batch_size}, "
                   f"预计分批数={(total_records + batch_size - 1) // batch_size}")

        try:
            if enable_versioning:
                # 🔧 版本控制模式：UPDATE旧版本is_latest=false + INSERT新版本
                logger.info("启用版本控制，使用UPDATE+INSERT模式保留历史版本")

                # 第一步：批量UPDATE旧版本，设置is_latest=false（保留历史）
                update_requests = []
                for embedding_data in embeddings_data:
                    knowledge_id = embedding_data.get("knowledge_id")
                    data_layer = embedding_data.get("data_layer")
                    field_id = embedding_data.get("field_id")
                    data_row_id = embedding_data.get("data_row_id")

                    update_expr = f"knowledge_id = '{knowledge_id}' AND data_layer = '{data_layer}' AND field_id = '{field_id}' AND data_row_id = {data_row_id} AND is_latest = true"
                    update_requests.append({
                        "data": {"is_latest": False},
                        "expr": update_expr
                    })

                # 🔧 使用PGVector客户端的批量更新方法
                if update_requests:
                    try:
                        update_result = await self.vdb_client.abatch_update(
                            collection_name=self.collection_name,
                            updates=update_requests
                        )
                        logger.info(f"批量更新旧版本完成: 更新{update_result.get('update_count', 0)}条记录为非最新版本")
                    except Exception as e:
                        logger.warning(f"批量更新旧版本失败: {e}")

                # 第二步：批量插入新版本（按batch_size分批）
                for i in range(0, total_records, batch_size):
                    batch_data = embeddings_data[i:i + batch_size]
                    batch_num = i // batch_size + 1

                    try:
                        # 准备批量插入数据
                        insert_data = []
                        for embedding_data in batch_data:
                            insert_data.append({
                                "embedding": embedding_data.get("embedding"),
                                "knowledge_id": embedding_data.get("knowledge_id"),
                                "data_row_id": embedding_data.get("data_row_id"),
                                "field_id": embedding_data.get("field_id"),
                                "data_layer": embedding_data.get("data_layer"),
                                "is_latest": True
                            })

                        # 🔧 使用PGVector客户端的批量插入
                        result = await self.vdb_client.ainsert(
                            collection_name=self.collection_name,
                            data=insert_data
                        )

                        # 收集返回的ID
                        if result and result.get('ids'):
                            results.extend(result['ids'])

                        logger.debug(f"版本控制批次{batch_num}插入完成: {len(batch_data)}条记录")

                    except Exception as e:
                        logger.warning(f"版本控制批次{batch_num}插入失败: {e}")
                        # 继续处理下一批
            else:
                # 🔧 非版本控制模式：DELETE所有版本 + INSERT新版本（严格删除）
                logger.info("非版本控制模式，使用DELETE+INSERT模式（不保留历史）")

                # 第一步：批量删除所有版本（不只是is_latest=true）
                delete_exprs = []
                for embedding_data in embeddings_data:
                    knowledge_id = embedding_data.get("knowledge_id")
                    data_layer = embedding_data.get("data_layer")
                    field_id = embedding_data.get("field_id")
                    data_row_id = embedding_data.get("data_row_id")

                    # 删除所有版本，不限制is_latest
                    delete_expr = f"knowledge_id = '{knowledge_id}' AND data_layer = '{data_layer}' AND field_id = '{field_id}' AND data_row_id = {data_row_id}"
                    delete_exprs.append(delete_expr)

                # 🔧 使用PGVector客户端的批量删除方法
                if delete_exprs:
                    try:
                        delete_result = await self.vdb_client.abatch_delete(
                            collection_name=self.collection_name,
                            exprs=delete_exprs
                        )
                        logger.info(f"批量删除所有版本完成: 删除{delete_result.get('delete_count', 0)}条记录")
                    except Exception as e:
                        logger.warning(f"批量删除所有版本失败: {e}")

                # 第二步：批量插入新版本（按batch_size分批）
                for i in range(0, total_records, batch_size):
                    batch_data = embeddings_data[i:i + batch_size]
                    batch_num = i // batch_size + 1

                    try:
                        # 准备批量插入数据
                        insert_data = []
                        for embedding_data in batch_data:
                            insert_data.append({
                                "embedding": embedding_data.get("embedding"),
                                "knowledge_id": embedding_data.get("knowledge_id"),
                                "data_row_id": embedding_data.get("data_row_id"),
                                "field_id": embedding_data.get("field_id"),
                                "data_layer": embedding_data.get("data_layer"),
                                "is_latest": True
                            })

                        # 🔧 使用PGVector客户端的批量插入
                        result = await self.vdb_client.ainsert(
                            collection_name=self.collection_name,
                            data=insert_data
                        )

                        # 收集返回的ID
                        if result and result.get('ids'):
                            results.extend(result['ids'])

                        logger.debug(f"非版本控制批次{batch_num}插入完成: {len(batch_data)}条记录")

                    except Exception as e:
                        logger.warning(f"非版本控制批次{batch_num}插入失败: {e}")
                        # 继续处理下一批

        except Exception as e:
            logger.error(f"批量插入向量失败: {e}")
            raise DDDatabaseError(f"批量插入向量失败: {e}")

        logger.info(f"批量插入向量完成: 成功插入{len(results)}条记录")
        return results

    async def get_latest_vectors(
        self,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int,
        field_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取最新版本的向量数据

        Args:
            knowledge_id: 知识库ID
            data_layer: 数据层
            submission_pk: dd_submission_data表的主键id
            field_ids: 字段名列表(dr09/dr17)，为空则获取所有字段

        Returns:
            向量数据列表
        """
        try:
            # 🔧 使用PGVector客户端的标准查询方法
            # 构建查询表达式
            expr_parts = [
                f"knowledge_id = '{knowledge_id}'",
                f"data_layer = '{data_layer}'",
                f"data_row_id = {submission_pk}",
                "is_latest = true"
            ]

            if field_ids:
                field_list = "', '".join(field_ids)
                expr_parts.append(f"field_id IN ('{field_list}')")

            expr = " AND ".join(expr_parts)

            # 使用PGVector客户端的标准查询方法
            entities = await self.vdb_client.aquery(
                collection_name=self.collection_name,
                expr=expr,
                output_fields=["id", "embedding", "knowledge_id", "data_row_id", "field_id", "data_layer", "is_latest", "create_time", "update_time"]
            )

            # 转换Entity对象为字典
            results = []
            for entity in entities:
                if hasattr(entity, 'data') and entity.data:
                    results.append(entity.data)
                elif hasattr(entity, 'to_dict'):
                    results.append(entity.to_dict())
                else:
                    # 如果是字典格式，直接使用
                    results.append(entity if isinstance(entity, dict) else {})

            return results

        except Exception as e:
            logger.error(f"获取最新向量数据失败: {e}")
            raise DDDatabaseError(f"获取最新向量数据失败: {e}")
    
    async def _search_vectors(
        self,
        query_vector: List[float],
        filters: Dict[str, Any],
        limit: int = 10,
        min_score: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """搜索向量"""
        try:
            # 使用PGVectorClient的正确API
            # 构建过滤表达式
            expr_parts = []
            for key, value in filters.items():
                if isinstance(value, str):
                    expr_parts.append(f"{key} = '{value}'")
                else:
                    expr_parts.append(f"{key} = {value}")
            expr = " AND ".join(expr_parts) if expr_parts else None

            # 调用PGVectorClient的asearch方法
            results = await self.vdb_client.asearch(
                collection_name=self.collection_name,
                data=[query_vector],  # 注意：需要是列表的列表
                anns_field="embedding",  # 向量字段名
                param={"metric_type": "COSINE"},  # 搜索参数
                limit=limit,
                expr=expr,
                output_fields=["id", "knowledge_id", "data_row_id", "field_id", "data_layer"],
                min_score=min_score  # 🔧 传递min_score参数到PGVector客户端
            )

            # 处理搜索结果
            processed_results = []
            if results and len(results) > 0:
                # PGVectorClient返回的是List[List[SearchResult]]，取第一个查询的结果
                search_results = results[0] if results else []
                for result in search_results:
                    # SearchResult对象有id, distance, entity等属性
                    # 🔧 统一使用SearchResult.score属性，它会自动计算1.0-distance
                    # 这与ahybrid_search中的score设置保持一致
                    processed_result = {
                        "id": result.id,
                        "score": result.score,  # 使用SearchResult的score属性，确保一致性
                        "distance": result.distance,  # 保留原始距离，向后兼容
                        "knowledge_id": result.entity.get("knowledge_id") if result.entity else None,
                        "data_row_id": result.entity.get("data_row_id") if result.entity else None,
                        "field_id": result.entity.get("field_id") if result.entity else None,
                        "data_layer": result.entity.get("data_layer") if result.entity else None
                        # 移除content和source_id字段：这些字段在dd_embeddings表中不存在
                    }
                    processed_results.append(processed_result)

            return processed_results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {e}")
            raise DDDatabaseError(f"搜索向量失败: {e}")
    
    async def _delete_vectors(self, filters: Dict[str, Any]) -> bool:
        """删除向量"""
        try:
            # 构建删除条件表达式
            conditions = []
            for key, value in filters.items():
                if isinstance(value, str):
                    conditions.append(f"{key} = '{value}'")
                elif isinstance(value, bool):
                    conditions.append(f"{key} = {str(value).lower()}")
                else:
                    conditions.append(f"{key} = {value}")
            
            expr = " AND ".join(conditions)
            
            # 调用PGVectorClient的delete方法，需要expr参数
            result = await self.vdb_client.adelete(
                collection_name=self.collection_name,
                expr=expr
            )
            return bool(result)
            
        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise DDDatabaseError(f"删除向量失败: {e}")
