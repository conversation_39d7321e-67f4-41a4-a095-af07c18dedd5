"""
Knowledge V2 API 主路由

基于批量操作的高性能元数据管理API主入口
"""

from fastapi import APIRouter
from .routers import (
    databases_router,
    tables_router,
    columns_router,
    code_sets_router,
    relations_router,
    search_router,
    system_router
)
from .routers.dd import router as dd_router
from .routers.templates import router as templates_router

# 创建主路由器 - V2版本
router = APIRouter(prefix="/knowledge/v2", tags=["元数据管理V2"])

# 注册所有子路由
router.include_router(databases_router)
router.include_router(tables_router)
router.include_router(columns_router)
router.include_router(code_sets_router)
router.include_router(relations_router)
router.include_router(search_router)
router.include_router(system_router)
router.include_router(dd_router)  # DD数据管理路由
router.include_router(templates_router)  # 模板管理路由

# 健康检查端点
@router.get("/health", tags=["系统"])
async def health_check():
    """V2 API健康检查"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "message": "Knowledge V2 API is running with batch operations"
    }
