import sys
from pathlib import Path

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 初始化进阶企业模式日志配置
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    level="DEBUG",
    log_dir="./logs",
    log_filename="hsbc_knowledge_system.log",
    enable_file_logging=True,
    enable_console_logging=True,
    enable_json_format=True,
    enable_async=True
)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入所有API路由
# 1. DD-B增强处理API
from api.dd_submission.routers.dd_b_enhanced import router as dd_b_enhanced_router
from api.dd_submission.routers.dd_b_recommend import router as dd_b_recommend_router
from api.dd_submission.routers.dd_b_update import router as dd_b_update_router
from api.dd_submission.routers.dd_c_recommend import router as dd_c_recommend_router
from api.dd_submission.routers.dd_c_process import router as dd_c_process_router
from api.dd_submission.routers.dd_c_update import router as dd_c_update_router

# 2. 知识管理API
from api.knowledge.metadata.main import router as metadata_router
from api.knowledge.knowledge_base import router as knowledge_base_router
from api.knowledge.dd.main import router as dd_api_router

# 3. Knowledge V2 API (新增)
from api.knowledge_v2.main import router as knowledge_v2_router

# 导入dd_0-1的路由
from api.dd_tran_one.tran_one_api import router as tran_on_router

# 4. 外规内化API 731演示暂不引入
# from app.ier.api import router as ier_router

# 辅助编码/ETL推荐
from api.dd_sql_recommend.routers import router as dd_sql_recommend_router
# 义务解读，数据回写
from api.dd_submission.routers import duty_distribution_router, data_backfill_router

# 创建FastAPI应用
app = FastAPI(
    title="HSBC知识管理系统 - 统一服务",
    description="HSBC Knowledge Management System - Unified Service\n\n"
                "集成所有知识管理功能的统一API服务\n\n"
                "## 🚀 核心功能\n\n"
                "### 知识管理服务\n"
                "• **知识库管理**: 支持灵活的模型配置和多种知识库类型\n"
                "• **元数据管理**: 完整的数据库、表、字段元数据CRUD操作\n"
                "• **Knowledge V2**: 高性能批量操作的统一元数据管理API\n"
                "• **DD数据管理**: DD数据需求管理和智能搜索\n"
                "• **向量搜索**: 语义检索和企业级数据治理\n\n"
                "### DD-B增强处理\n"
                "• **智能字段填充**: 基于AI的BDR/SDR字段自动填充\n"
                "• **异步处理**: 后台异步处理，立即响应前端\n"
                "• **批量处理**: 支持大规模数据批量处理\n"
                "• **前端回调**: 处理完成后自动回调前端接口\n"
                "• **进度监控**: 实时处理进度和检查点机制\n\n"
                "## ⚡ 技术特性\n"
                "• **高性能**: Pipeline并发处理，批量操作优化\n"
                "• **高可靠**: 超时保护、错误恢复、断点续传\n"
                "• **智能化**: LLM驱动的表选择和字段映射\n"
                "• **标准化**: 完整的元数据管理和BDR字段输出\n"
                "• **兼容性**: V1和V2 API并行支持，平滑迁移\n\n"
                "✅ **生产就绪**: 经过充分测试，性能优化，稳定可靠",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议配置具体的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册所有API路由
# 1. 知识库管理API
app.include_router(knowledge_base_router, prefix="/api")

# 2. 元数据管理系统 (V1)
app.include_router(metadata_router, prefix="/api")

# 3. Knowledge V2 API (新增高性能批量操作API)
app.include_router(knowledge_v2_router, prefix="/api")

# 4. DD数据需求管理API
app.include_router(dd_api_router, prefix="/api")

# dd0-1需求管理API
app.include_router(tran_on_router, prefix="/api")

# 5. DD-B增强处理API
app.include_router(dd_b_enhanced_router, prefix="/api/dd")
app.include_router(dd_b_recommend_router, prefix="/api/dd")
app.include_router(dd_b_update_router, prefix="/api/dd")
app.include_router(dd_c_recommend_router, prefix="/api/dd")
app.include_router(dd_c_process_router, prefix="/api/dd")
app.include_router(dd_c_update_router, prefix="/api/dd")

# # 4. 外规内化API
# app.include_router(ier_router, prefix="/api")

# 义务解读，数据回写，ETL推荐
app.include_router(dd_sql_recommend_router)
app.include_router(duty_distribution_router)
app.include_router(data_backfill_router)

@app.get("/")
async def root():
    """服务根路径"""
    return {
        "service": "HSBC Knowledge Management System",
        "environment": "Production",
        "version": "2.0.0",
        "port": 30351,
        "status": "running",
        "docs": "/docs",
        "api_prefix": "/api",
        "features": [
            "知识库管理（支持灵活模型配置）",
            "元数据管理V1（传统CRUD操作）",
            "Knowledge V2（高性能批量操作）",
            "DD数据需求管理",
            "DD智能搜索（向量+文本）",
            "智能字段填充",
            "异步处理",
            "批量处理",
            "前端回调",
            "进度监控",
            "向量搜索和语义检索",
            "企业级数据治理"
        ],
        "endpoints": {
            # 知识库管理
            "knowledge_bases": "/api/knowledge/",
            "available_models": "/api/knowledge/models/available",

            # 元数据管理V1
            "metadata_v1_databases": "/api/knowledge/metadata/databases",
            "metadata_v1_tables": "/api/knowledge/metadata/tables",
            "metadata_v1_columns": "/api/knowledge/metadata/columns",
            "metadata_v1_health": "/api/knowledge/metadata/health",

            # Knowledge V2 (高性能批量操作)
            "knowledge_v2_databases": "/api/knowledge/v2/source-databases/",
            "knowledge_v2_tables": "/api/knowledge/v2/source-tables/",
            "knowledge_v2_columns": "/api/knowledge/v2/source-columns/",
            "knowledge_v2_code_sets": "/api/knowledge/v2/code-sets/",
            "knowledge_v2_health": "/api/knowledge/v2/health",

            # DD数据需求管理
            "dd_departments": "/api/knowledge/dd/departments/",
            "dd_submissions": "/api/knowledge/dd/submissions/",
            "dd_search": "/api/knowledge/dd/search/",
            "dd_health": "/api/knowledge/dd/health",

            # DD-B增强处理
            "dd_b_process": "/api/dd/dd-b/process",
            "dd_b_health": "/api/dd/dd-b/health",
            "dd_b_recommend": "/api/dd/dd-b/recommend",
            "dd_b_update": "/api/dd/dd-b/backfill",
            "dd_c_recommend": "/api/dd/dd-c/recommend",
            "dd_c_process": "/api/dd/dd-c/process",
            "callback_config": "/api/dd/dd-b/config/callback-url"
        },
        "callback_url": "http://218.78.129.173:30134/regulatory/release/ai/back/ddDataBizBackAI"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "HSBC Knowledge Management System",
        "version": "2.0.0",
        "timestamp": "2025-07-29",
        "components": {
            "knowledge_base": "healthy",
            "metadata_v1": "healthy",
            "knowledge_v2": "healthy",
            "dd_system": "healthy",
            "dd_b_processing": "healthy"
        }
    }


if __name__ == "__main__":
    import uvicorn
    print("🚀 启动HSBC知识管理系统统一服务")
    print("📍 端口: 30351")
    print("🌐 文档: http://localhost:30351/docs")
    print("✅ 环境: Production (生产)")
    print("💼 用途: 知识管理 + 元数据管理 + DD处理")
    print("🔧 功能: 知识库管理 + V1/V2 API + DD智能处理")
    print("📊 API版本:")
    print("   - 知识库管理: /api/knowledge/")
    print("   - 元数据V1: /api/knowledge/metadata/")
    print("   - Knowledge V2: /api/knowledge/v2/")
    print("   - DD系统: /api/knowledge/dd/")
    print("   - DD-B处理: /api/dd/")
    uvicorn.run(app, host="0.0.0.0", port=30351)
